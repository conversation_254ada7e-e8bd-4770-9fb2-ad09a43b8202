demo-rollup-readme.sh
*.db
/target
resources/dbs/
.idea/
target-path*

target/
fuzz/Cargo.lock

.DS_Store

demo_data/
resources/dbs/da-db/
resources/dbs/sequencer-db/
resources/dbs/full-node-db/
resources/dbs/batch-prover-db/
resources/dbs/light-client-prover-db/
resources/dbs/
resources/bitcoin/inscription_txs/
/.vscode/*

/docker/credentials/*
!/docker/credentials/bridge-0.addr
!/docker/credentials/bridge-0.key

adapters/solana/libyellowstone_grpc_geyser.dylib

reveal_*.tx

*.txs

elf/

# benchmarking related files
*input.bin
*transactions.txt
*profile.pb
*stats.txt

bitcoin-testnet4/
resources/dbs*