[submodule "crates/evm/src/evm/system_contracts/lib/forge-std"]
	path = crates/evm/src/evm/system_contracts/lib/forge-std
	url = https://github.com/foundry-rs/forge-std
[submodule "crates/evm/src/evm/system_contracts/lib/openzeppelin-contracts"]
	path = crates/evm/src/evm/system_contracts/lib/openzeppelin-contracts
	url = https://github.com/OpenZeppelin/openzeppelin-contracts
[submodule "crates/evm/src/evm/system_contracts/lib/openzeppelin-contracts-upgradeable"]
	path = crates/evm/src/evm/system_contracts/lib/openzeppelin-contracts-upgradeable
	url = https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable
[submodule "crates/evm/src/evm/system_contracts/lib/bitcoin-spv"]
	path = crates/evm/src/evm/system_contracts/lib/bitcoin-spv
	url = https://github.com/chainwayxyz/bitcoin-spv
