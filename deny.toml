[licenses]
allow = [
  "Apache-2.0",
  # "Apache-2.0 WITH LLVM-exception",
  "BSD-2-Clause",
  "BSD-3-Clause",
  "BSL-1.0",
  "CC0-1.0",
  "GPL-3.0",
  "ISC",
  # "LGPL-3.0",
  "MIT",
  "MITNFA",
  "MPL-2.0",
  "OpenSSL",
  "Unicode-3.0",
  # "Unicode-DFS-2016",
  # "Unlicense",
  "Zlib",
]

[[licenses.clarify]]
name = "ring"
# ring is derived from BoringSSL and has a bit of a special licensing situation,
# but we can effectively treat is as OpenSSL-like licensing.
expression = "OpenSSL"
license-files = [{ path = "LICENSE", hash = 0xbd0eed23 }]

[sources.allow-org]
github = [
  "paradigmxyz",
  # "alloy-rs",
  "chainwayxyz",
  "penumbra-zone",
]

[bans]
multiple-versions = "allow"
# We'd prefer to use Rustls instead of OpenSSL; this helps up from accidentally
# using OpenSSL. See <https://github.com/Sovereign-Labs/sovereign-sdk-wip/issues/352>.
deny = [{ name = "native-tls" }, { name = "openssl" }]

[advisories]
ignore = [
  "RUSTSEC-2024-0388", # Unmaintained derivative crate
  "RUSTSEC-2024-0370", # Unmaintained proc-macro-error crate
  "RUSTSEC-2024-0384", # Unmaintained instant crate
  "RUSTSEC-2024-0436", # Unmaintained paste crate
  "RUSTSEC-2025-0012", # Unmaintained backoff crate
]
