# Auditor's Guide for Citrea's System Contracts Folder

Hello there fellow security researcher, it's [<PERSON><PERSON>](https://x.com/okkotheja<PERSON>) here. I'm one of the two smart contract developers behind the system contracts folder and I'm also an auditor, so I wanted to write the document I always wished I had while auditing a project. I believe it is easier to obtain the initial context if someone who already has it transfers it to you, so please treat this as my humble attempt at transferring my context to you. But I must warn you: as I'm biased as the author of some of the following code that I will be explaining, it is highly probable that I have some blind spots and wrong assumptions. Thus I encourage you to challenge every statement in this document and see if you can break them.

If you are primarily a Solidity auditor, don't know <PERSON><PERSON>, or are not familiar with the details of core-level blockchain development, this guide is a good starting point for you. If you wish to gain a more formal background first, consult the documentation for `Bridge` and `BitcoinLightClient`, as this document is rather informal and does not aim to be an exhaustive explanation of the entire folder. Rather, it is more of a field guide where I share my personal insights and potential attack surfaces.

## Scope

Only `citrea/crates/evm/src/evm/system_contracts/*` is explained in this document. All of Citrea's critical Solidity code lives in this folder alongside the genesis script that sets up the initial state of the chain with predeployed contracts. I suggest reviewing this script after your first read-through the three system contract files:`BitcoinLightClient`, `Bridge`, and `FeeVault`.

## `BitcoinLightClient.sol`

### Overview

Our first stop is under `/src`. Thanks to this contract, applications on Citrea, including our `Bridge`, can know about Bitcoin's state. Through a system transaction, Citrea's system signer passes each Bitcoin block's blockhash, witness Merkle root and depth of the Merkle tree. It must be noted that this contract lags behind Bitcoin a certain number of blocks which is the finality depth. This is done as a reorg happening in Bitcoin would require a reorg to Citrea, resulting in chain halt. Thus, a configurable value specifies how many blocks behind Bitcoin the `BitcoinLightClient` and the rest of the system are tracking.

If you are only interested in reviewing the contracts, you can treat the system as a black box that only passes valid data from Bitcoin blocks. Breaking this assumption would create a serious issue, but that depends on core-level protocol code and is thus not relevant to this guide—please see the rest of the documentation for that information.

This contract inherits from `IBitcoinLightClient`, which is an interface that defines the external functions of `BitcoinLightClient`. I structured it this way because we wrote the interface first.

You may notice that the `SYSTEM_CALLER` is a constant with a repeating `0xdead` pattern. This is a privileged address in Citrea's execution, and transactions originating from this address do not decrease the address's balance for gas fees, though the spent gas still consumes the block gas limit. These transactions are called system transactions.

### `initializeBlockNumber`

The first one of these transactions is `initializeBlockNumber(uint256 _blockNumber)`. This function is only called once in the first Citrea block and never again. It sets the Bitcoin block height from which we will start tracking. It has the `onlySystem` modifier for correctness since we want this function to be called only by the system caller. But it would actually be fine if we didn't have the modifier. Why? Feel free to take a second to think about it.

An important insight here is that the native asset of Citrea, `cBTC`, is directly pegged to BTC. As such, no address except `Bridge` starts with a positive cBTC balance, and there is no way to create new `cBTC`. `Bridge` starts with 21 million cBTC balance, and this is distributed as a result of the `deposit` function, which I will explain in the next section. Thus, if no deposits have been processed yet, no transactions can be made to Citrea. That's why we needed the system transactions that do not consume any balance—they are required to bootstrap the system so that users can obtain a cBTC balance and start sending transactions. Returning to the question above, since any attacker would require a balance to call `initializeBlockNumber` before the system, this is impossible, and we would be fine without the modifier since it can only be called once and deposits cannot be processed without initializing `BitcoinLightClient`. But for completeness' sake, it is nice to have.

### `setBlockInfo`

`setBlockInfo(bytes32 _blockHash, bytes32 _witnessRoot, uint256 _coinbaseDepth)` is our second system transaction. You will notice that it does not have a block number parameter; this is because `BitcoinLightClient`'s block information is filled sequentially. Thus, Bitcoin block information cannot be overwritten and no block can be skipped, just as in Bitcoin itself when reorgs are disregarded. The block hash is likely familiar to people coming from EVM lands, but you may be wondering what `_witnessRoot` is if you are not a Bitcoin person.

Before talking about `_witnessRoot`, we need to know about the regular transactions root, which can be found in the header of any Bitcoin block. This is the root of the Merkle tree of transaction IDs, which are the hashes of raw Bitcoin transaction hex when only legacy fields are included, so version, inputs, outputs, and locktime. With this root, any Bitcoin transaction's inclusion in the block—in other words, whether that Bitcoin transaction happened or not—can be proven efficiently. 

Bitcoin's SegWit upgrade moved some of the data that would regularly be in input data to another field called witness, and this resulted in another type of transaction ID called the witness transaction ID. As you may expect, this is the hash of the full transaction hex including both the witness field and the legacy fields, and `_witnessRoot` is the root of the Merkle tree constructed from the witness transaction IDs of transactions. 

Transaction IDs are unique, so why did we choose the witness root instead of the regular root? Firstly, anyone who wishes to prove the inclusion of a transaction through its regular ID can do so by proving the full header, as the block hash is also written; this can be verified since the block hash is the hash of the header. But more importantly, the regular root gives us less information as we cannot know about the witness fields of the transactions. The Bridge needs to know about the witness field of the transactions it verifies, and there are cross-chain applications that live on Citrea that may need that information.

In addition to `_witnessRoot`, `_coinbaseDepth` is set in the `setBlockInfo` call. Each Bitcoin block starts with the coinbase transaction; this is the transaction through which the miner of the block sends themselves BTC (this is how BTC is created!). We pass the depth of this transaction in the Merkle tree of transactions, and then in Merkle proof verification, we assert that the length of the proof is equal to this depth to mitigate against [this issue](https://bitslog.com/2018/06/09/leaf-node-weakness-in-bitcoin-merkle-tree-design/#:~:text=Another%20way%20to,equal%20tree%20depths). This ensures that any claimed transactions are on the same level as the coinbase transaction, as they should be, preventing attackers from making fraudulent claims about non-existent transactions.

### Getters

The rest of the contract consists of getters and inclusion verification functions. About the getters, I only want to talk about the warnings in the comments above the contract. In Solidity, there is no null value as there is in some other languages. As such, null-zero confusions can occur frequently and can lead to bugs if an explicitly zero value is interpreted as null by a developer. 

We have a similar risk here. For regular transaction roots, a zero value as the root is not possible because even if there is only one transaction, the hash of it cannot be the zero value since the root will be that hash—unless you are really unlucky and the hash turns out to be zero. But by default, the witness transaction ID of coinbase transactions is zero, so if a block only has the coinbase transaction, its `witnessRoot` will be zero. Thus, if an integrator checks whether this root is not zero as a way of verifying that Citrea has knowledge of that block, it will fail because a zero value won't mean null in this case.

### Transaction inclusion verification

There are 3 different external Bitcoin transaction inclusion functions. Two of these prove inclusion by witness transaction ID, and as we store the witness root, we only need a Merkle proof. One of them specifies the block containing the claimed transaction by its hash, while the other uses the block height. The third one does the verification through the regular transaction ID, and as we don't store the transaction root directly, the caller needs to pass the header too. The header is hashed to see if it matches the stored block hash, then parsed to extract the transaction root. Parsing and Merkle proof verification is done by the `bitcoin-spv` library, which is not our work, yet is in the scope of the audit, so you are encouraged to review the library as well.

## `Bridge.sol`

### Overview

Next stop is the `Bridge`. Citrea uses `cBTC` as its native asset, which is bridged BTC. Our bridge is called [*Clementine*](https://github.com/chainwayxyz/clementine), and the Citrea end of the bridge is `Bridge.sol`. This contract is the only address that has a positive cBTC balance in the genesis state, and all cBTC that is in circulation comes from this contract. In genesis, it has a balance of 21 million cBTC; the reason for this is that BTC supply is capped at 21 million and cBTC has a 1:1 peg with BTC.

It inherits from `Ownable2StepUpgradeable` as all system contracts are upgradeable contracts using transparent proxies, and utilizes the libraries from `bitcoin-spv` and our `WitnessUtils.sol` for `bytes` types. All these libraries are for parsing and validation of Bitcoin-specific data structures and won't be explained separately here, but I suggest you review them thoroughly as any parsing or validation errors in the libraries can lead to potential critical vulnerabilities in our code.

### `initialize`

Our first function of interest is `initialize(bytes calldata _depositPrefix, bytes calldata _depositSuffix, uint256 _depositAmount)`. Like `setBlockInfo`, it is only called once and is called by the system caller during the bootstrapping phase of the whole chain in the first Citrea block. `depositPrefix` and `depositSuffix` are parts of a piece of Bitcoin script that is used to check the signatures of the N-of-N multisig, but for our purposes, assume it is a string that represents the deposit script with fixed parts at the beginning and end, with the middle part changing for each user. That middle part contains the EVM address of the cBTC recipient for a deposit action. 

While `_depositPrefix` and `_depositSuffix` have a setter, `_depositAmount` is only set here and does not have a setter. (In practice it can be changed as the contract is upgradeable, but we assume that this won't be necessary, hence there is no setter for it.) This is the fixed amount of BTC that can be deposited and withdrawn in a single action. This requirement is out of the ordinary for a bridge, yet it is a necessary evil that arises directly from the construction of Clementine. Due to this, arbitrary amounts cannot be bridged to and from Citrea using the canonical bridge, and we advise users to use third-party bridges. While the liquidity of these third-party bridges is sourced from the canonical bridge, end users do not suffer from the fixed amount requirement thanks to them.

### Prefix and suffix setters

After that, we have the setters for prefixes and suffixes of deposit and replace scripts. Parts of the deposit script have `onlyOwner` setters as Clementine's set of N-of-N multisig can be changed, resulting in a different signature check target which changes the deposit prefix, and the suffix can be changed for various reasons. Replace scripts have a similar case; they are used for replace deposit transactions, which are transactions that overwrite the information of older transactions on the Bridge contract. They are required in case there is a bug in the Clementine implementation and constants used are changed, in which case replace transactions overwrite the existing transactions. The prefix and suffix setters have the same purpose as the deposit script, as they also check an N-of-N multisig signature.

### `setFailedDepositVault`

`setFailedDepositVault(address _failedDepositVault)` changes the address of the contract where the cBTC from reverted native asset transfers are sent. So if the recipient of a `deposit` call is a smart contract (or a 7702-enabled EOA) and reverts on its fallback function, cBTC will be sent to this address. This address is initialized as `******************************************`, which is the predeploy address for the initial failed deposit vault. I will get back to predeploy addresses when we go through the genesis script.

### `deposit`

Now, we reach the `deposit(Transaction calldata moveTx, MerkleProof calldata proof, bytes32 shaScriptPubkeys)` function. It has the `onlySystemOrOperator` modifier because we want this function to be callable both as a system function and by a regular caller, which is the `operator`.

For details on `moveTx`, see Clementine docs or [whitepaper](https://citrea.xyz/clementine_whitepaper.pdf). But in summary, you send your `depositAmount` of BTC into a Bitcoin script where either the N-of-N can spend these or you can take them after X blocks have passed. If everything goes well, the N-of-N should spend this to move it to a script that only the N-of-N can spend these BTCs. As expected, this transaction is called the `moveTx`. The existence of this transaction shows us that the user sent their BTC on the Bitcoin side and now that BTC is locked in a way that only the N-of-N can spend, hence we are ready to send the user their deserved cBTC. But before sending the cBTC, we should make sure that the provided transaction is in valid formatting and indeed included in a Bitcoin block. `validateAndCheckInclusion` does that. Then we check if there is only one input, as the `moveTx` only takes as input the transaction output that the user sent to that Bitcoin script (which allows spending by the N-of-N or taking after X blocks). After that, we do a surprising thing. We check if the Schnorr signature provided in the `moveTx` belongs to the N-of-N. This looks redundant as this exact same signature check is done in Bitcoin itself. You couldn't have gotten this transaction included in a Bitcoin block without a valid signature, as the Bitcoin script used checks the signature.

### A critical vulnerability we found in `deposit`

We do this because we discovered an attack. We assume the passed `moveTx` is a [P2TR](https://learnmeabitcoin.com/technical/script/p2tr/) with script path spend type of Bitcoin transaction, which has the signature as its first witness item, the locking script as its second, and the control block as its third. But we are not actually asserting whether that transaction is indeed P2TR—what if it were another type, for example, P2WSH? 

In our usual flow, before we started doing the signature check, we skipped the first witness item as that is the signature and directly parsed the second one as that contained the deposit script, checking if it matched our stored prefixes and suffixes. So my concern was to see if there would be a way to provide a malicious `moveTx` that does not originate from the N-of-N but from an attacker, and pass the script matching checks by crafting the transaction so that the deposit script is still there as a string of bytes but interpreted as data instead of code. If such a transaction can be forged, the contract would be fooled into thinking that transaction is a valid deposit transaction and it would send cBTC but wouldn't receive BTC on Bitcoin. With this idea in mind, I found that we can create such a forged P2WSH transaction.

What do we need? The first witness item is skipped, so we don't need to concern ourselves with that. The second witness item needs to be `depositPrefix` + attacker's EVM address + `depositSuffix`. The third witness item can be anything too, but we will use this item to craft a valid P2WSH transaction. So what can we do? We can use the following locking script: `OP_2DROP OP_TRUE`. This just consumes two elements from the stack and then returns true. Since this just consumes elements from the stack and does not do any operations on them, we can put anything in place of these elements. We can put an arbitrary string into the first witness item and then put `depositPrefix` + attacker's EVM address + `depositSuffix` into the second one, and per P2WSH's standard, we will put the locking script we used into the third witness item. Thanks to these steps, we will have a forged `moveTx` that does not send any BTC anywhere, but from the `Bridge` contract's standpoint is a valid deposit action and thus it will send cBTC to the attacker's address. 

How can we mitigate this? One approach would be to look for P2TR indicators in the transaction and assert their existence. But we chose a stronger solution: why not check the multisig's signature ourselves in the contract so that whatever goes through, we are sure it was approved by the multisig? The only downside is added gas, but `deposit` is a system transaction so there is no balance decrease due to gas. This was the only critical issue found on system contracts to this date, and I believe you should ponder these types of code-data confusions and edge cases that can result from the nature of Bitcoin and our potentially false assumptions about it as you may find additional vulnerabilities similar to this one.

### More on `deposit` flow

OK, back to the `deposit` function. We verified the Schnorr signature, then we nullify the `moveTx` based on its transaction ID. Transaction IDs are unique, thus this should be a secure method of preventing double spending. Then we check how many witness items there are in the first witness field. The first witness field corresponds to the first input, and we only have one input, so it makes sense that we only care about that first witness field. Then this witness field should have 3 witness items: the first one is the signature coming from the N-of-N, the second one is the locking script for this leaf of Taproot, and the third one is the control block. This is in line with the P2TR specification, so we assert that there should be 3. Then we check that the second witness item starts with the deposit prefix and ends with the deposit suffix. Finally, we extract the EVM address from the middle part of the second witness and attempt to send the cBTC to it. If the attempt fails, we send the cBTC to the failed deposit vault. This concludes the `deposit` flow.

### `withdraw`

Next up, a group of `withdraw` functions. `withdraw(bytes32 txId, bytes4 outputId)` requires the fixed deposit amount of cBTC to be sent via the `withdraw` call, and the user specifies an `ANYONE_CAN_PAY` PSBT UTXO, which is like an intent where the user provides a dust input and in the output specifies the amount of BTC they want in return—the constant deposit amount in our case. As the sum of inputs is lower than the sum of outputs, this transaction cannot be broadcasted in this form and hence it is a **P**artially **S**igned **B**itcoin **T**ransaction. Then the withdraw flow is completed by the filling of this UTXO by the operator, and the user gets their BTC back. 

Please note that each UTXO in `withdrawalUTXOs` matches with a deposit action at the same index, so all withdrawals are matched with deposits in a way, but of course they are not associated with the same addresses as withdrawals can occur in any order. The complexity of `withdraw` is on the Clementine side rather than the `Bridge` contract, so this function is rather trivial. 

`batchWithdraw` is the batched version of this where multiples of the deposit amount can be withdrawn at once with multiple such PSBTs. `safeWithdraw` is the only scary-looking function here, but fear not—from a security standpoint, it is equivalent to `withdraw`. If you only consider the state changes, it only adds additional checks on top of `withdraw`, and since both functions are permissionless, `safeWithdraw` is not a target for an attacker. But why does it exist if it only does what `withdraw` does with much more work? We wanted to provide an option for users who would like to have ease of mind at the cost of gas by doing extra checks such as Bitcoin transaction validation and inclusion, and signature checks. Due to this, I won't go into more detail about this function, but that's the idea behind it.

### `replaceDeposit`

After that, we have `replaceDeposit(Transaction calldata replaceTx, MerkleProof calldata proof, uint256 idToReplace, bytes32 shaScriptPubkeys)`. You will notice that the body of this function looks really similar to `deposit`, and that is on purpose. `replaceDeposit` is used when there is a bug in Clementine and the constants used in Clementine need to be changed. In that case, we need to replace existing transaction information because if we don't do that, withdrawals cannot be processed. So to achieve that, a replace transaction is broadcast which includes a signature from the N-of-N just like deposit, and on the contract we check that signature and do string matching assertions like in `deposit`. After this, we change the deposit transaction ID at that index to the new one. Thanks to this, withdrawals can be processed.

This ends our tour of the `Bridge` contract. The rest of the functions are internal functions and they are self-explanatory.

## `FeeVault`s

All of the fee vault contracts inherit `FeeVault.sol` and don't do any modifications. Nevertheless, we separated them in case we want to change their logic specific to their use case. `FeeVault` accepts `cBTC` from any source with its empty `receive` function. It has a setter for `recipient`, which is the address where `withdraw` sends the balance of this contract, and a `minWithdraw` which specifies the minimum amount that can be withdrawn. This contract is simple enough, so I won't go into more detail.

## `WCBTC9.sol`

This is the regular `WETH9` with name and symbol change.

## Genesis scripts

Our final stop is the genesis scripts. The initial state of the chain is called the genesis state, and as mentioned before, Citrea's `Bridge` requires `BitcoinLightClient` to exist as a contract to process any deposits. But to deploy a contract, you need gas, and no one has gas to spend before the first deposit. Thus this set of system contracts needs to be put into the genesis state as predeploys. We used to do this manually, but each change to the contract code required modification of many genesis state files. With inspiration from Optimism, I wrote two scripts for this purpose. One is a Foundry script that does this state setting and exports a JSON of the state, and the other is a Python script that takes that JSON file and transforms it into another JSON that Citrea accepts.

`GenesisGenerator.s.sol` can set the genesis state for both production and testing purposes depending on whether `run` or `runProd` is called. For the audit, `runProd` is the important one. We read the addresses with authority such as the `feeVaultOwner` from the environment. After that, we give the predetermined `Bridge` proxy address 21 million cBTC. Then a `proxyAdmin` contract using OZ's library is created, and this contract is responsible for being the direct upgrader of each system contract, and if the upgradeability owner calls this contract with the new implementation of any system contract, the system contracts get upgraded. Then `setContracts` deploys each system contract to its predetermined address. `0x31...` is the namespace of proxies and `0x32...` is for implementations. `deployContract` deploys both the proxy and implementation for a particular system contract. This function is not used for `WCBTC` as that is not an upgradeable contract. Finally, we pass the dumped state in JSON format to the Python script, which does some formatting and adds additional information.