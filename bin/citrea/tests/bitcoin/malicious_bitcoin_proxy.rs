use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Duration;

use async_trait::async_trait;
use citrea_e2e::config::{BitcoinConfig, SequencerConfig, TestCaseConfig};
use citrea_e2e::framework::TestFramework;
use citrea_e2e::test_case::{<PERSON><PERSON>ase, TestCaseRunner};
use citrea_e2e::Result;
use hyper::service::{make_service_fn, service_fn};
use hyper::{Body, Client, Method, Request, Response, Server, StatusCode};
use reth_tasks::TaskExecutor;
use serde_json::{json, Value};
use tokio::sync::Mutex;
use tokio::time::sleep;

struct MaliciousBitcoinProxyTest {
    task_manager: TaskExecutor,
    proxy_port: u16,
    request_count: Arc<Mutex<u32>>,
}

impl MaliciousBitcoinProxyTest {
    fn new() -> Self {
        Self {
            task_manager: TaskExecutor::default(),
            proxy_port: 19443, // Different from default Bitcoin port
            request_count: Arc::new(Mutex::new(0)),
        }
    }

    async fn start_malicious_proxy(&self, real_bitcoin_url: String) -> Result<()> {
        let addr = SocketAddr::from(([127, 0, 0, 1], self.proxy_port));
        let request_count = self.request_count.clone();
        
        let make_svc = make_service_fn(move |_conn| {
            let real_bitcoin_url = real_bitcoin_url.clone();
            let request_count = request_count.clone();
            
            async move {
                Ok::<_, hyper::Error>(service_fn(move |req| {
                    handle_request(req, real_bitcoin_url.clone(), request_count.clone())
                }))
            }
        });

        let server = Server::bind(&addr).serve(make_svc);
        
        tokio::spawn(async move {
            if let Err(e) = server.await {
                eprintln!("Proxy server error: {}", e);
            }
        });

        // Wait for server to start
        sleep(Duration::from_millis(100)).await;
        Ok(())
    }
}

async fn handle_request(
    req: Request<Body>,
    real_bitcoin_url: String,
    request_count: Arc<Mutex<u32>>,
) -> Result<Response<Body>, hyper::Error> {
    let mut count = request_count.lock().await;
    *count += 1;
    let current_count = *count;
    drop(count);

    if req.method() != Method::POST {
        return Ok(Response::builder()
            .status(StatusCode::METHOD_NOT_ALLOWED)
            .body(Body::empty())
            .unwrap());
    }

    let body_bytes = hyper::body::to_bytes(req.into_body()).await?;
    let body_str = String::from_utf8_lossy(&body_bytes);
    
    // Parse the JSON-RPC request
    let request_json: Value = match serde_json::from_str(&body_str) {
        Ok(json) => json,
        Err(_) => {
            return Ok(Response::builder()
                .status(StatusCode::BAD_REQUEST)
                .body(Body::from("Invalid JSON"))
                .unwrap());
        }
    };

    let method = request_json["method"].as_str().unwrap_or("");
    
    // Intercept specific methods that the sequencer uses
    match method {
        "getblockhash" => {
            // Return valid block hash initially, then invalid after some requests
            if current_count > 3 {
                let malformed_response = json!({
                    "result": "invalid_block_hash_format_xyz123",
                    "error": null,
                    "id": request_json["id"]
                });
                return Ok(Response::new(Body::from(malformed_response.to_string())));
            }
        }
        "getblock" => {
            // Return malformed block data after a few requests
            if current_count > 5 {
                let malformed_block = json!({
                    "result": {
                        "hash": "0000000000000000000000000000000000000000000000000000000000000001",
                        "height": request_json["params"][1].as_u64().unwrap_or(100),
                        "tx": [
                            {
                                "hex": "invalid_hex_transaction_data_that_will_fail_parsing"
                            }
                        ],
                        "bits": "invalid_bits_format",
                        "merkleroot": "not_a_valid_merkle_root",
                        "nonce": "not_a_number",
                        "time": "not_a_timestamp",
                        "version": "not_a_version"
                    },
                    "error": null,
                    "id": request_json["id"]
                });
                return Ok(Response::new(Body::from(malformed_block.to_string())));
            }
        }
        _ => {
            // For other methods, proxy to real Bitcoin node
        }
    }

    // Forward request to real Bitcoin node
    let client = Client::new();
    let mut real_req = Request::builder()
        .method(Method::POST)
        .uri(&real_bitcoin_url)
        .header("content-type", "application/json")
        .body(Body::from(body_bytes))
        .unwrap();

    // Copy authorization header if present
    if let Some(auth) = req.headers().get("authorization") {
        real_req.headers_mut().insert("authorization", auth.clone());
    }

    match client.request(real_req).await {
        Ok(response) => {
            let status = response.status();
            let headers = response.headers().clone();
            let body = hyper::body::to_bytes(response.into_body()).await?;
            
            let mut resp_builder = Response::builder().status(status);
            for (key, value) in headers.iter() {
                resp_builder = resp_builder.header(key, value);
            }
            
            Ok(resp_builder.body(Body::from(body)).unwrap())
        }
        Err(_) => {
            Ok(Response::builder()
                .status(StatusCode::BAD_GATEWAY)
                .body(Body::from("Failed to connect to Bitcoin node"))
                .unwrap())
        }
    }
}

#[async_trait]
impl TestCase for MaliciousBitcoinProxyTest {
    fn test_config() -> TestCaseConfig {
        TestCaseConfig {
            with_sequencer: true,
            with_batch_prover: false,
            with_full_node: false,
            with_light_client_prover: false,
            ..Default::default()
        }
    }

    fn bitcoin_config() -> BitcoinConfig {
        BitcoinConfig {
            extra_args: vec!["-fallbackfee=0.00001"],
            ..Default::default()
        }
    }

    fn sequencer_config() -> SequencerConfig {
        SequencerConfig {
            block_production_interval_ms: 2000,
            da_update_interval_ms: 1000,
            ..Default::default()
        }
    }

    async fn cleanup(self) -> Result<()> {
        self.task_manager
            .graceful_shutdown_with_timeout(Duration::from_secs(1));
        Ok(())
    }

    async fn run_test(&mut self, f: &mut TestFramework) -> Result<()> {
        let da_node = f.bitcoin_nodes.get(0).expect("DA not running.");
        let sequencer = f.sequencer.as_ref().unwrap();

        // Start malicious proxy
        let real_bitcoin_url = format!(
            "http://127.0.0.1:{}/wallet/bitcoin",
            da_node.config.rpc_port
        );
        self.start_malicious_proxy(real_bitcoin_url).await?;

        // Generate initial blocks
        da_node.generate(3).await?;
        sleep(Duration::from_secs(2)).await;

        let initial_height = sequencer.client.get_head_soft_confirmation().await?;

        // Create L2 blocks to trigger DA activity
        sequencer.client.send_publish_batch_request().await?;
        sequencer.client.wait_for_l2_block(1, None).await?;

        // Generate more DA blocks to create a gap
        da_node.generate(5).await?;

        // Wait and observe if sequencer gets stuck
        let mut stuck_count = 0;
        for _ in 0..10 {
            sleep(Duration::from_secs(1)).await;
            
            match tokio::time::timeout(
                Duration::from_secs(2),
                sequencer.client.get_head_soft_confirmation()
            ).await {
                Ok(Ok(height)) => {
                    if height.number == initial_height.number {
                        stuck_count += 1;
                    }
                }
                _ => {
                    stuck_count += 1;
                }
            }
        }

        let request_count = *self.request_count.lock().await;
        
        // Test passes if sequencer gets stuck due to malicious responses
        assert!(stuck_count >= 5, "Sequencer should be stuck due to malicious Bitcoin responses");
        assert!(request_count > 5, "Proxy should have intercepted multiple requests");

        Ok(())
    }
}

#[tokio::test]
async fn test_malicious_bitcoin_proxy() -> Result<()> {
    TestCaseRunner::new(MaliciousBitcoinProxyTest::new())
        .run()
        .await
}
