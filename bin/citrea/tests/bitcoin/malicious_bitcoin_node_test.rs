use std::time::Duration;

use async_trait::async_trait;
use citrea_e2e::config::{BitcoinConfig, SequencerConfig, TestCaseConfig};
use citrea_e2e::framework::TestFramework;
use citrea_e2e::test_case::{<PERSON><PERSON><PERSON>, TestCaseRunner};
use citrea_e2e::Result;
use reth_tasks::TaskExecutor;
use tokio::time::{sleep, timeout};

/// Test case to demonstrate the malicious Bitcoin node vulnerability
///
/// This test shows how a malicious Bitcoin node can cause the sequencer to:
/// 1. Enter infinite retry loops when processing missed DA blocks
/// 2. Halt L2 block production network-wide
/// 3. Create denial-of-service conditions
struct MaliciousBitcoinNodeTest {
    task_manager: TaskExecutor,
}



#[async_trait]
impl TestCase for MaliciousBitcoinNodeTest {
    fn test_config() -> TestCaseConfig {
        TestCaseConfig {
            with_sequencer: true,
            with_batch_prover: false,
            with_full_node: false,
            with_light_client_prover: false,
            ..Default::default()
        }
    }

    fn bitcoin_config() -> BitcoinConfig {
        BitcoinConfig {
            extra_args: vec![
                "-persistmempool=0",
                "-walletbroadcast=0",
                "-fallbackfee=0.00001",
            ],
            ..Default::default()
        }
    }

    fn sequencer_config() -> SequencerConfig {
        SequencerConfig {
            block_production_interval_ms: 1000, // 1 second blocks
            da_update_interval_ms: 500,         // Check DA every 500ms
            ..Default::default()
        }
    }

    async fn cleanup(self) -> Result<()> {
        self.task_manager
            .graceful_shutdown_with_timeout(Duration::from_secs(1));
        Ok(())
    }

    async fn run_test(&mut self, f: &mut TestFramework) -> Result<()> {
        let da_node = f.bitcoin_nodes.get(0).expect("DA not running.");
        let sequencer = f.sequencer.as_ref().unwrap();

        // Establish normal operation
        da_node.generate(3).await?;
        sleep(Duration::from_secs(2)).await;

        let initial_height = sequencer.client.get_head_soft_confirmation().await?;

        // Create L2 blocks to trigger DA activity
        sequencer.client.send_publish_batch_request().await?;
        sequencer.client.wait_for_l2_block(1, None).await?;

        // Generate more DA blocks to create a gap that will trigger process_missed_da_blocks
        da_node.generate(5).await?;

        // Stop Bitcoin node to simulate malicious/unavailable DA layer
        da_node.stop().await?;

        // Wait and observe if sequencer gets stuck in retry loops
        let mut stuck_count = 0;
        let mut timeout_count = 0;

        for _ in 0..15 {
            sleep(Duration::from_secs(1)).await;

            match timeout(Duration::from_secs(2), sequencer.client.get_head_soft_confirmation()).await {
                Ok(Ok(height)) => {
                    if height.number == initial_height.number {
                        stuck_count += 1;
                    }
                }
                Ok(Err(_)) => {
                    stuck_count += 1;
                }
                Err(_) => {
                    timeout_count += 1;
                    stuck_count += 1;
                }
            }
        }

        // Restart Bitcoin node to test recovery
        da_node.start().await?;
        sleep(Duration::from_secs(3)).await;

        let final_height = match timeout(
            Duration::from_secs(5),
            sequencer.client.get_head_soft_confirmation()
        ).await {
            Ok(Ok(height)) => height.number,
            _ => initial_height.number,
        };

        // Verify the vulnerability was triggered
        assert!(stuck_count >= 10, "Sequencer should be stuck due to DA unavailability (stuck_count: {})", stuck_count);
        assert!(timeout_count >= 3, "Should have timeouts indicating retry loops (timeout_count: {})", timeout_count);

        // Verify recovery after Bitcoin node restart
        if final_height <= initial_height.number {
            // This indicates the sequencer is permanently stuck in retry loops
            panic!("Sequencer failed to recover even after Bitcoin node restart - infinite retry loop confirmed");
        }

        Ok(())
    }
}

#[tokio::test]
async fn test_malicious_bitcoin_node_vulnerability() -> Result<()> {
    let task_manager = TaskExecutor::default();

    TestCaseRunner::new(MaliciousBitcoinNodeTest {
        task_manager,
    })
    .run()
    .await
}
