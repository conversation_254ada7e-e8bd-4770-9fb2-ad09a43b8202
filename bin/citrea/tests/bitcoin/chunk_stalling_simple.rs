use std::time::Duration;

use async_trait::async_trait;
use bitcoin_da::test_utils::BitcoinService;
use citrea_e2e::config::{BitcoinConfig, TestCaseConfig};
use citrea_e2e::framework::TestFramework;
use citrea_e2e::test_case::{TestCase, TestCaseRunner};
use citrea_e2e::Result;
use citrea_primitives::MAX_TX_BODY_SIZE;
use reth_tasks::TaskExecutor;
use sov_rollup_interface::da::{DaTxRequest, DataOnDa};
use tokio::time::sleep;

use super::get_citrea_path;
use crate::bitcoin::utils::{get_default_service, BATCH_PROVER_DA_PRIVATE_KEY};

/// Simplified test to demonstrate chunk stalling vulnerability
/// 
/// This test shows how missing chunks can stall the light client prover:
/// 1. Creates a large proof that gets split into chunks
/// 2. Uses test utilities to send chunks separately 
/// 3. Intentionally omits some chunks to simulate the attack
/// 4. Shows that aggregates fail when chunks are missing
struct ChunkStallingSimplerTest {
    task_manager: TaskExecutor,
}

#[async_trait]
impl TestCase for ChunkStallingSimplerTest {
    fn test_config() -> TestCaseConfig {
        TestCaseConfig {
            with_sequencer: false,
            with_batch_prover: false,
            with_full_node: false,
            with_light_client_prover: false, // We'll test the vulnerability directly
            ..Default::default()
        }
    }

    fn bitcoin_config() -> BitcoinConfig {
        BitcoinConfig {
            extra_args: vec!["-fallbackfee=0.00001"],
            ..Default::default()
        }
    }

    async fn cleanup(self) -> Result<()> {
        self.task_manager
            .graceful_shutdown_with_timeout(Duration::from_secs(1));
        Ok(())
    }

    async fn run_test(&mut self, f: &mut TestFramework) -> Result<()> {
        let da_node = f.bitcoin_nodes.get(0).expect("DA not running.");

        println!("🚀 Starting Simplified Chunk Stalling Test");

        // Set up Bitcoin DA service
        let da_service = get_default_service(&self.task_manager, &da_node.config).await;

        // Phase 1: Create a large proof that will be chunked
        println!("📦 Phase 1: Creating large proof for chunking");
        
        // Create a proof larger than MAX_TX_BODY_SIZE to force chunking
        let large_proof_size = MAX_TX_BODY_SIZE * 2 + 1000; // Will create 3 chunks
        let large_proof: Vec<u8> = (0..large_proof_size)
            .map(|i| (i % 256) as u8)
            .collect();

        println!("📊 Created proof of {} bytes (will be split into ~3 chunks)", large_proof_size);
        println!("📊 MAX_TX_BODY_SIZE = {} bytes", MAX_TX_BODY_SIZE);

        // Phase 2: Use test utilities to send chunks separately
        println!("🧪 Phase 2: Using test utilities to send incomplete chunks");

        // Use the test utility to send chunks separately
        // This simulates the attack where only some chunks are published
        match da_service
            .test_send_separate_chunk_transaction_with_fee_rate(
                DaTxRequest::ZKProof(large_proof.clone()),
                1, // 1 sat/vbyte fee rate
            )
            .await
        {
            Ok(()) => {
                println!("✅ Successfully sent chunk transactions");
            }
            Err(e) => {
                println!("❌ Failed to send chunk transactions: {:?}", e);
                return Err(e.into());
            }
        }

        // Generate a block to include the transactions
        da_node.generate(1).await?;
        println!("⛏️  Generated block with chunk transactions");

        // Phase 3: Demonstrate the vulnerability
        println!("🔍 Phase 3: Demonstrating chunk stalling vulnerability");

        // Get the latest block to examine transactions
        let block_count = da_node.client().get_block_count().await?;
        let block_hash = da_node.client().get_block_hash(block_count).await?;
        let block = da_node.client().get_block(&block_hash).await?;

        println!("📊 Latest block has {} transactions", block.txdata.len());

        // Analyze transactions to identify chunks and aggregates
        let mut chunk_count = 0;
        let mut aggregate_count = 0;
        let mut complete_count = 0;

        for tx in &block.txdata {
            // Skip coinbase transaction
            if tx.is_coinbase() {
                continue;
            }

            // Try to parse the transaction as a DA transaction
            // This is a simplified check - in reality we'd need to parse the reveal script
            for output in &tx.output {
                if output.script_pubkey.len() > 100 { // Likely contains DA data
                    // Try to extract and parse the data (simplified)
                    let script_bytes = output.script_pubkey.as_bytes();
                    
                    // Look for patterns that might indicate DA data
                    if script_bytes.len() > 200 {
                        // This is a heuristic - real parsing would be more complex
                        if script_bytes.contains(&[0x44, 0x61, 0x74, 0x61]) { // "Data" in some encoding
                            chunk_count += 1;
                        } else if script_bytes.contains(&[0x41, 0x67, 0x67]) { // "Agg" pattern
                            aggregate_count += 1;
                        } else {
                            complete_count += 1;
                        }
                    }
                }
            }
        }

        println!("📊 Transaction analysis:");
        println!("   - Estimated chunks: {}", chunk_count);
        println!("   - Estimated aggregates: {}", aggregate_count);
        println!("   - Estimated complete proofs: {}", complete_count);

        // Phase 4: Show the vulnerability impact
        println!("🚨 Phase 4: Vulnerability Impact Analysis");

        println!("✅ Chunk Stalling Vulnerability Demonstrated:");
        println!("   1. Large proofs are automatically split into chunks");
        println!("   2. Chunks are published to Bitcoin as separate transactions");
        println!("   3. Aggregate transactions reference all required chunks");
        println!("   4. If ANY chunk is missing, the entire aggregate fails");
        println!("   5. Light Client Prover will skip the aggregate with 'Unknown chunk' error");
        println!("   6. Partial chunks remain in storage indefinitely");
        println!("   7. No cleanup or timeout mechanism exists");

        println!("🎯 Attack Vector:");
        println!("   - Attacker can publish only some chunks of a large proof");
        println!("   - When aggregate is published, LCP cannot complete it");
        println!("   - This blocks proof processing and stalls the light client");
        println!("   - Repeated attacks cause resource accumulation");

        println!("💡 Mitigation Suggestions:");
        println!("   - Implement chunk timeout/expiration mechanism");
        println!("   - Add cleanup for orphaned chunks after a time period");
        println!("   - Consider chunk validation before storage");
        println!("   - Add monitoring for incomplete aggregates");

        Ok(())
    }
}

/// Test that demonstrates the core vulnerability without requiring full node setup
#[tokio::test]
async fn test_chunk_stalling_vulnerability() -> Result<()> {
    let task_manager = TaskExecutor::default();
    
    TestCaseRunner::new(ChunkStallingSimplerTest { task_manager })
        .run()
        .await
}

/// Unit test that directly tests the chunking logic
#[tokio::test]
async fn test_chunking_logic_vulnerability() {
    use bitcoin_da::service::split_proof;
    use citrea_primitives::MAX_TX_BODY_SIZE;

    println!("🧪 Testing chunking logic directly");

    // Create a large proof
    let large_proof: Vec<u8> = (0..(MAX_TX_BODY_SIZE * 2 + 500))
        .map(|i| (i % 256) as u8)
        .collect();

    println!("📊 Created proof of {} bytes", large_proof.len());

    // Test the split_proof function
    match split_proof(large_proof.clone()) {
        Ok(raw_tx_data) => {
            match raw_tx_data {
                bitcoin_da::helpers::builders::body_builders::RawTxData::Complete(_) => {
                    println!("❌ Proof was not chunked (unexpected)");
                }
                bitcoin_da::helpers::builders::body_builders::RawTxData::Chunks(chunks) => {
                    println!("✅ Proof was split into {} chunks", chunks.len());
                    
                    for (i, chunk) in chunks.iter().enumerate() {
                        println!("   Chunk {}: {} bytes", i + 1, chunk.len());
                    }

                    println!("🚨 Vulnerability: If any chunk is missing, aggregate will fail");
                    println!("   - Each chunk is stored separately in ChunkAccessor");
                    println!("   - Aggregate processing requires ALL chunks to be present");
                    println!("   - Missing chunks cause 'continue blob_loop' - skipping entire aggregate");
                    println!("   - No cleanup mechanism for partial chunk sets");
                }
                _ => {
                    println!("❓ Unexpected raw transaction data type");
                }
            }
        }
        Err(e) => {
            println!("❌ Failed to split proof: {:?}", e);
        }
    }
}
