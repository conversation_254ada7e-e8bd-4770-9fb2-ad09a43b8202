use std::time::Duration;

use async_trait::async_trait;
use citrea_e2e::config::{BitcoinConfig, SequencerConfig, TestCaseConfig};
use citrea_e2e::framework::TestFramework;
use citrea_e2e::test_case::{<PERSON><PERSON><PERSON>, TestCaseRunner};
use citrea_e2e::Result;
use reth_tasks::TaskExecutor;
use tokio::time::{sleep, timeout};

struct SequencerRetryLoopTest {
    task_manager: TaskExecutor,
}

#[async_trait]
impl TestCase for SequencerRetryLoopTest {
    fn test_config() -> TestCaseConfig {
        TestCaseConfig {
            with_sequencer: true,
            with_batch_prover: false,
            with_full_node: false,
            with_light_client_prover: false,
            ..Default::default()
        }
    }

    fn bitcoin_config() -> BitcoinConfig {
        BitcoinConfig {
            extra_args: vec!["-fallbackfee=0.00001"],
            ..Default::default()
        }
    }

    fn sequencer_config() -> SequencerConfig {
        SequencerConfig {
            block_production_interval_ms: 2000,
            da_update_interval_ms: 1000,
            ..Default::default()
        }
    }

    async fn cleanup(self) -> Result<()> {
        self.task_manager
            .graceful_shutdown_with_timeout(Duration::from_secs(1));
        Ok(())
    }

    async fn run_test(&mut self, f: &mut TestFramework) -> Result<()> {
        let da_node = f.bitcoin_nodes.get(0).expect("DA not running.");
        let sequencer = f.sequencer.as_ref().unwrap();

        // Generate initial blocks
        da_node.generate(3).await?;
        sleep(Duration::from_secs(2)).await;

        let initial_height = sequencer.client.get_head_soft_confirmation().await?;

        // Create L2 activity
        sequencer.client.send_publish_batch_request().await?;
        sequencer.client.wait_for_l2_block(1, None).await?;

        // Generate DA blocks to create gap
        da_node.generate(5).await?;
        
        // Stop Bitcoin node to trigger retry loops in process_missed_da_blocks
        da_node.stop().await?;

        // Monitor for stuck behavior
        let mut consecutive_stuck = 0;
        let mut total_timeouts = 0;
        
        for i in 0..20 {
            sleep(Duration::from_millis(500)).await;
            
            match timeout(Duration::from_secs(1), sequencer.client.get_head_soft_confirmation()).await {
                Ok(Ok(height)) => {
                    if height.number == initial_height.number {
                        consecutive_stuck += 1;
                    } else {
                        consecutive_stuck = 0;
                    }
                }
                Ok(Err(_)) => {
                    consecutive_stuck += 1;
                }
                Err(_) => {
                    total_timeouts += 1;
                    consecutive_stuck += 1;
                }
            }
            
            // If stuck for 5+ consecutive checks, the vulnerability is triggered
            if consecutive_stuck >= 5 && i >= 5 {
                break;
            }
        }

        // Restart Bitcoin node
        da_node.start().await?;
        sleep(Duration::from_secs(2)).await;

        // Check if sequencer recovers
        let recovery_height = match timeout(
            Duration::from_secs(3),
            sequencer.client.get_head_soft_confirmation()
        ).await {
            Ok(Ok(height)) => height.number,
            _ => initial_height.number,
        };

        // Assertions to verify the vulnerability
        assert!(consecutive_stuck >= 5, 
               "Sequencer should get stuck when Bitcoin node is unavailable (stuck: {})", 
               consecutive_stuck);
        
        assert!(total_timeouts >= 2, 
               "Should have timeouts indicating retry loops (timeouts: {})", 
               total_timeouts);

        // If sequencer doesn't recover, it's permanently stuck in retry loops
        if recovery_height <= initial_height.number {
            panic!("VULNERABILITY CONFIRMED: Sequencer stuck in infinite retry loop even after Bitcoin node recovery");
        }

        Ok(())
    }
}

#[tokio::test]
async fn test_sequencer_retry_loop_vulnerability() -> Result<()> {
    let task_manager = TaskExecutor::default();
    
    TestCaseRunner::new(SequencerRetryLoopTest { task_manager })
        .run()
        .await
}
