/// Tests for closing down and reopening nodes with the same data
/// to make sure the nodes can continue from where they left off
/// In the past we had problems with this scenario.
use std::str::FromStr;
use std::time::Duration;

use alloy_primitives::Address;
use alloy_rpc_types::BlockNumberOrTag;
use citrea_common::{BatchProverConfig, SequencerConfig};
use citrea_stf::genesis_config::GenesisPaths;
use sov_db::ledger_db::migrations::copy_db_dir_recursive;
use sov_mock_da::{<PERSON><PERSON><PERSON><PERSON><PERSON>, MockDaService};
use tokio::time::sleep;

use super::init_test_rollup;
use crate::common::helpers::{
    create_default_rollup_config, start_rollup, tempdir_with_children, wait_for_l1_block,
    wait_for_l2_block, wait_for_prover_job, wait_for_prover_job_count, NodeMode,
};
use crate::common::{make_test_client, TEST_DATA_GENESIS_PATH};

#[tokio::test(flavor = "multi_thread")]
async fn test_reopen_full_node() -> Result<(), anyhow::Error> {
    // citrea::initialize_logging(tracing::Level::INFO);
    let storage_dir = tempdir_with_children(&["DA", "sequencer", "full-node"]);
    let da_db_dir = storage_dir.path().join("DA").to_path_buf();
    let sequencer_db_dir = storage_dir.path().join("sequencer").to_path_buf();
    let fullnode_db_dir = storage_dir.path().join("full-node").to_path_buf();

    let (seq_port_tx, seq_port_rx) = tokio::sync::oneshot::channel();

    let rollup_config = create_default_rollup_config(
        true,
        &sequencer_db_dir,
        &da_db_dir,
        NodeMode::SequencerNode,
        None,
    );
    let sequencer_config = SequencerConfig::default();
    let seq_task = start_rollup(
        seq_port_tx,
        GenesisPaths::from_dir(TEST_DATA_GENESIS_PATH),
        None,
        None,
        rollup_config,
        Some(sequencer_config),
        None,
        false,
    )
    .await;

    let seq_port = seq_port_rx.await.unwrap();

    let (full_node_port_tx, full_node_port_rx) = tokio::sync::oneshot::channel();

    let rollup_config = create_default_rollup_config(
        true,
        &fullnode_db_dir,
        &da_db_dir,
        NodeMode::FullNode(seq_port),
        None,
    );
    // starting full node with db path
    let rollup_task = start_rollup(
        full_node_port_tx,
        GenesisPaths::from_dir(TEST_DATA_GENESIS_PATH),
        None,
        None,
        rollup_config,
        None,
        None,
        false,
    )
    .await;

    let full_node_port = full_node_port_rx.await.unwrap();

    let seq_test_client = init_test_rollup(seq_port).await;
    let full_node_test_client = init_test_rollup(full_node_port).await;

    let addr = Address::from_str("******************************************").unwrap();

    // create 10 blocks
    for _ in 0..10 {
        let _pending = seq_test_client
            .send_eth(addr, None, None, None, 0u128)
            .await
            .unwrap();
        seq_test_client.send_publish_batch_request().await;
    }

    // wait for full node to sync
    wait_for_l2_block(&full_node_test_client, 10, None).await;

    // check if latest blocks are the same
    let seq_last_block = seq_test_client
        .eth_get_block_by_number_with_detail(Some(BlockNumberOrTag::Latest))
        .await;

    let full_node_last_block = full_node_test_client
        .eth_get_block_by_number_with_detail(Some(BlockNumberOrTag::Latest))
        .await;

    assert_eq!(seq_last_block.header.number, 10);
    assert_eq!(full_node_last_block.header.number, 10);

    assert_eq!(
        seq_last_block.header.state_root,
        full_node_last_block.header.state_root
    );
    assert_eq!(seq_last_block.header.hash, full_node_last_block.header.hash);

    // close full node
    rollup_task.graceful_shutdown();

    // create 100 more blocks
    for _ in 0..100 {
        let _pending = seq_test_client
            .send_eth(addr, None, None, None, 0u128)
            .await
            .unwrap();
        seq_test_client.send_publish_batch_request().await;
    }

    let da_service = MockDaService::new(MockAddress::from([0; 32]), &da_db_dir);
    da_service.publish_test_block().await.unwrap();

    // start full node again
    let (full_node_port_tx, full_node_port_rx) = tokio::sync::oneshot::channel();

    // Copy the db to a new path with the same contents because
    // the lock is not released on the db directory even though the task is aborted
    let _ = copy_db_dir_recursive(&fullnode_db_dir, &storage_dir.path().join("fullnode_copy"));

    let fullnode_db_dir = storage_dir.path().join("fullnode_copy");

    let rollup_config = create_default_rollup_config(
        true,
        &fullnode_db_dir,
        &da_db_dir,
        NodeMode::FullNode(seq_port),
        None,
    );
    // spin up the full node again with the same data where it left of only with different path to not stuck on lock
    let rollup_task = start_rollup(
        full_node_port_tx,
        GenesisPaths::from_dir(TEST_DATA_GENESIS_PATH),
        None,
        None,
        rollup_config,
        None,
        None,
        false,
    )
    .await;

    let full_node_port = full_node_port_rx.await.unwrap();

    let full_node_test_client = make_test_client(full_node_port).await?;

    wait_for_l2_block(&seq_test_client, 110, None).await;
    wait_for_l2_block(&full_node_test_client, 110, None).await;

    // wait just a bit to ensure l2 blocks generated due to missed DA blocks are caught up as well
    tokio::time::sleep(Duration::from_secs(1)).await;

    // check if the latest block state roots are same
    let seq_last_block = seq_test_client
        .eth_get_block_by_number_with_detail(Some(BlockNumberOrTag::Latest))
        .await;

    let full_node_last_block = full_node_test_client
        .eth_get_block_by_number_with_detail(Some(BlockNumberOrTag::Latest))
        .await;

    assert_eq!(
        seq_last_block.header.number,
        full_node_last_block.header.number
    );
    assert_eq!(
        seq_last_block.header.state_root,
        full_node_last_block.header.state_root
    );
    assert_eq!(seq_last_block.header.hash, full_node_last_block.header.hash);

    seq_task.graceful_shutdown();
    rollup_task.graceful_shutdown();

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_reopen_sequencer() -> Result<(), anyhow::Error> {
    // citrea::initialize_logging(tracing::Level::DEBUG);

    // open, close without publishing blocks
    let storage_dir = tempdir_with_children(&["DA", "sequencer"]);
    let da_db_dir = storage_dir.path().join("DA").to_path_buf();
    let sequencer_db_dir = storage_dir.path().join("sequencer").to_path_buf();

    let (seq_port_tx, seq_port_rx) = tokio::sync::oneshot::channel();

    let rollup_config = create_default_rollup_config(
        true,
        &sequencer_db_dir,
        &da_db_dir,
        NodeMode::SequencerNode,
        None,
    );
    let sequencer_config = SequencerConfig::default();
    let seq_task = start_rollup(
        seq_port_tx,
        GenesisPaths::from_dir(TEST_DATA_GENESIS_PATH),
        None,
        None,
        rollup_config,
        Some(sequencer_config),
        None,
        false,
    )
    .await;

    let seq_port = seq_port_rx.await.unwrap();

    let seq_test_client = init_test_rollup(seq_port).await;

    let block = seq_test_client
        .eth_get_block_by_number(Some(BlockNumberOrTag::Latest))
        .await;
    assert_eq!(block.header.number, 0);

    // close sequencer
    seq_task.graceful_shutdown();

    let (seq_port_tx, seq_port_rx) = tokio::sync::oneshot::channel();

    // Copy the db to a new path with the same contents because
    // the lock is not released on the db directory even though the task is aborted
    let _ = copy_db_dir_recursive(
        &sequencer_db_dir,
        &storage_dir.path().join("sequencer_copy"),
    );

    let da_service = MockDaService::new(MockAddress::from([0; 32]), &da_db_dir);
    da_service.publish_test_block().await.unwrap();

    wait_for_l1_block(&da_service, 1, None).await;

    let sequencer_db_dir = storage_dir.path().join("sequencer_copy");

    let rollup_config = create_default_rollup_config(
        true,
        &sequencer_db_dir,
        &da_db_dir,
        NodeMode::SequencerNode,
        None,
    );
    let sequencer_config = SequencerConfig::default();

    let seq_task = start_rollup(
        seq_port_tx,
        GenesisPaths::from_dir(TEST_DATA_GENESIS_PATH),
        None,
        None,
        rollup_config,
        Some(sequencer_config),
        None,
        true,
    )
    .await;

    let seq_port = seq_port_rx.await.unwrap();

    let seq_test_client = make_test_client(seq_port).await?;

    let seq_last_block = seq_test_client
        .eth_get_block_by_number(Some(BlockNumberOrTag::Latest))
        .await;

    // make sure the state roots are the same
    assert_eq!(seq_last_block.header.state_root, block.header.state_root);
    assert_eq!(seq_last_block.header.number, block.header.number);

    seq_test_client.send_publish_batch_request().await;
    seq_test_client.send_publish_batch_request().await;

    wait_for_l2_block(&seq_test_client, 2, None).await;

    assert_eq!(
        seq_test_client
            .eth_get_block_by_number(Some(BlockNumberOrTag::Latest))
            .await
            .header
            .number,
        2
    );

    seq_task.graceful_shutdown();

    Ok(())
}

#[tokio::test(flavor = "multi_thread")]
async fn test_reopen_prover() -> Result<(), anyhow::Error> {
    // citrea::initialize_logging(tracing::Level::DEBUG);

    let storage_dir = tempdir_with_children(&["DA", "sequencer", "prover"]);
    let da_db_dir = storage_dir.path().join("DA").to_path_buf();
    let sequencer_db_dir = storage_dir.path().join("sequencer").to_path_buf();
    let prover_db_dir = storage_dir.path().join("prover").to_path_buf();

    let da_service = MockDaService::new(MockAddress::default(), &da_db_dir.clone());

    let (seq_port_tx, seq_port_rx) = tokio::sync::oneshot::channel();

    let rollup_config = create_default_rollup_config(
        true,
        &sequencer_db_dir,
        &da_db_dir,
        NodeMode::SequencerNode,
        None,
    );
    let sequencer_config = SequencerConfig::default();

    let seq_task = start_rollup(
        seq_port_tx,
        GenesisPaths::from_dir(TEST_DATA_GENESIS_PATH),
        None,
        None,
        rollup_config,
        Some(sequencer_config),
        None,
        false,
    )
    .await;

    let seq_port = seq_port_rx.await.unwrap();
    let seq_test_client = make_test_client(seq_port).await?;

    let (prover_node_port_tx, prover_node_port_rx) = tokio::sync::oneshot::channel();

    let prover_db_dir_cloned = prover_db_dir.clone();
    let da_db_dir_cloned = da_db_dir.clone();

    let rollup_config = create_default_rollup_config(
        true,
        &prover_db_dir_cloned,
        &da_db_dir_cloned,
        NodeMode::Prover(seq_port),
        None,
    );

    let prover_node_task_manager = start_rollup(
        prover_node_port_tx,
        GenesisPaths::from_dir(TEST_DATA_GENESIS_PATH),
        Some(BatchProverConfig::default()),
        None,
        rollup_config,
        None,
        None,
        false,
    )
    .await;

    let prover_node_port = prover_node_port_rx.await.unwrap();
    let prover_client = make_test_client(prover_node_port).await?;

    // prover should not have any blocks saved
    assert_eq!(prover_client.eth_block_number().await, 0);
    // publish 3 l2 blocks, no commitment should be sent
    for _ in 0..3 {
        seq_test_client.send_publish_batch_request().await;
    }
    wait_for_l2_block(&seq_test_client, 3, None).await;

    seq_test_client.send_publish_batch_request().await;
    wait_for_l2_block(&seq_test_client, 4, None).await;

    // sequencer commitment should be sent
    // Block that contains the commitment
    wait_for_l1_block(&da_service, 2, None).await;

    // wait for prover to start the proving job
    let job_ids = wait_for_prover_job_count(&prover_client, 1, None)
        .await
        .unwrap();
    assert_eq!(job_ids.len(), 1);

    // wait for prover to finish the proving job
    wait_for_prover_job(&prover_client, job_ids[0], None)
        .await
        .unwrap();

    // Contains the proof
    wait_for_l1_block(&da_service, 3, None).await;

    // prover should have synced all 4 l2 blocks
    assert_eq!(prover_client.eth_block_number().await, 4);

    prover_node_task_manager.graceful_shutdown();

    sleep(Duration::from_secs(1)).await;

    let _ = copy_db_dir_recursive(&prover_db_dir, &storage_dir.path().join("prover_copy"));
    sleep(Duration::from_secs(1)).await;

    // Reopen prover with the new path
    let (prover_node_port_tx, prover_node_port_rx) = tokio::sync::oneshot::channel();

    let prover_copy_db_dir = storage_dir.path().join("prover_copy");
    let da_db_dir_cloned = da_db_dir.clone();

    let rollup_config = create_default_rollup_config(
        true,
        &prover_copy_db_dir,
        &da_db_dir_cloned,
        NodeMode::Prover(seq_port),
        None,
    );
    let prover_node_task_manager = start_rollup(
        prover_node_port_tx,
        GenesisPaths::from_dir(TEST_DATA_GENESIS_PATH),
        Some(BatchProverConfig::default()),
        None,
        rollup_config,
        None,
        None,
        false,
    )
    .await;

    let prover_node_port = prover_node_port_rx.await.unwrap();
    let prover_client = make_test_client(prover_node_port).await?;

    seq_test_client.send_publish_batch_request().await;
    wait_for_l2_block(&seq_test_client, 6, None).await;
    // Still should have 4 blocks there are no commitments yet
    wait_for_l2_block(&prover_client, 6, None).await;
    // Allow for the L2 block to be committed and stored
    // Otherwise, the L2 block height might be registered but it hasn't
    // been processed inside the EVM yet.
    sleep(Duration::from_secs(1)).await;
    assert_eq!(prover_client.eth_block_number().await, 6);

    prover_node_task_manager.graceful_shutdown();
    sleep(Duration::from_secs(2)).await;

    seq_test_client.send_publish_batch_request().await;
    seq_test_client.send_publish_batch_request().await;
    wait_for_l2_block(&seq_test_client, 8, None).await;
    let _ = copy_db_dir_recursive(&prover_db_dir, &storage_dir.path().join("prover_copy2"));

    sleep(Duration::from_secs(2)).await;
    // Reopen prover with the new path
    let (prover_node_port_tx, prover_node_port_rx) = tokio::sync::oneshot::channel();

    let prover_copy2_dir_cloned = storage_dir.path().join("prover_copy2");
    let da_db_dir_cloned = da_db_dir.clone();

    let rollup_config = create_default_rollup_config(
        true,
        &prover_copy2_dir_cloned,
        &da_db_dir_cloned,
        NodeMode::Prover(seq_port),
        None,
    );

    let prover_node_task_manager = start_rollup(
        prover_node_port_tx,
        GenesisPaths::from_dir(TEST_DATA_GENESIS_PATH),
        Some(BatchProverConfig::default()),
        None,
        rollup_config,
        None,
        None,
        false,
    )
    .await;

    let prover_node_port = prover_node_port_rx.await.unwrap();
    let prover_client = make_test_client(prover_node_port).await?;
    sleep(Duration::from_secs(2)).await;

    // We have 8 blocks in total, make sure the prover syncs
    // and starts proving the second commitment.
    wait_for_l2_block(&prover_client, 7, Some(Duration::from_secs(300))).await;
    assert_eq!(prover_client.eth_block_number().await, 8);
    sleep(Duration::from_secs(1)).await;

    seq_test_client.send_publish_batch_request().await;
    wait_for_l2_block(&seq_test_client, 9, None).await;

    wait_for_l1_block(&da_service, 4, None).await;
    sleep(Duration::from_secs(1)).await;

    // Commitment is sent
    wait_for_l1_block(&da_service, 5, None).await;

    // wait for prover to start the proving job
    let job_ids = wait_for_prover_job_count(&prover_client, 1, None)
        .await
        .unwrap();
    assert_eq!(job_ids.len(), 1);

    // wait for proving job to finish
    wait_for_prover_job(&prover_client, job_ids[0], None)
        .await
        .unwrap();

    // Should now have 8 blocks = 2 commitments of blocks 1-4 and 5-8
    // there is an extra l2 block due to the prover publishing a proof. This causes
    // a new MockDa block, which in turn causes the sequencer to publish an extra l2 block
    // TODO: Debug why this is not including block 9 in the commitment
    // https://github.com/chainwayxyz/citrea/issues/684
    assert!(prover_client.eth_block_number().await >= 8);
    // TODO: Also test with multiple commitments in single Mock DA Block
    seq_task.graceful_shutdown();
    prover_node_task_manager.graceful_shutdown();
    Ok(())
}
