[package]
name = "citrea"
version = { workspace = true }
authors = { workspace = true }
default-run = "citrea"
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
publish = false
resolver = "2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
# Citrea deps
bitcoin-da = { path = "../../crates/bitcoin-da", features = ["native"] }
citrea-batch-prover = { path = "../../crates/batch-prover" }
citrea-common = { path = "../../crates/common" }
citrea-fullnode = { path = "../../crates/fullnode" }
citrea-light-client-prover = { path = "../../crates/light-client-prover", features = ["native"] }
citrea-primitives = { path = "../../crates/primitives" }
citrea-risc0-adapter = { path = "../../crates/risc0", features = ["native"] }
citrea-risc0-batch-proof = { path = "../../guests/risc0/batch-proof" }
citrea-risc0-light-client = { path = "../../guests/risc0/light-client-proof" }
citrea-sequencer = { path = "../../crates/sequencer" }
# citrea-sp1 = { path = "../../crates/sp1", features = ["native"] }
citrea-stf = { path = "../../crates/citrea-stf", features = ["native"] }
citrea-storage-ops = { path = "../../crates/storage-ops" }
ethereum-rpc = { path = "../../crates/ethereum-rpc" }
prover-services = { path = "../../crates/prover-services" }
short-header-proof-provider = { path = "../../crates/short-header-proof-provider", features = ["native"] }

# Sovereign-SDK deps
l2-block-rule-enforcer = { path = "../../crates/l2-block-rule-enforcer" }
sov-db = { path = "../../crates/sovereign-sdk/full-node/db/sov-db" }
sov-keys = { path = "../../crates/sovereign-sdk/module-system/sov-keys", features = ["native"] }
sov-ledger-rpc = { path = "../../crates/sovereign-sdk/full-node/sov-ledger-rpc", features = ["server"] }
sov-mock-da = { path = "../../crates/sovereign-sdk/adapters/mock-da", features = ["native"] }
sov-modules-api = { path = "../../crates/sovereign-sdk/module-system/sov-modules-api", features = ["native"] }
sov-modules-rollup-blueprint = { path = "../../crates/sovereign-sdk/module-system/sov-modules-rollup-blueprint" }
sov-modules-stf-blueprint = { path = "../../crates/sovereign-sdk/module-system/sov-modules-stf-blueprint", features = ["native"] }
sov-prover-storage-manager = { path = "../../crates/sovereign-sdk/full-node/sov-prover-storage-manager" }
sov-rollup-interface = { path = "../../crates/sovereign-sdk/rollup-interface", features = ["native"] }
sov-state = { path = "../../crates/sovereign-sdk/module-system/sov-state", features = ["native"] }

# 3rd-party deps
alloy-primitives = { workspace = true }
alloy-sol-types = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
bitcoin = { workspace = true }
bitcoincore-rpc = { workspace = true }
borsh = { workspace = true, features = ["bytes"] }
clap = { workspace = true }
hex = { workspace = true }
jsonrpsee = { workspace = true, features = ["http-client", "server"] }
log-panics = { workspace = true }
metrics = { workspace = true }
metrics-exporter-prometheus = { workspace = true, default-features = true }
metrics-util = { workspace = true }
reth-primitives = { workspace = true }
reth-tasks = { workspace = true }
reth-transaction-pool = { workspace = true }
risc0-binfmt = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

[dev-dependencies]
citrea-evm = { path = "../../crates/evm", features = ["native"] }
citrea-primitives = { path = "../../crates/primitives", features = ["testing"] }
citrea-risc0-adapter = { path = "../../crates/risc0", features = ["native", "testing"] }
sov-mock-da = { path = "../../crates/sovereign-sdk/adapters/mock-da", default-features = false }
sov-prover-storage-manager = { path = "../../crates/sovereign-sdk/full-node/sov-prover-storage-manager", features = ["test-utils"] }
sov-rollup-interface = { path = "../../crates/sovereign-sdk/rollup-interface", features = ["testing"] }
sov-schema-db = { path = "../../crates/sovereign-sdk/full-node/db/sov-schema-db" }

alloy = { workspace = true, features = ["hyper", "consensus", "rpc-types-eth", "provider-http", "signers", "signer-local"] }
alloy-rlp = { workspace = true }
alloy-rpc-types = { workspace = true }
alloy-rpc-types-trace = { workspace = true }
base64 = { workspace = true }
bincode = { workspace = true }
borsh = { workspace = true }
chrono = { workspace = true }
futures = { workspace = true }
jmt = { workspace = true }
rand = { workspace = true }
reqwest = { workspace = true }
risc0-zkvm = { workspace = true, default-features = false, features = ["std"] }
rs_merkle = { workspace = true }
serde_json = { workspace = true }
sha2 = { workspace = true }
tempfile = { workspace = true }
tokio = { workspace = true }
uuid = { workspace = true }

revm = { workspace = true }

log = "0.4.22"
regex = "1.10"
rustc_version_runtime = { workspace = true }

# bitcoin-e2e dependencies
bitcoin.workspace = true
citrea-e2e = { workspace = true }

[build-dependencies]
sp1-helper = { version = "3.0.0", default-features = false }

[features]
default = [] # Deviate from convention by making the "native" feature active by default. This aligns with how this package is meant to be used (as a binary first, library second).
testing = [
  "bitcoin-da/testing",
  "citrea-primitives/testing",
  "citrea-risc0-batch-proof/testing",
  "citrea-risc0-light-client/testing",
  "sov-rollup-interface/testing",
]

[[bin]]
name = "citrea"
path = "src/main.rs"
