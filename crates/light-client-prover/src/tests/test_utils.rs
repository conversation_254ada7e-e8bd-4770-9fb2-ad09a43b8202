use std::collections::BTreeMap;
use std::path::PathBuf;
use std::sync::Arc;

use rand::{thread_rng, Rng};
use sov_mock_da::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>B<PERSON>b, <PERSON>ckDaSpec, MockDaVerifier};
use sov_mock_zkvm::{MockCodeCommitment, MockJournal, MockProof, MockZkvm};
use sov_modules_api::{WorkingSet, Zkvm};
use sov_modules_core::Storage;
use sov_prover_storage_manager::{Config, ProverStorage, ProverStorageManager};
use sov_rollup_interface::da::{
    BatchProofMethodId, BlobReaderTrait, DaVerifier, DataOnDa, SequencerCommitment,
};
use sov_rollup_interface::zk::batch_proof::output::v3::BatchProofCircuitOutputV3;
use sov_rollup_interface::zk::batch_proof::output::{BatchProofCircuitOutput, CumulativeStateDiff};
use sov_rollup_interface::zk::light_client_proof::input::LightClientCircuitInput;
use sov_rollup_interface::zk::light_client_proof::output::LightClientCircuitOutput;

use crate::circuit::accessors::ChunkAccessor;
use crate::circuit::LightClientProofCircuit;

pub(crate) fn create_mock_sequencer_commitment(
    index: u32,
    l2_end: u64,
    merkle_root: [u8; 32],
) -> SequencerCommitment {
    SequencerCommitment {
        index,
        l2_end_block_number: l2_end,
        merkle_root,
    }
}

pub(crate) fn create_mock_sequencer_commitment_blob(
    sequencer_commitment: SequencerCommitment,
) -> MockBlob {
    let da_data = DataOnDa::SequencerCommitment(sequencer_commitment);

    let da_data_ser = borsh::to_vec(&da_data).expect("should serialize");

    let blob = MockBlob::new(
        da_data_ser,
        MockAddress::new([45u8; 32]),
        [0u8; 32],
        [42; 32],
    );
    blob.full_data();

    blob
}

pub(crate) fn create_mock_batch_proof(
    initial_state_root: [u8; 32],
    last_l2_height: u64,
    is_valid: bool,
    last_l1_hash_on_bitcoin_light_client_contract: [u8; 32],
    sequencer_commitments: Vec<SequencerCommitment>,
    prev_commitment_hash: Option<[u8; 32]>,
    batch_prover_da_pubkey: [u8; 32],
) -> MockBlob {
    let batch_proof_method_id = MockCodeCommitment([0u8; 32]);

    let commitment_hashes: Vec<[u8; 32]> = sequencer_commitments
        .iter()
        .map(|c| c.serialize_and_calculate_sha_256())
        .collect();
    let prev_index = if sequencer_commitments[0].index <= 1 {
        None
    } else {
        Some(sequencer_commitments[0].index - 1)
    };
    let mut state_roots = vec![initial_state_root];

    // For the sake of easiness of impl tests, we can use merkle root as state root
    state_roots.extend(sequencer_commitments.iter().map(|c| c.merkle_root));

    let bp = BatchProofCircuitOutput::V3(BatchProofCircuitOutputV3 {
        state_roots,
        final_l2_block_hash: [4; 32],
        state_diff: BTreeMap::new(),
        last_l2_height,
        sequencer_commitment_hashes: commitment_hashes,
        last_l1_hash_on_bitcoin_light_client_contract,
        sequencer_commitment_index_range: (
            sequencer_commitments[0].index,
            sequencer_commitments[sequencer_commitments.len() - 1].index,
        ),
        previous_commitment_index: prev_index,
        previous_commitment_hash: prev_commitment_hash,
    });

    let bp_serialized = borsh::to_vec(&bp).expect("should serialize");

    let serialized_journal =
        borsh::to_vec(&MockJournal::Verifiable(bp_serialized.clone())).unwrap();

    let mock_proof = MockProof {
        program_id: batch_proof_method_id.clone(),
        is_valid,
        log: serialized_journal.clone(),
    };

    let mock_serialized = mock_proof.encode_to_vec();

    let da_data = DataOnDa::Complete(mock_serialized);
    let da_data_ser = borsh::to_vec(&da_data).expect("should serialize");

    let blob = MockBlob::new(
        da_data_ser,
        MockAddress::new(batch_prover_da_pubkey),
        [0u8; 32],
        [42; 32],
    );
    blob.full_data();

    blob
}

pub(crate) fn create_serialized_mock_proof(
    initial_state_root: [u8; 32],
    last_l2_height: u64,
    is_valid: bool,
    state_diff: Option<CumulativeStateDiff>,
    last_l1_hash_on_bitcoin_light_client_contract: [u8; 32],
    sequencer_commitments: Vec<SequencerCommitment>,
    prev_commitment_hash: Option<[u8; 32]>,
) -> Vec<u8> {
    let batch_proof_method_id = MockCodeCommitment([0u8; 32]);

    let commitment_hashes: Vec<[u8; 32]> = sequencer_commitments
        .iter()
        .map(|c| c.serialize_and_calculate_sha_256())
        .collect();
    let prev_index = if sequencer_commitments[0].index == 1 {
        None
    } else {
        Some(sequencer_commitments[0].index - 1)
    };

    let mut state_roots = vec![initial_state_root];

    // For the sake of easiness of impl tests, we can use merkle root as state root
    state_roots.extend(sequencer_commitments.iter().map(|c| c.merkle_root));

    let bp = BatchProofCircuitOutput::V3(BatchProofCircuitOutputV3 {
        state_roots,
        final_l2_block_hash: [4; 32],
        state_diff: state_diff.unwrap_or_default(),
        last_l2_height,
        sequencer_commitment_hashes: commitment_hashes,
        last_l1_hash_on_bitcoin_light_client_contract,
        sequencer_commitment_index_range: (
            sequencer_commitments[0].index,
            sequencer_commitments[sequencer_commitments.len() - 1].index,
        ),
        previous_commitment_index: prev_index,
        previous_commitment_hash: prev_commitment_hash,
    });

    let bp_serialized = borsh::to_vec(&bp).expect("should serialize");

    let serialized_journal =
        borsh::to_vec(&MockJournal::Verifiable(bp_serialized.clone())).unwrap();

    let mock_proof = MockProof {
        program_id: batch_proof_method_id.clone(),
        is_valid,
        log: serialized_journal.clone(),
    };

    mock_proof.encode_to_vec()
}

pub(crate) fn create_prev_lcp_serialized(
    output: LightClientCircuitOutput,
    is_valid: bool,
) -> Vec<u8> {
    let serialized = borsh::to_vec(&output).expect("should serialize");
    let mock_journal = match is_valid {
        true => borsh::to_vec(&MockJournal::Verifiable(serialized)).unwrap(),
        false => borsh::to_vec(&MockJournal::Unverifiable(serialized)).unwrap(),
    };
    let mock_proof = MockProof {
        program_id: output.light_client_proof_method_id.into(),
        is_valid,
        log: mock_journal,
    };

    mock_proof.encode_to_vec()
}

pub(crate) fn create_new_method_id_tx(
    activation_height: u64,
    new_method_id: [u32; 8],
    pub_key: [u8; 32],
) -> MockBlob {
    let da_data = DataOnDa::BatchProofMethodId(BatchProofMethodId {
        method_id: new_method_id,
        activation_l2_height: activation_height,
    });

    let da_data_ser = borsh::to_vec(&da_data).expect("should serialize");

    let blob = MockBlob::new(da_data_ser, MockAddress::new(pub_key), [0u8; 32], [42; 32]);
    blob.full_data();

    blob
}

pub(crate) fn create_random_state_diff(size_in_kb: u64) -> BTreeMap<Arc<[u8]>, Option<Arc<[u8]>>> {
    let mut rng = thread_rng();
    let mut map = BTreeMap::new();
    let mut total_size: u64 = 0;

    // Convert size to bytes
    let size_in_bytes = size_in_kb * 1024;

    while total_size < size_in_bytes {
        // Generate a random 32-byte key
        let key: Vec<u8> = (0..32).map(|_| rng.gen::<u8>()).collect();

        // Randomly decide if the value is `None` or a `Vec<u8>` of random length
        let value: Option<Vec<u8>> = if rng.gen_bool(0.1) {
            None
        } else {
            let value_size: usize = rng.gen_range(1..=2048);
            Some((0..value_size).map(|_| rng.gen::<u8>()).collect())
        };

        // Calculate the size of the key and value
        let key_size = key.len() as u64;
        let value_size = match &value {
            Some(v) => v.len() as u64 + 1,
            None => 1,
        };

        // Add to the map
        map.insert(
            Arc::from(key.into_boxed_slice()),
            value.map(|v| Arc::from(v.into_boxed_slice())),
        );

        // Update the total size
        total_size += key_size + value_size;
    }

    map
}

/// MockDA MockZkvm native context circuit runner implementation
pub struct NativeCircuitRunner {
    circuit: LightClientProofCircuit<ProverStorage, MockDaSpec, MockZkvm>,
    pub(crate) prover_storage_manager: ProverStorageManager,
}

impl NativeCircuitRunner {
    pub fn new(db_path: PathBuf) -> Self {
        let prover_storage_manager = ProverStorageManager::new(Config {
            path: db_path,
            db_max_open_files: None,
        })
        .unwrap();
        let circuit = LightClientProofCircuit::new();

        Self {
            circuit,
            prover_storage_manager,
        }
    }

    /// Run the circuit with the given input and return the input with its witness filled
    /// that will be used to run the circuit in ZK context
    pub fn run(
        &self,
        mut input: LightClientCircuitInput<MockDaSpec>,
        l2_genesis_state_root: [u8; 32],
        inital_batch_proof_method_ids: Vec<(u64, [u32; 8])>,
        batch_prover_da_pub_key: &[u8],
        sequencer_da_pub_key: &[u8],
        method_id_upgrade_authority: &[u8],
    ) -> LightClientCircuitInput<MockDaSpec> {
        let prover_storage = self
            .prover_storage_manager
            .create_storage_for_next_l2_height();

        let prev_lcp_output = input.previous_light_client_proof.clone().map(|proof| {
            let journal = MockZkvm::extract_raw_output(&proof).unwrap();
            MockZkvm::deserialize_output(&journal).unwrap()
        });

        let da_verifier = MockDaVerifier {};

        // Hack for mock da and mockzkvm usage
        let da_txs = da_verifier
            .verify_transactions(
                &input.da_block_header,
                input.inclusion_proof,
                input.completeness_proof.clone(),
            )
            .unwrap();

        let res = self.circuit.run_l1_block(
            sov_rollup_interface::Network::Nightly,
            prover_storage,
            Default::default(),
            da_txs,
            input.da_block_header.clone(),
            prev_lcp_output,
            l2_genesis_state_root,
            inital_batch_proof_method_ids,
            batch_prover_da_pub_key,
            sequencer_da_pub_key,
            method_id_upgrade_authority,
        );

        self.prover_storage_manager.finalize_storage(res.change_set);

        input.witness = res.witness;

        input
    }

    /// Used for a single test case
    pub fn insert_random_chunk(&self) {
        let prover_storage = self
            .prover_storage_manager
            .create_storage_for_next_l2_height();

        let mut working_set = WorkingSet::new(prover_storage.clone());

        let mut rng = thread_rng();
        let mut wtxid = [0u8; 32];
        let mut chunk = vec![0u8; 1024];

        rng.fill(&mut wtxid);
        rng.fill(&mut chunk[..]);

        ChunkAccessor::insert(wtxid, chunk, &mut working_set);

        let (read_write_log, mut witness) = working_set.checkpoint().freeze();

        let (_, jmt_state_update, _) = prover_storage
            .compute_state_update(&read_write_log, &mut witness, false)
            .unwrap();

        prover_storage.commit(&jmt_state_update, &Default::default(), &Default::default());
        self.prover_storage_manager.finalize_storage(prover_storage);
    }
}
