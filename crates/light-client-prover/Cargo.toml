[package]
name = "citrea-light-client-prover"
version.workspace = true
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true

[dependencies]
# Citrea Deps
bitcoin-da = { path = "../bitcoin-da", optional = true }
citrea-common = { path = "../common", optional = true }
citrea-primitives = { path = "../primitives" }
citrea-risc0-batch-proof = { path = "../../guests/risc0/batch-proof" }
prover-services = { path = "../prover-services", optional = true }

# Sov SDK deps
sov-db = { path = "../sovereign-sdk/full-node/db/sov-db", optional = true }
sov-mock-da = { path = "../sovereign-sdk/adapters/mock-da", optional = true }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", default-features = false }
sov-modules-core = { path = "../sovereign-sdk/module-system/sov-modules-core", default-features = false }
sov-prover-storage-manager = { path = "../sovereign-sdk/full-node/sov-prover-storage-manager", optional = true }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface" }
sov-state = { path = "../sovereign-sdk/module-system/sov-state", optional = true, features = ["native"] }

# 3rd-party deps
alloy-primitives = { workspace = true, optional = true }
anyhow = { workspace = true, optional = true }
async-trait = { workspace = true, optional = true }
borsh = { workspace = true }
const-hex = { workspace = true }
constmuck = { workspace = true }
hex = { workspace = true, optional = true }
jsonrpsee = { workspace = true, optional = true, features = ["http-client", "server", "client"] }
metrics = { workspace = true, optional = true }
metrics-derive = { workspace = true, optional = true }
reth-tasks = { workspace = true, optional = true }
tokio = { workspace = true, optional = true }
tracing = { workspace = true, optional = true }

[dev-dependencies]
rand = { workspace = true }
sov-mock-da = { path = "../sovereign-sdk/adapters/mock-da", features = ["native"] }
sov-mock-zkvm = { path = "../sovereign-sdk/adapters/mock-zkvm" }
sov-prover-storage-manager = { path = "../sovereign-sdk/full-node/sov-prover-storage-manager", features = ["test-utils"] }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface", features = ["testing"] }
sov-state = { path = "../sovereign-sdk/module-system/sov-state" }
tempfile = { workspace = true }

[features]
default = []
native = [
  "dep:sov-mock-da",
  "dep:bitcoin-da",
  "dep:prover-services",
  "dep:citrea-common",
  "dep:sov-prover-storage-manager",
  "dep:sov-db",
  "dep:hex",
  "dep:anyhow",
  "dep:async-trait",
  "dep:jsonrpsee",
  "dep:metrics",
  "dep:metrics-derive",
  "dep:reth-tasks",
  "dep:tokio",
  "dep:tracing",
  "dep:alloy-primitives",
  "sov-rollup-interface/native",
  "dep:sov-state",
]
