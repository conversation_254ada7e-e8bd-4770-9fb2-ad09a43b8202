[package]
name = "bitcoin-da"
version = { workspace = true }
authors = { workspace = true }
edition = "2021"
homepage = { workspace = true }
license = "MIT OR Apache-2.0"
publish = false
repository = { workspace = true }

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
citrea-common = { path = "../common", optional = true }
citrea-primitives = { path = "../primitives" }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface" }

anyhow = { workspace = true }
async-trait = { workspace = true, optional = true }
backoff = { workspace = true, optional = true }
bitcoin = { workspace = true }
borsh = { workspace = true }
crypto-bigint = { workspace = true }
futures = { workspace = true, optional = true }
hex = { workspace = true, features = ["serde"] }
itertools = { workspace = true }
jsonrpsee = { workspace = true, optional = true }
k256 = { workspace = true }
lru = { workspace = true, optional = true }
metrics = { workspace = true, optional = true }
metrics-derive = { workspace = true, optional = true }
reqwest = { workspace = true, optional = true }
reth-tasks = { workspace = true, optional = true }
secp256k1 = { version = "0.29", optional = true, features = ["rand-std", "std", "global-context"] }
serde = { workspace = true }
serde_json = { workspace = true, optional = true, features = ["raw_value"] }
sha2 = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true, features = ["full"], optional = true }
tracing = { workspace = true, optional = true }

bitcoincore-rpc = { workspace = true, optional = true }

[dev-dependencies]
rand = { workspace = true }

[features]
default = []
native = [
  "dep:async-trait",
  "dep:backoff",
  "dep:futures",
  "dep:lru",
  "dep:tokio",
  "dep:metrics",
  "dep:metrics-derive",
  "dep:reth-tasks",
  "dep:tracing",
  "dep:serde_json",
  "sov-rollup-interface/native",
  "dep:citrea-common",
  "dep:bitcoincore-rpc",
  "dep:reqwest",
  "dep:jsonrpsee",
  "dep:secp256k1",
]
testing = []
