[package]
name = "citrea-batch-prover"
version.workspace = true
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true

[dependencies]
# Citrea Deps
citrea-common = { path = "../common" }
citrea-primitives = { path = "../primitives" }
citrea-stf = { path = "../citrea-stf" }
prover-services = { path = "../prover-services" }
short-header-proof-provider = { path = "../short-header-proof-provider", features = ["native"] }
# Sov SDK deps
sov-db = { path = "../sovereign-sdk/full-node/db/sov-db" }
sov-keys = { path = "../sovereign-sdk/module-system/sov-keys" }
sov-ledger-rpc = { path = "../sovereign-sdk/full-node/sov-ledger-rpc", features = ["client"] }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", default-features = false }
sov-modules-core = { path = "../sovereign-sdk/module-system/sov-modules-core" }
sov-modules-stf-blueprint = { path = "../sovereign-sdk/module-system/sov-modules-stf-blueprint", features = ["native"] }
sov-prover-storage-manager = { path = "../sovereign-sdk/full-node/sov-prover-storage-manager" }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface" }
sov-state = { path = "../sovereign-sdk/module-system/sov-state" }

# 3rd-party deps
alloy-primitives = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
backoff = { workspace = true }
base64 = { workspace = true }
bincode = { workspace = true }
borsh = { workspace = true }
faster-hex = { workspace = true }
futures = { workspace = true }
hex = { workspace = true }
jsonrpsee = { workspace = true, features = ["http-client", "server", "client"] }
metrics = { workspace = true }
metrics-derive = { workspace = true }
parking_lot = { workspace = true }
rand = { workspace = true }
rayon = { workspace = true }
reth-tasks = { workspace = true }
risc0-zkvm = { workspace = true }
rs_merkle = { workspace = true }
serde = { workspace = true }
tokio = { workspace = true }
tower = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
uuid = { workspace = true }

[dev-dependencies]
citrea-primitives = { path = "../primitives", features = ["testing"] }
sov-db = { path = "../sovereign-sdk/full-node/db/sov-db" }
sov-mock-da = { path = "../sovereign-sdk/adapters/mock-da", features = ["native"] }
sov-mock-zkvm = { path = "../sovereign-sdk/adapters/mock-zkvm" }

tempfile = { workspace = true }
