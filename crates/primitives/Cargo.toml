[package]
name = "citrea-primitives"
version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
publish = false

[dependencies]
# Sov SDK deps
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface" }

# 3rd-party deps
alloy-eips = { workspace = true }
brotli = { workspace = true }
rs_merkle = { workspace = true }
sha2 = { workspace = true }

[features]
testing = ["sov-rollup-interface/testing"]
