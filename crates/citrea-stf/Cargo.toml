[package]
name = "citrea-stf"
version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
publish = false
resolver = "2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = { workspace = true, optional = true }
borsh = { workspace = true }
# will be needed after we update the storage system
# tokio = { workspace = true, features = ["sync"], optional = true }
jsonrpsee = { workspace = true, features = ["http-client", "server"], optional = true }
serde = { workspace = true }
serde_json = { workspace = true, optional = true }
tempfile = { workspace = true, optional = true }
tracing = { workspace = true, optional = true }

sov-accounts = { path = "../sovereign-sdk/module-system/module-implementations/sov-accounts", default-features = false }
sov-db = { path = "../sovereign-sdk/full-node/db/sov-db", optional = true }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", default-features = false }
sov-modules-stf-blueprint = { path = "../sovereign-sdk/module-system/sov-modules-stf-blueprint" }
sov-prover-storage-manager = { path = "../sovereign-sdk/full-node/sov-prover-storage-manager", optional = true }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface" }
sov-state = { path = "../sovereign-sdk/module-system/sov-state" }

citrea-evm = { path = "../evm" }
l2-block-rule-enforcer = { path = "../l2-block-rule-enforcer" }
short-header-proof-provider = { path = "../short-header-proof-provider" }

[dev-dependencies]
citrea-primitives = { path = "../primitives", features = ["testing"] }
citrea-stf = { path = ".", features = ["testing"] }
rs_merkle = { workspace = true }
sov-keys = { path = "../sovereign-sdk/module-system/sov-keys", features = ["native"] }
sov-mock-da = { path = "../sovereign-sdk/adapters/mock-da", features = ["native"] }
sov-mock-zkvm = { path = "../sovereign-sdk/adapters/mock-zkvm" }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", features = ["native"] }
sov-modules-core = { path = "../sovereign-sdk/module-system/sov-modules-core", features = ["native"] }
sov-modules-stf-blueprint = { path = "../sovereign-sdk/module-system/sov-modules-stf-blueprint", features = ["native"] }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface", features = ["testing"] }
sov-state = { path = "../sovereign-sdk/module-system/sov-state", features = ["native"] }

[features]
default = []
native = [
  "short-header-proof-provider/native",
  "dep:sov-db",
  "sov-accounts/native",
  "sov-modules-api/native",
  "sov-rollup-interface/native",
  "sov-modules-stf-blueprint/native",
  "l2-block-rule-enforcer/native",
  "citrea-evm/native",
  "dep:jsonrpsee",
  "dep:tracing",
  "dep:anyhow",
  "dep:serde_json",
]
testing = ["native", "dep:tempfile", "dep:sov-prover-storage-manager"]
