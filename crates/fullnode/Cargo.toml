[package]
name = "citrea-fullnode"
version.workspace = true
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true

[dependencies]
# Citrea Deps
citrea-common = { path = "../common" }
citrea-primitives = { path = "../primitives" }
citrea-stf = { path = "../citrea-stf" }
citrea-storage-ops = { path = "../storage-ops" }

# Sov SDK deps
sov-db = { path = "../sovereign-sdk/full-node/db/sov-db" }
sov-keys = { path = "../sovereign-sdk/module-system/sov-keys" }
sov-ledger-rpc = { path = "../sovereign-sdk/full-node/sov-ledger-rpc", features = ["client"] }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", default-features = false }
sov-modules-stf-blueprint = { path = "../sovereign-sdk/module-system/sov-modules-stf-blueprint", features = ["native"] }
sov-prover-storage-manager = { path = "../sovereign-sdk/full-node/sov-prover-storage-manager" }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface" }
sov-state = { path = "../sovereign-sdk/module-system/sov-state" }

# 3rd-party deps
alloy-primitives = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
backoff = { workspace = true }
bincode = { workspace = true }
borsh = { workspace = true }
hex = { workspace = true }
jsonrpsee = { workspace = true }
metrics = { workspace = true }
metrics-derive = { workspace = true }
reth-tasks = { workspace = true }
rs_merkle = { workspace = true }
serde = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }
