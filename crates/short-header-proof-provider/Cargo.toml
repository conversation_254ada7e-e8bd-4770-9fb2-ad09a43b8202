[package]
name = "short-header-proof-provider"
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
repository = { workspace = true }

version = { workspace = true }
publish = false
resolver = "2"

[dependencies]
borsh = { workspace = true }
futures = { workspace = true, optional = true }
parking_lot = { workspace = true, optional = true }

sov-db = { path = "../sovereign-sdk/full-node/db/sov-db", default-features = false, optional = true }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", default-features = false }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface", default-features = false }
thiserror = { workspace = true }

[dev-dependencies]
tempfile = { workspace = true }

sov-mock-da = { path = "../sovereign-sdk/adapters/mock-da", default-features = false }

[features]
native = [
  "sov-modules-api/native",
  "sov-rollup-interface/native",

  "dep:sov-db",
  "dep:futures",
  "dep:parking_lot",
]
