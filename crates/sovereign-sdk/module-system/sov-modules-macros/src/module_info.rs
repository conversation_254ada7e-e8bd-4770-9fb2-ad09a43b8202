use proc_macro2::{self, Ident, Span};
use syn::{
    Attribute, DataStruct, DeriveInput, ImplGenerics, LitStr, PathArguments, TypeGenerics,
    WhereClause,
};

use self::parsing::{<PERSON><PERSON>leField, ModuleFieldAttribute, StructDef};
use crate::common::get_generics_type_param;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>lone, PartialEq, Eq)]
pub enum ModuleType {
    Standard,
    Kernel,
}

pub(crate) fn derive_module_info(
    input: DeriveInput,
    variant: ModuleType,
) -> Result<proc_macro::TokenStream, syn::Error> {
    let struct_def = StructDef::parse(&input)?;

    let impl_prefix_functions = impl_prefix_functions(&struct_def)?;
    let impl_new = impl_module_info(&struct_def, variant)?;

    Ok(quote::quote! {
        #impl_prefix_functions

        #impl_new
    }
    .into())
}

// Creates a prefix function for each field of the underlying structure.
fn impl_prefix_functions(struct_def: &StructDef) -> Result<proc_macro2::TokenStream, syn::Error> {
    let StructDef {
        ident,
        struct_rename,
        impl_generics,
        type_generics,
        fields,
        where_clause,
        ..
    } = struct_def;

    let prefix_functions = fields
        .iter()
        // Don't generate prefix functions for modules or addresses; only state.
        .filter(|field| matches!(field.attr, ModuleFieldAttribute::State { .. }))
        .map(|field| make_prefix_func(field, ident, struct_rename));

    Ok(quote::quote! {
        impl #impl_generics #ident #type_generics #where_clause{
            #(#prefix_functions)*
        }
    })
}

// Implements the `ModuleInfo` trait.
fn impl_module_info(
    struct_def: &StructDef,
    variant: ModuleType,
) -> Result<proc_macro2::TokenStream, syn::Error> {
    let module_address = struct_def.module_address();

    let StructDef {
        ident,
        struct_rename,
        impl_generics,
        type_generics,
        generic_param,
        fields,
        where_clause,
    } = struct_def;

    let mut impl_self_init = Vec::default();
    let mut impl_self_body = Vec::default();
    let mut modules = Vec::default();

    for field in fields.iter() {
        match &field.attr {
            ModuleFieldAttribute::State { codec_builder, .. } => {
                impl_self_init.push(make_init_state(
                    field,
                    &codec_builder
                        .as_ref()
                        .cloned()
                        .unwrap_or_else(default_codec_builder),
                )?);
                impl_self_body.push(&field.ident);
            }
            ModuleFieldAttribute::Module => {
                impl_self_init.push(make_init_module(field, ModuleType::Standard)?);
                impl_self_body.push(&field.ident);
                modules.push(&field.ident);
            }
            ModuleFieldAttribute::KernelModule => {
                if let ModuleType::Standard = variant {
                    return Err(syn::Error::new_spanned(
                        &field.ident,
                        "The `#[kernel_module]` attribute is only allowed in kernel modules.",
                    ));
                }
                impl_self_init.push(make_init_module(field, ModuleType::Kernel)?);
                impl_self_body.push(&field.ident);
                modules.push(&field.ident);
            }
            ModuleFieldAttribute::Address => {
                impl_self_init.push(make_init_address(
                    field,
                    ident,
                    struct_rename,
                    generic_param,
                )?);
                impl_self_body.push(&field.ident);
            }
            ModuleFieldAttribute::Gas => {
                impl_self_body.push(&field.ident);
            }
            ModuleFieldAttribute::Memory => {
                impl_self_init.push(make_init_memory(field)?);
                impl_self_body.push(&field.ident);
            }
        };
    }

    let fn_address = make_fn_address(&module_address.ident)?;
    let fn_dependencies = make_fn_dependencies(modules);
    let fn_prefix = make_module_prefix_fn(ident, struct_rename);

    Ok(quote::quote! {
        impl #impl_generics ::std::default::Default for #ident #type_generics #where_clause{
            fn default() -> Self {
                #(#impl_self_init)*

                Self{
                    #(#impl_self_body),*
                }
            }
        }

        impl #impl_generics ::sov_modules_api::ModuleInfo for #ident #type_generics #where_clause{
            type Context = #generic_param;

            #fn_prefix

            #fn_address

            #fn_dependencies
        }
    })
}

fn default_codec_builder() -> syn::Path {
    syn::parse_str("::core::default::Default::default").unwrap()
}

fn make_prefix_func(
    field: &ModuleField,
    module_ident: &proc_macro2::Ident,
    module_rename: &Option<LitStr>,
) -> proc_macro2::TokenStream {
    let ModuleFieldAttribute::State { rename, .. } = &field.attr else {
        unreachable!("Prefix is implemented for state only");
    };
    let field_ident = &field.ident;
    let prefix_func_ident = prefix_func_ident(field_ident);
    let field_name = if let Some(name) = rename {
        quote::quote! { #name }
    } else {
        quote::quote! { stringify!(#field_ident) }
    };

    // generates prefix functions:
    //   fn _prefix_field_ident() -> sov_modules_api::ModulePrefix {
    //      let module_path = "some_module";
    //      sov_modules_api::ModulePrefix::new_storage(module_path, module_name, field_ident)
    //   }
    let module_name = if let Some(name) = module_rename {
        quote::quote! { #name }
    } else {
        quote::quote! { stringify!(#module_ident) }
    };
    quote::quote! {
        fn #prefix_func_ident() -> sov_modules_api::ModulePrefix {
            let module_path = module_path!();
            sov_modules_api::ModulePrefix::new_storage(module_path, #module_name, #field_name)
        }
    }
}

fn prefix_func_ident(ident: &proc_macro2::Ident) -> proc_macro2::Ident {
    syn::Ident::new(&format!("_prefix_{ident}"), ident.span())
}

fn make_fn_address(
    address_ident: &proc_macro2::Ident,
) -> Result<proc_macro2::TokenStream, syn::Error> {
    Ok(quote::quote! {
        fn address(&self) -> &<Self::Context as ::sov_modules_api::Spec>::Address {
           &self.#address_ident
        }
    })
}

fn make_fn_dependencies(modules: Vec<&proc_macro2::Ident>) -> proc_macro2::TokenStream {
    let address_tokens = modules.iter().map(|ident| {
        quote::quote! {
            &self.#ident.address()
        }
    });

    quote::quote! {
        fn dependencies(&self) -> ::std::vec::Vec<&<Self::Context as sov_modules_api::Spec>::Address> {
            ::std::vec![#(#address_tokens),*]
        }
    }
}
fn make_init_state(
    field: &ModuleField,
    encoding_constructor: &syn::Path,
) -> Result<proc_macro2::TokenStream, syn::Error> {
    let prefix_fun = prefix_func_ident(&field.ident);
    let field_ident = &field.ident;
    let ty = &field.ty;

    let ty = match ty {
        syn::Type::Path(syn::TypePath { path, .. }) => {
            let mut segments = path.segments.clone();

            let last = segments
                .last_mut()
                .expect("Impossible happened! A type path has no segments");

            // Remove generics for the type SomeType<G> => SomeType
            last.arguments = PathArguments::None;
            segments
        }

        _ => {
            return Err(syn::Error::new_spanned(
                ty,
                "Type not supported by the `ModuleInfo` macro",
            ));
        }
    };

    // generates code for the state initialization:
    //  let state_prefix = Self::_prefix_field_ident().into();
    //  let field_ident = path::StateType::new(state_prefix);
    Ok(quote::quote! {
        let state_prefix = Self::#prefix_fun().into();
        let #field_ident = #ty::with_codec(state_prefix, #encoding_constructor());
    })
}

fn make_init_module(
    field: &ModuleField,
    variant: ModuleType,
) -> Result<proc_macro2::TokenStream, syn::Error> {
    let field_ident = &field.ident;
    let ty = &field.ty;
    let trait_to_assert = match variant {
        ModuleType::Standard => quote::quote! { ::sov_modules_api::Module },
        ModuleType::Kernel => quote::quote! { ::sov_modules_api::KernelModule },
    };

    Ok(quote::quote! {
        // Ensure that the type implements "Module" or "KernelModule" at compile time
        let _ = <#ty as #trait_to_assert>::genesis;
        let #field_ident = <#ty as ::std::default::Default>::default();
    })
}

fn make_module_prefix_fn(
    struct_ident: &Ident,
    struct_rename: &Option<LitStr>,
) -> proc_macro2::TokenStream {
    let prefix_body = make_module_prefix_fn_body(struct_ident, struct_rename);
    quote::quote! {
        fn prefix(&self) -> sov_modules_api::ModulePrefix {
            #prefix_body
        }
    }
}

fn make_module_prefix_fn_body(
    struct_ident: &Ident,
    struct_rename: &Option<LitStr>,
) -> proc_macro2::TokenStream {
    let struct_name = if let Some(name) = struct_rename {
        quote::quote! { #name }
    } else {
        quote::quote! { stringify!(#struct_ident) }
    };

    quote::quote! {
        let module_path = module_path!();
        sov_modules_api::ModulePrefix::new_module(module_path, #struct_name)
    }
}

fn make_init_address(
    field: &ModuleField,
    struct_ident: &Ident,
    struct_rename: &Option<LitStr>,
    generic_param: &Ident,
) -> Result<proc_macro2::TokenStream, syn::Error> {
    let field_ident = &field.ident;
    let generate_prefix = make_module_prefix_fn_body(struct_ident, struct_rename);

    Ok(quote::quote! {
        use ::sov_modules_api::digest::Digest as _;
        let prefix = {
            #generate_prefix
        };

        let #field_ident : <#generic_param as sov_modules_api::Spec>::Address =
            <#generic_param as ::sov_modules_api::Spec>::Address::try_from(&prefix.hash::<#generic_param>())
                .unwrap_or_else(|e| panic!("ModuleInfo macro error, unable to create an Address for module: {}", e));
    })
}

// function for initializing memory fields
// calls default on the memory field
fn make_init_memory(field: &ModuleField) -> Result<proc_macro2::TokenStream, syn::Error> {
    let ident = &field.ident;

    // Generate the token stream that initializes the memory field with Default::default()
    let tokens = quote::quote! {
        let #ident = Default::default();
    };

    Ok(tokens)
}
/// Internal `proc macro` parsing utilities.
pub mod parsing {
    use syn::{Lit, Meta, MetaList, MetaNameValue};

    use super::*;

    pub struct StructDef<'a> {
        pub ident: &'a proc_macro2::Ident,
        pub struct_rename: Option<LitStr>,
        pub impl_generics: ImplGenerics<'a>,
        pub type_generics: TypeGenerics<'a>,
        pub generic_param: Ident,

        pub fields: Vec<ModuleField>,
        pub where_clause: Option<&'a WhereClause>,
    }

    /// Extracts the value of `rename` from `#[module(rename = "NewName")]`
    fn get_module_rename(attrs: &[Attribute]) -> Option<LitStr> {
        for attr in attrs {
            // Look for `#[module(...)]`
            if let Ok(Meta::List(MetaList { path, nested, .. })) = attr.parse_meta() {
                if path.is_ident("module") {
                    // Iterate over key-value pairs inside `module(...)`
                    for meta in nested {
                        if let syn::NestedMeta::Meta(Meta::NameValue(MetaNameValue {
                            path,
                            lit,
                            ..
                        })) = meta
                        {
                            if path.is_ident("rename") {
                                if let Lit::Str(lit_str) = lit {
                                    return Some(lit_str);
                                }
                            }
                        }
                    }
                }
            }
        }
        None
    }

    impl<'a> StructDef<'a> {
        pub fn parse(input: &'a DeriveInput) -> syn::Result<Self> {
            let ident = &input.ident;
            let module_rename = get_module_rename(&input.attrs);
            let generic_param = get_generics_type_param(&input.generics, Span::call_site())?;
            let (impl_generics, type_generics, where_clause) = input.generics.split_for_impl();
            let fields = parse_module_fields(&input.data)?;
            check_exactly_one_address(&fields)?;
            check_zero_or_one_gas(&fields)?;

            Ok(StructDef {
                ident,
                struct_rename: module_rename,
                fields,
                impl_generics,
                type_generics,
                generic_param,
                where_clause,
            })
        }

        pub fn module_address(&self) -> &ModuleField {
            self.fields
                .iter()
                .find(|field| matches!(field.attr, ModuleFieldAttribute::Address))
                .expect("Module address not found but it was validated already; this is a bug")
        }
    }

    #[derive(Clone)]
    pub struct ModuleField {
        pub ident: syn::Ident,
        pub ty: syn::Type,
        pub attr: ModuleFieldAttribute,
    }

    #[derive(Clone)]
    pub enum ModuleFieldAttribute {
        Module,
        KernelModule,
        State {
            codec_builder: Option<syn::Path>,
            rename: Option<syn::LitStr>,
        },
        Address,
        Gas,
        Memory,
    }

    impl ModuleFieldAttribute {
        fn parse(attr: &Attribute) -> syn::Result<Self> {
            match attr.path.segments[0].ident.to_string().as_str() {
                "module" => {
                    if attr.tokens.is_empty() {
                        Ok(Self::Module)
                    } else {
                        Err(syn::Error::new_spanned(
                            attr,
                            "The `#[module]` attribute does not accept any arguments.",
                        ))
                    }
                }
                "kernel_module" => {
                    if attr.tokens.is_empty() {
                        Ok(Self::KernelModule)
                    } else {
                        Err(syn::Error::new_spanned(
                            attr,
                            "The `#[kernel_module]` attribute does not accept any arguments.",
                        ))
                    }
                }
                "address" => {
                    if attr.tokens.is_empty() {
                        Ok(Self::Address)
                    } else {
                        Err(syn::Error::new_spanned(
                            attr,
                            "The `#[address]` attribute does not accept any arguments.",
                        ))
                    }
                }
                "state" => parse_state_attr(attr),
                "gas" => {
                    if attr.tokens.is_empty() {
                        Ok(Self::Gas)
                    } else {
                        Err(syn::Error::new_spanned(
                            attr,
                            "The `#[gas]` attribute does not accept any arguments.",
                        ))
                    }
                }
                "memory" => {
                    if attr.tokens.is_empty() {
                        Ok(Self::Memory)
                    } else {
                        Err(syn::Error::new_spanned(
                            attr,
                            "The `#[memory]` attribute does not accept any arguments.",
                        ))
                    }
                }
                _ => unreachable!("attribute names were validated already; this is a bug"),
            }
        }
    }

    fn parse_state_attr(attr: &Attribute) -> syn::Result<ModuleFieldAttribute> {
        let syntax_err =
            syn::Error::new_spanned(attr, "Invalid syntax for the `#[state]` attribute.");

        let meta = if attr.tokens.is_empty() {
            return Ok(ModuleFieldAttribute::State {
                codec_builder: None,
                rename: None,
            });
        } else {
            attr.parse_meta()?
        };

        let meta_list = match meta {
            syn::Meta::List(l) if !l.nested.is_empty() => l,
            _ => return Err(syntax_err),
        };
        let mut codec_builder = None;
        let mut rename = None;
        for nested in meta_list.nested {
            let nv = match nested {
                syn::NestedMeta::Meta(syn::Meta::NameValue(nv)) => nv,
                _ => return Err(syntax_err),
            };
            let Some(ident) = nv.path.get_ident() else {
                return Err(syntax_err);
            };
            match ident.to_string().as_str() {
                "codec_builder" => {
                    let path = match &nv.lit {
                        syn::Lit::Str(lit) => lit.parse_with(syn::Path::parse_mod_style)?,
                        _ => return Err(syntax_err),
                    };
                    codec_builder = Some(path)
                }
                "rename" => {
                    let name = match &nv.lit {
                        syn::Lit::Str(lit) => lit.to_owned(),
                        _ => return Err(syntax_err),
                    };
                    rename = Some(name)
                }
                _ => return Err(syntax_err),
            }
        }
        Ok(ModuleFieldAttribute::State {
            codec_builder,
            rename,
        })
    }

    fn parse_module_fields(data: &syn::Data) -> syn::Result<Vec<ModuleField>> {
        let data_struct = data_to_struct(data)?;
        let mut parsed_fields = vec![];

        for field in data_struct.fields.iter() {
            let ident = get_field_ident(field)?;
            let ty = field.ty.clone();
            let attr = get_field_attribute(field)?;

            parsed_fields.push(ModuleField {
                ident: ident.clone(),
                ty,
                attr: ModuleFieldAttribute::parse(attr)?,
            });
        }

        Ok(parsed_fields)
    }

    fn check_exactly_one_address(fields: &[ModuleField]) -> syn::Result<()> {
        let address_fields = fields
            .iter()
            .filter(|field| matches!(field.attr, ModuleFieldAttribute::Address))
            .collect::<Vec<_>>();

        match address_fields.len() {
            0 => Err(syn::Error::new(
                Span::call_site(),
                "The `ModuleInfo` macro requires `[address]` attribute.",
            )),
            1 => Ok(()),
            _ => Err(syn::Error::new_spanned(
                address_fields[1].ident.clone(),
                format!(
                    "The `address` attribute is defined more than once, revisit field: {}",
                    address_fields[1].ident,
                ),
            )),
        }
    }

    fn check_zero_or_one_gas(fields: &[ModuleField]) -> syn::Result<()> {
        let gas_fields = fields
            .iter()
            .filter(|field| matches!(field.attr, ModuleFieldAttribute::Gas))
            .collect::<Vec<_>>();

        match gas_fields.len() {
            0 | 1 => Ok(()),
            _ => Err(syn::Error::new_spanned(
                gas_fields[1].ident.clone(),
                format!(
                    "The `gas` attribute is defined more than once, revisit field: {}",
                    gas_fields[1].ident,
                ),
            )),
        }
    }

    fn data_to_struct(data: &syn::Data) -> syn::Result<&DataStruct> {
        match data {
            syn::Data::Struct(data_struct) => Ok(data_struct),
            syn::Data::Enum(en) => Err(syn::Error::new_spanned(
                en.enum_token,
                "The `ModuleInfo` macro supports structs only.",
            )),
            syn::Data::Union(un) => Err(syn::Error::new_spanned(
                un.union_token,
                "The `ModuleInfo` macro supports structs only.",
            )),
        }
    }

    fn get_field_ident(field: &syn::Field) -> syn::Result<&syn::Ident> {
        field.ident.as_ref().ok_or(syn::Error::new_spanned(
            field,
            "The `ModuleInfo` macro supports structs only, unnamed fields witnessed.",
        ))
    }

    fn get_field_attribute(field: &syn::Field) -> syn::Result<&Attribute> {
        let ident = get_field_ident(field)?;
        let mut attr = None;
        for a in field.attrs.iter() {
            match a.path.segments[0].ident.to_string().as_str() {
                "state" | "module" | "address" | "gas" | "kernel_module" | "memory" => {
                    if attr.is_some() {
                        return Err(syn::Error::new_spanned(ident, "Only one attribute out of `#[kernel_module]`, `#[module]`, `#[state]`, `#[address]`, `#[memory]`, and #[gas] is allowed per field."));
                    } else {
                        attr = Some(a);
                    }
                }
                _ => {}
            }
        }

        if let Some(attr) = attr {
            Ok(attr)
        } else {
            Err(syn::Error::new_spanned(
                ident,
                format!("The field `{}` is missing an attribute: add `#[kernel_module]`, `#[module]`, `#[state]`, `#[address]`, `#[memory]`, or #[gas].", ident),
            ))
        }
    }
}
