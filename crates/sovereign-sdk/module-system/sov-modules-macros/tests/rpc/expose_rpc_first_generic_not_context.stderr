error[E0220]: associated type `Storage` not found for `S`
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:96:1
   |
96 | #[expose_rpc]
   | ^^^^^^^^^^^^^ associated type `Storage` not found
   |
   = note: this error originates in the attribute macro `expose_rpc` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `S: sov_modules_api::Context` is not satisfied
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:16
   |
99 | struct Runtime<S: TestSpec, C: Context> {
   |                ^ the trait `sov_modules_api::Context` is not implemented for `S`
   |
note: required by a bound in `sov_modules_api::Genesis::Context`
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-core/src/module/mod.rs
   |
   |     type Context: Context;
   |                   ^^^^^^^ required by this bound in `Genesis::Context`
help: consider further restricting type parameter `S` with trait `Context`
   |
99 | struct Runtime<S: TestSpec + sov_modules_api::Context, C: Context> {
   |                            ++++++++++++++++++++++++++

error[E0277]: the trait bound `S: Spec` is not satisfied
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:97:10
   |
97 | #[derive(Genesis, DispatchCall, MessageCodec, DefaultRuntime)]
   |          ^^^^^^^ the trait `Spec` is not implemented for `S`
   |
   = note: this error originates in the derive macro `Genesis` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider further restricting type parameter `S` with trait `Spec`
   |
99 | struct Runtime<S: TestSpec + sov_modules_api::Spec, C: Context> {
   |                            +++++++++++++++++++++++

error[E0277]: `<S as TestSpec>::Data` cannot be shared between threads safely
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:8
   |
99 | struct Runtime<S: TestSpec, C: Context> {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `<S as TestSpec>::Data` cannot be shared between threads safely
   |
   = help: within `Runtime<S, C>`, the trait `std::marker::Sync` is not implemented for `<S as TestSpec>::Data`
note: required because it appears within the type `PhantomData<<S as TestSpec>::Data>`
  --> $RUST/core/src/marker.rs
   |
   | pub struct PhantomData<T: ?Sized>;
   |            ^^^^^^^^^^^
note: required because it appears within the type `sov_modules_api::StateValue<<S as TestSpec>::Data>`
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-api/src/containers/value.rs
   |
   | pub struct StateValue<V, Codec = BorshCodec> {
   |            ^^^^^^^^^^
note: required because it appears within the type `QueryModule<C, <S as TestSpec>::Data>`
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:36:16
   |
36 |     pub struct QueryModule<C: Context, D: Data> {
   |                ^^^^^^^^^^^
note: required because it appears within the type `Runtime<S, C>`
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:8
   |
99 | struct Runtime<S: TestSpec, C: Context> {
   |        ^^^^^^^
note: required by a bound in `sov_modules_api::DispatchCall`
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-core/src/module/dispatch.rs
   |
   | pub trait DispatchCall: Send + Sync {
   |                                ^^^^ required by this bound in `DispatchCall`
help: consider further restricting the associated type
   |
99 | struct Runtime<S: TestSpec, C: Context> where <S as TestSpec>::Data: std::marker::Sync {
   |                                         ++++++++++++++++++++++++++++++++++++++++++++++

error[E0277]: `<S as TestSpec>::Data` cannot be sent between threads safely
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:8
   |
99 | struct Runtime<S: TestSpec, C: Context> {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `<S as TestSpec>::Data` cannot be sent between threads safely
   |
   = help: within `Runtime<S, C>`, the trait `Send` is not implemented for `<S as TestSpec>::Data`
note: required because it appears within the type `PhantomData<<S as TestSpec>::Data>`
  --> $RUST/core/src/marker.rs
   |
   | pub struct PhantomData<T: ?Sized>;
   |            ^^^^^^^^^^^
note: required because it appears within the type `sov_modules_api::StateValue<<S as TestSpec>::Data>`
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-api/src/containers/value.rs
   |
   | pub struct StateValue<V, Codec = BorshCodec> {
   |            ^^^^^^^^^^
note: required because it appears within the type `QueryModule<C, <S as TestSpec>::Data>`
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:36:16
   |
36 |     pub struct QueryModule<C: Context, D: Data> {
   |                ^^^^^^^^^^^
note: required because it appears within the type `Runtime<S, C>`
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:8
   |
99 | struct Runtime<S: TestSpec, C: Context> {
   |        ^^^^^^^
note: required by a bound in `sov_modules_api::DispatchCall`
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-core/src/module/dispatch.rs
   |
   | pub trait DispatchCall: Send + Sync {
   |                         ^^^^ required by this bound in `DispatchCall`
help: consider further restricting the associated type
   |
99 | struct Runtime<S: TestSpec, C: Context> where <S as TestSpec>::Data: Send {
   |                                         +++++++++++++++++++++++++++++++++

error[E0277]: the trait bound `S: sov_modules_api::Context` is not satisfied
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:16
   |
99 | struct Runtime<S: TestSpec, C: Context> {
   |                ^ the trait `sov_modules_api::Context` is not implemented for `S`
   |
note: required by a bound in `sov_modules_api::DispatchCall::Context`
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-core/src/module/dispatch.rs
   |
   |     type Context: Context;
   |                   ^^^^^^^ required by this bound in `DispatchCall::Context`
help: consider further restricting type parameter `S` with trait `Context`
   |
99 | struct Runtime<S: TestSpec + sov_modules_api::Context, C: Context> {
   |                            ++++++++++++++++++++++++++

error[E0277]: `<S as TestSpec>::Data` cannot be shared between threads safely
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:97:19
   |
97 | #[derive(Genesis, DispatchCall, MessageCodec, DefaultRuntime)]
   |                   ^^^^^^^^^^^^ `<S as TestSpec>::Data` cannot be shared between threads safely
   |
   = help: within `RuntimeCall<S, C>`, the trait `std::marker::Sync` is not implemented for `<S as TestSpec>::Data`
note: required because it appears within the type `RuntimeCall<S, C>`
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:97:19
   |
97 | #[derive(Genesis, DispatchCall, MessageCodec, DefaultRuntime)]
   |                   ^^^^^^^^^^^^
note: required by a bound in `sov_modules_api::DispatchCall::Decodable`
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-core/src/module/dispatch.rs
   |
   |     type Decodable: Send + Sync;
   |                            ^^^^ required by this bound in `DispatchCall::Decodable`
   = note: this error originates in the derive macro `DispatchCall` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider further restricting the associated type
   |
99 | struct Runtime<S: TestSpec, C: Context> where <S as TestSpec>::Data: std::marker::Sync {
   |                                         ++++++++++++++++++++++++++++++++++++++++++++++

error[E0277]: `<S as TestSpec>::Data` cannot be sent between threads safely
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:97:19
   |
97 | #[derive(Genesis, DispatchCall, MessageCodec, DefaultRuntime)]
   |                   ^^^^^^^^^^^^ `<S as TestSpec>::Data` cannot be sent between threads safely
   |
   = help: within `RuntimeCall<S, C>`, the trait `Send` is not implemented for `<S as TestSpec>::Data`
note: required because it appears within the type `RuntimeCall<S, C>`
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:97:19
   |
97 | #[derive(Genesis, DispatchCall, MessageCodec, DefaultRuntime)]
   |                   ^^^^^^^^^^^^
note: required by a bound in `sov_modules_api::DispatchCall::Decodable`
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-core/src/module/dispatch.rs
   |
   |     type Decodable: Send + Sync;
   |                     ^^^^ required by this bound in `DispatchCall::Decodable`
   = note: this error originates in the derive macro `DispatchCall` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider further restricting the associated type
   |
99 | struct Runtime<S: TestSpec, C: Context> where <S as TestSpec>::Data: Send {
   |                                         +++++++++++++++++++++++++++++++++

error[E0277]: the trait bound `S: Spec` is not satisfied
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:97:19
   |
97 | #[derive(Genesis, DispatchCall, MessageCodec, DefaultRuntime)]
   |                   ^^^^^^^^^^^^ the trait `Spec` is not implemented for `S`
   |
   = note: this error originates in the derive macro `DispatchCall` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider further restricting type parameter `S` with trait `Spec`
   |
99 | struct Runtime<S: TestSpec + sov_modules_api::Spec, C: Context> {
   |                            +++++++++++++++++++++++

error[E0277]: the trait bound `S: Spec` is not satisfied
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:96:1
   |
96 | #[expose_rpc]
   | ^^^^^^^^^^^^^ the trait `Spec` is not implemented for `S`
   |
   = note: this error originates in the attribute macro `expose_rpc` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider further restricting type parameter `S` with trait `Spec`
   |
99 | struct Runtime<S: TestSpec + sov_modules_api::Spec, C: Context> {
   |                            +++++++++++++++++++++++

error[E0599]: no method named `set` found for struct `sov_modules_api::StateValue` in the current scope
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:53:23
   |
53 |             self.data.set(config, working_set);
   |                       ^^^ method not found in `StateValue<D>`

error[E0599]: no method named `set` found for struct `sov_modules_api::StateValue` in the current scope
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:62:23
   |
62 |             self.data.set(&msg, working_set);
   |                       ^^^ method not found in `StateValue<D>`

error[E0599]: no method named `get` found for struct `sov_modules_api::StateValue` in the current scope
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:87:39
   |
87 |                 let value = self.data.get(working_set).map(|d| format!("{:?}", d));
   |                                       ^^^ method not found in `StateValue<D>`

error[E0271]: type mismatch resolving `<QueryModule<C, <S as TestSpec>::Data> as ModuleInfo>::Context == S`
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:97:10
   |
97 | #[derive(Genesis, DispatchCall, MessageCodec, DefaultRuntime)]
   |          ^^^^^^^ expected type parameter `S`, found type parameter `C`
98 | #[serialization(borsh::BorshDeserialize, borsh::BorshSerialize)]
99 | struct Runtime<S: TestSpec, C: Context> {
   |                -            - found type parameter
   |                |
   |                expected type parameter
   |
   = note: expected type parameter `S`
              found type parameter `C`
   = note: a type parameter was expected, but a different one was found; you might be missing a type parameter or trait bound
   = note: for more information, visit https://doc.rust-lang.org/book/ch10-02-traits.html#traits-as-parameters
   = note: required for the cast from `&QueryModule<C, <S as TestSpec>::Data>` to `&dyn sov_modules_api::ModuleInfo<Context = S>`
   = note: this error originates in the derive macro `Genesis` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0277]: the trait bound `S: sov_modules_api::Context` is not satisfied
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:97:10
   |
97 | #[derive(Genesis, DispatchCall, MessageCodec, DefaultRuntime)]
   |          ^^^^^^^ the trait `sov_modules_api::Context` is not implemented for `S`
   |
note: required by a bound in `sort_values_by_modules_dependencies`
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-api/src/lib.rs
   |
   | pub fn sort_values_by_modules_dependencies<C: Context, TValue>(
   |                                               ^^^^^^^ required by this bound in `sort_values_by_modules_dependencies`
   = note: this error originates in the derive macro `Genesis` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider further restricting type parameter `S` with trait `Context`
   |
99 | struct Runtime<S: TestSpec + sov_modules_api::Context, C: Context> {
   |                            ++++++++++++++++++++++++++

error[E0308]: mismatched types
  --> tests/rpc/expose_rpc_first_generic_not_context.rs:97:19
   |
97 | #[derive(Genesis, DispatchCall, MessageCodec, DefaultRuntime)]
   |                   ^^^^^^^^^^^^
   |                   |
   |                   expected `&C`, found `&S`
   |                   arguments to this function are incorrect
98 | #[serialization(borsh::BorshDeserialize, borsh::BorshSerialize)]
99 | struct Runtime<S: TestSpec, C: Context> {
   |                -            - expected type parameter
   |                |
   |                found type parameter
   |
   = note: expected reference `&C`
              found reference `&S`
   = note: a type parameter was expected, but a different one was found; you might be missing a type parameter or trait bound
   = note: for more information, visit https://doc.rust-lang.org/book/ch10-02-traits.html#traits-as-parameters
note: method defined here
  --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-core/src/module/mod.rs
   |
   |     fn call(
   |        ^^^^
   = note: this error originates in the derive macro `DispatchCall` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0599]: the function or associated item `default` exists for struct `Runtime<ZkDefaultContext, ActualSpec>`, but its trait bounds were not satisfied
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:114:50
    |
99  | struct Runtime<S: TestSpec, C: Context> {
    | --------------------------------------- function or associated item `default` not found for this struct because it doesn't satisfy `_: Default`
...
103 | struct ActualSpec;
    | ----------------- doesn't satisfy `ActualSpec: sov_modules_api::Context`
...
114 |     let runtime = &mut Runtime::<C, ActualSpec>::default();
    |                                                  ^^^^^^^ function or associated item cannot be called on `Runtime<ZkDefaultContext, ActualSpec>` due to unsatisfied trait bounds
    |
   ::: $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-api/src/default_context.rs
    |
    | pub struct ZkDefaultContext {
    | --------------------------- doesn't satisfy `ZkDefaultContext: TestSpec`
    |
    = note: the following trait bounds were not satisfied:
            `ActualSpec: sov_modules_api::Context`
            `ZkDefaultContext: TestSpec`
note: the trait `sov_modules_api::Context` must be implemented
   --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-core/src/module/spec.rs
    |
    | pub trait Context: Spec + Clone + Debug + PartialEq + 'static {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `default`, perhaps you need to implement one of them:
            candidate #1: `std::default::Default`
            candidate #2: `tinyvec::array::Array`

error[E0277]: the trait bound `ZkDefaultContext: TestSpec` is not satisfied
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:114:24
    |
114 |     let runtime = &mut Runtime::<C, ActualSpec>::default();
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^ the trait `TestSpec` is not implemented for `ZkDefaultContext`
    |
    = help: the trait `TestSpec` is implemented for `ActualSpec`
note: required by a bound in `Runtime`
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:19
    |
99  | struct Runtime<S: TestSpec, C: Context> {
    |                   ^^^^^^^^ required by this bound in `Runtime`

error[E0277]: the trait bound `ActualSpec: sov_modules_api::Context` is not satisfied
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:114:24
    |
114 |     let runtime = &mut Runtime::<C, ActualSpec>::default();
    |                        ^^^^^^^^^^^^^^^^^^^^^^^^ the trait `sov_modules_api::Context` is not implemented for `ActualSpec`
    |
    = help: the following other types implement trait `sov_modules_api::Context`:
              DefaultContext
              ZkDefaultContext
note: required by a bound in `Runtime`
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:32
    |
99  | struct Runtime<S: TestSpec, C: Context> {
    |                                ^^^^^^^ required by this bound in `Runtime`

error[E0277]: the trait bound `Runtime<ZkDefaultContext, ActualSpec>: EncodeCall<QueryModule<ZkDefaultContext, u32>>` is not satisfied
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:120:10
    |
120 |         <RT as EncodeCall<my_module::QueryModule<C, u32>>>::encode_call(message);
    |          ^^ the trait `EncodeCall<QueryModule<ZkDefaultContext, u32>>` is not implemented for `Runtime<ZkDefaultContext, ActualSpec>`
    |
    = help: the trait `EncodeCall<QueryModule<C, <S as TestSpec>::Data>>` is implemented for `Runtime<S, C>`

error[E0277]: the trait bound `ZkDefaultContext: TestSpec` is not satisfied
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:120:10
    |
120 |         <RT as EncodeCall<my_module::QueryModule<C, u32>>>::encode_call(message);
    |          ^^ the trait `TestSpec` is not implemented for `ZkDefaultContext`
    |
    = help: the trait `TestSpec` is implemented for `ActualSpec`
note: required by a bound in `Runtime`
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:19
    |
99  | struct Runtime<S: TestSpec, C: Context> {
    |                   ^^^^^^^^ required by this bound in `Runtime`

error[E0277]: the trait bound `ActualSpec: sov_modules_api::Context` is not satisfied
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:120:10
    |
120 |         <RT as EncodeCall<my_module::QueryModule<C, u32>>>::encode_call(message);
    |          ^^ the trait `sov_modules_api::Context` is not implemented for `ActualSpec`
    |
    = help: the following other types implement trait `sov_modules_api::Context`:
              DefaultContext
              ZkDefaultContext
note: required by a bound in `Runtime`
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:32
    |
99  | struct Runtime<S: TestSpec, C: Context> {
    |                                ^^^^^^^ required by this bound in `Runtime`

error[E0599]: the function or associated item `decode_call` exists for struct `Runtime<ZkDefaultContext, ActualSpec>`, but its trait bounds were not satisfied
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:121:22
    |
99  | struct Runtime<S: TestSpec, C: Context> {
    | --------------------------------------- function or associated item `decode_call` not found for this struct because it doesn't satisfy `_: DispatchCall`
...
103 | struct ActualSpec;
    | ----------------- doesn't satisfy `ActualSpec: sov_modules_api::Context`
...
121 |     let module = RT::decode_call(&serialized_message).unwrap();
    |                      ^^^^^^^^^^^ function or associated item cannot be called on `Runtime<ZkDefaultContext, ActualSpec>` due to unsatisfied trait bounds
    |
   ::: $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-api/src/default_context.rs
    |
    | pub struct ZkDefaultContext {
    | --------------------------- doesn't satisfy `ZkDefaultContext: TestSpec`
    |
    = note: the following trait bounds were not satisfied:
            `ActualSpec: sov_modules_api::Context`
            `ZkDefaultContext: TestSpec`
note: the trait `sov_modules_api::Context` must be implemented
   --> $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-core/src/module/spec.rs
    |
    | pub trait Context: Spec + Clone + Debug + PartialEq + 'static {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `decode_call`, perhaps you need to implement it:
            candidate #1: `sov_modules_api::DispatchCall`

error[E0277]: the trait bound `ZkDefaultContext: TestSpec` is not satisfied
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:121:18
    |
121 |     let module = RT::decode_call(&serialized_message).unwrap();
    |                  ^^ the trait `TestSpec` is not implemented for `ZkDefaultContext`
    |
    = help: the trait `TestSpec` is implemented for `ActualSpec`
note: required by a bound in `Runtime`
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:19
    |
99  | struct Runtime<S: TestSpec, C: Context> {
    |                   ^^^^^^^^ required by this bound in `Runtime`

error[E0277]: the trait bound `ActualSpec: sov_modules_api::Context` is not satisfied
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:121:18
    |
121 |     let module = RT::decode_call(&serialized_message).unwrap();
    |                  ^^ the trait `sov_modules_api::Context` is not implemented for `ActualSpec`
    |
    = help: the following other types implement trait `sov_modules_api::Context`:
              DefaultContext
              ZkDefaultContext
note: required by a bound in `Runtime`
   --> tests/rpc/expose_rpc_first_generic_not_context.rs:99:32
    |
99  | struct Runtime<S: TestSpec, C: Context> {
    |                                ^^^^^^^ required by this bound in `Runtime`
