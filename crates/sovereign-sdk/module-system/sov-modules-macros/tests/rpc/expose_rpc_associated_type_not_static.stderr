error[E0599]: the method `set` exists for struct `StateValue<D>`, but its trait bounds were not satisfied
  --> tests/rpc/expose_rpc_associated_type_not_static.rs:56:23
   |
56 |             self.data.set(config, working_set);
   |                       ^^^ method cannot be called on `StateValue<D>` due to unsatisfied trait bounds
   |
  ::: $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-api/src/containers/value.rs
   |
   | pub struct StateValue<V, Codec = BorshCodec> {
   | -------------------------------------------- doesn't satisfy `_: StateValueAccessor<D, BorshCodec, WorkingSet<_>>`
   |
  ::: $WORKSPACE/crates/sovereign-sdk/module-system/sov-state/src/codec/borsh_codec.rs
   |
   | pub struct BorshCodec;
   | --------------------- doesn't satisfy `BorshCodec: StateValueCodec<D>`
   |
   = note: the following trait bounds were not satisfied:
           `BorshCodec: StateValueCodec<D>`
           which is required by `sov_modules_api::StateValue<D>: sov_modules_api::StateValueAccessor<D, BorshCodec, sov_modules_api::WorkingSet<_>>`

error[E0599]: the method `set` exists for struct `StateValue<D>`, but its trait bounds were not satisfied
  --> tests/rpc/expose_rpc_associated_type_not_static.rs:65:23
   |
65 |             self.data.set(&msg, working_set);
   |                       ^^^ method cannot be called on `StateValue<D>` due to unsatisfied trait bounds
   |
  ::: $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-api/src/containers/value.rs
   |
   | pub struct StateValue<V, Codec = BorshCodec> {
   | -------------------------------------------- doesn't satisfy `_: StateValueAccessor<D, BorshCodec, WorkingSet<_>>`
   |
  ::: $WORKSPACE/crates/sovereign-sdk/module-system/sov-state/src/codec/borsh_codec.rs
   |
   | pub struct BorshCodec;
   | --------------------- doesn't satisfy `BorshCodec: StateValueCodec<D>`
   |
   = note: the following trait bounds were not satisfied:
           `BorshCodec: StateValueCodec<D>`
           which is required by `sov_modules_api::StateValue<D>: sov_modules_api::StateValueAccessor<D, BorshCodec, sov_modules_api::WorkingSet<_>>`

error[E0599]: the method `get` exists for struct `StateValue<D>`, but its trait bounds were not satisfied
  --> tests/rpc/expose_rpc_associated_type_not_static.rs:90:39
   |
90 |                 let value = self.data.get(working_set).map(|d| format!("{:?}", d));
   |                                       ^^^ method cannot be called on `StateValue<D>` due to unsatisfied trait bounds
   |
  ::: $WORKSPACE/crates/sovereign-sdk/module-system/sov-modules-api/src/containers/value.rs
   |
   | pub struct StateValue<V, Codec = BorshCodec> {
   | -------------------------------------------- doesn't satisfy `_: StateValueAccessor<D, BorshCodec, WorkingSet<_>>`
   |
  ::: $WORKSPACE/crates/sovereign-sdk/module-system/sov-state/src/codec/borsh_codec.rs
   |
   | pub struct BorshCodec;
   | --------------------- doesn't satisfy `BorshCodec: StateValueCodec<D>`
   |
   = note: the following trait bounds were not satisfied:
           `BorshCodec: StateValueCodec<D>`
           which is required by `sov_modules_api::StateValue<D>: sov_modules_api::StateValueAccessor<D, BorshCodec, sov_modules_api::WorkingSet<_>>`

error[E0310]: the parameter type `S` may not live long enough
  --> tests/rpc/expose_rpc_associated_type_not_static.rs:99:1
   |
99 | #[expose_rpc]
   | ^^^^^^^^^^^^^
   | |
   | the parameter type `S` must be valid for the static lifetime...
   | ...so that the type `S` will meet its required lifetime bounds
   |
   = note: this error originates in the attribute macro `expose_rpc` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider adding an explicit lifetime bound
   |
102| struct Runtime<C: Context, S: TestSpec + 'static> {
   |                                        +++++++++
