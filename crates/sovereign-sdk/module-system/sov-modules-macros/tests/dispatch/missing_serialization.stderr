error: Serialization attributes must contain both 'Serialize' and 'Deserialize', but contains '[]'
 --> tests/dispatch/missing_serialization.rs:3:10
  |
3 | #[derive(DispatchCall)]
  |          ^^^^^^^^^^^^
  |
  = note: this error originates in the derive macro `DispatchCall` (in Nightly builds, run with -Z macro-backtrace for more info)

error: Serialization attributes must contain both 'Serialize' and 'Deserialize', but contains '["Serialize", "SomethingElse"]'
 --> tests/dispatch/missing_serialization.rs:6:10
  |
6 | #[derive(DispatchCall)]
  |          ^^^^^^^^^^^^
  |
  = note: this error originates in the derive macro `DispatchCall` (in Nightly builds, run with -Z macro-backtrace for more info)

error: Serialization attributes must contain both 'Serialize' and 'Deserialize', but contains '["OnlySomethingElse"]'
  --> tests/dispatch/missing_serialization.rs:10:10
   |
10 | #[derive(DispatchCall)]
   |          ^^^^^^^^^^^^
   |
   = note: this error originates in the derive macro `DispatchCall` (in Nightly builds, run with -Z macro-backtrace for more info)

error: Serialization attributes can not contain attributes that are not 'Serialize' and 'Deserialize', but contains: '["TryToInjectSomethingForbidden"]'
  --> tests/dispatch/missing_serialization.rs:14:10
   |
14 | #[derive(DispatchCall)]
   |          ^^^^^^^^^^^^
   |
   = note: this error originates in the derive macro `DispatchCall` (in Nightly builds, run with -Z macro-backtrace for more info)
