use sov_modules_api::DispatchCall;

#[derive(<PERSON><PERSON>atch<PERSON>all)]
struct TestStruct {}

#[derive(<PERSON>spatchCall)]
#[serialization(Serialize, SomethingElse)]
struct TestStruct2 {}

#[derive(DispatchCall)]
#[serialization(OnlySomethingElse)]
struct TestStruct3 {}

#[derive(<PERSON><PERSON>atch<PERSON>all)]
#[serialization(Serialize, Deserialize, TryToInjectSomethingForbidden)]
struct TestStruct4 {}

fn main() {}
