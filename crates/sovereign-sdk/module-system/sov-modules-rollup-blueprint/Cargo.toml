[package]
name = "sov-modules-rollup-blueprint"
version = { workspace = true }
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
publish = true
readme = "README.md"
repository = { workspace = true }
resolver = "2"
description = "This crate contains abstractions needed to create a new rollup"

[dependencies]
citrea-common = { path = "../../../common" }
citrea-stf = { path = "../../../citrea-stf" }
prover-services = { path = "../../../prover-services" }
sov-db = { path = "../../full-node/db/sov-db" }
sov-ledger-rpc = { path = "../../full-node/sov-ledger-rpc", features = ["server"] }
sov-modules-api = { path = "../../module-system/sov-modules-api", features = ["native"] }
sov-modules-stf-blueprint = { path = "../../module-system/sov-modules-stf-blueprint", features = ["native"] }
sov-prover-storage-manager = { path = "../../full-node/sov-prover-storage-manager" }
sov-rollup-interface = { path = "../../rollup-interface", features = ["native"] }

anyhow = { workspace = true }
async-trait = { workspace = true }
derive_more = { workspace = true, features = ["display"] }
jsonrpsee = { workspace = true, features = ["http-client", "server"] }
reth-tasks = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }
