[package]
name = "sov-accounts"
description = "A Sovereign SDK module for managing rollup state using accounts"
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
repository = { workspace = true }

version = { workspace = true }
readme = "README.md"
resolver = "2"

[dependencies]
borsh = { workspace = true, features = ["rc"] }
hex = { workspace = true }
schemars = { workspace = true, optional = true }
serde = { workspace = true }
serde_json = { workspace = true, optional = true }
thiserror = { workspace = true }
jsonrpsee = { workspace = true, features = [
    "macros",
    "client-core",
    "server",
], optional = true }

sov-db = { path = "../../../full-node/db/sov-db", optional = true }
sov-modules-api = { path = "../../sov-modules-api", default-features = false, features = [
    "macros",
] }
sov-state = { path = "../../sov-state" }
sov-keys = { path = "../../sov-keys" }
sov-rollup-interface = { path = "../../../rollup-interface" }


[dev-dependencies]
tempfile = { workspace = true }
sov-prover-storage-manager = { path = "../../../full-node/sov-prover-storage-manager", features = [
    "test-utils",
] }

[features]
default = ["native"]
native = [
    "serde",
    "serde_json",
    "jsonrpsee",
    "schemars",
    "dep:sov-db",
    "sov-state/native",
    "sov-modules-api/native",
]
serde = []
