mod genesis;
mod hooks;
use borsh::BorshSerialize;
pub use genesis::*;
#[cfg(feature = "native")]
mod query;
#[cfg(feature = "native")]
pub use query::*;
#[cfg(all(test, feature = "native"))]
mod tests;

pub use hooks::AccountsTxHook;
#[cfg(feature = "native")]
use sov_db::ledger_db::LedgerDB; // for rpc
use sov_keys::default_signature::K256PublicKey;
use sov_modules_api::{Address, Context, L2BlockModuleCallError, ModuleInfo, WorkingSet};
use sov_state::codec::BorshCodec;
use sov_state::storage::StateValueCodec;

impl FromIterator<Vec<u8>> for AccountConfig {
    fn from_iter<T: IntoIterator<Item = Vec<u8>>>(iter: T) -> Self {
        Self {
            pub_keys: iter.into_iter().collect(),
        }
    }
}

/// An account on the rollup.
#[derive(borsh::<PERSON><PERSON>hDese<PERSON>ize, borsh::BorshSerialize, Debug, PartialEq, Copy, Clone)]
pub struct Account {
    /// The address of the account.
    pub addr: Address,
    /// The current nonce value associated with the account.
    pub nonce: u64,
}

impl StateValueCodec<Account> for BorshCodec {
    type Error = std::io::Error;

    fn encode_value(&self, value: &Account) -> Vec<u8> {
        let mut buf = Vec::with_capacity(32 + 8);
        BorshSerialize::serialize(value, &mut buf).unwrap();
        buf
    }

    fn try_decode_value(&self, bytes: &[u8]) -> Result<Account, Self::Error> {
        borsh::from_slice(bytes)
    }
}

/// A module responsible for managing accounts on the rollup.
#[cfg_attr(feature = "native", derive(sov_modules_api::ModuleCallJsonSchema))]
#[derive(ModuleInfo, Clone)]
#[module(rename = "A")]
pub struct Accounts<C: Context> {
    /// The address of the sov-accounts module.
    #[address]
    pub address: C::Address,

    /// Mapping from an account address to a corresponding public key.
    #[state(rename = "p")]
    pub(crate) public_keys: sov_modules_api::StateMap<Address, K256PublicKey, BorshCodec>,

    /// Mapping from a public key to a corresponding account.
    #[state(rename = "a")]
    pub(crate) accounts: sov_modules_api::StateMap<K256PublicKey, Account, BorshCodec>,
}

impl<C: Context> sov_modules_api::Module for Accounts<C> {
    type Context = C;

    type Config = AccountConfig;

    type CallMessage = ();

    fn genesis(&self, config: &Self::Config, working_set: &mut WorkingSet<C::Storage>) {
        self.init_module(config, working_set)
    }

    fn call(
        &mut self,
        _msg: Self::CallMessage,
        _context: &Self::Context,
        _working_set: &mut WorkingSet<C::Storage>,
    ) -> Result<sov_modules_api::CallResponse, L2BlockModuleCallError> {
        Ok(sov_modules_api::CallResponse::default())
    }
}
