[package]
name = "sov-mock-zkvm"
description = "Mock zkVM implementation"
version.workspace = true
edition.workspace = true
license.workspace = true
authors.workspace = true
homepage.workspace = true
repository.workspace = true
publish = true


# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = { workspace = true }
borsh = { workspace = true }
uuid = { workspace = true }
serde = { workspace = true }
sov-rollup-interface = { path = "../../rollup-interface" }
tokio = { workspace = true }

[features]
default = []
