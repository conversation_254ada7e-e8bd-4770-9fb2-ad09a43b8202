//! Defines the traits that must be implemented by zkVMs. A zkVM like Risc0 consists of two components,
//! a "guest" and a "host". The guest is the zkVM program itself, and the host is the physical machine on
//! which the zkVM is running. Both the guest and the host are required to implement the [`Zkvm`] trait, in
//! addition to the specialized [`ZkvmGuest`] and [`ZkvmHost`] trait which is appropriate to that environment.
//!
//! For a detailed example showing how to implement these traits, see the
//! [risc0 adapter](https://github.com/Sovereign-Labs/sovereign-sdk/tree/main/adapters/risc0)
//! maintained by the Sovereign Labs team.

use std::fmt::Debug;

use borsh::{BorshDeserialize, BorshSerialize};
use serde::de::DeserializeOwned;
use serde::Serialize;
#[cfg(feature = "native")]
use tokio::sync::oneshot;

/// Definitions related to batch proofs.
pub mod batch_proof;
/// Definitions related to light client proofs.
pub mod light_client_proof;

/// The ZK proof generated by the [`ZkvmHost::run`] method.
pub type Proof = Vec<u8>;

#[cfg(feature = "native")]
#[derive(Debug, Clone)]
/// Wrapper around `Proof` to associate it with a job
pub struct ProofWithJob {
    /// Job id of the proof
    pub job_id: uuid::Uuid,
    /// Result proof bytes
    pub proof: Proof,
}

#[derive(Debug, Clone, Copy, BorshSerialize, BorshDeserialize)]
/// The type of the proof receipt
pub enum ReceiptType {
    /// Use Groth16
    Groth16,
    /// Use Succinct
    Succinct,
}

/// A trait implemented by the prover ("host") of a zkVM program.
#[cfg(feature = "native")]
pub trait ZkvmHost: Zkvm + Clone {
    /// The associated guest type
    type Guest: ZkvmGuest;
    /// Give the guest a piece of advice non-deterministically
    /// `item` is a borsh serialized input to the guest
    fn add_hint(&mut self, item: Vec<u8>);

    /// Simulate running the guest using the provided hints.
    ///
    /// Provides a simulated version of the guest which can be
    /// accessed in the current process.
    fn simulate_with_hints(&mut self) -> Self::Guest;

    /// Run the guest in the true zk environment using the provided hints.
    ///
    /// This runs the guest binary compiled for the zkVM target, optionally
    /// creating a SNARK of correct execution. Running the true guest binary comes
    /// with some mild performance overhead and is not as easy to debug as [`simulate_with_hints`](ZkvmHost::simulate_with_hints).
    fn run(
        &mut self,
        job_id: uuid::Uuid,
        elf: Vec<u8>,
        receipt_type: ReceiptType,
        with_prove: bool,
    ) -> anyhow::Result<oneshot::Receiver<ProofWithJob>>;

    /// Extracts public input and receipt from the proof.
    fn extract_output<T: BorshDeserialize>(proof: &Proof) -> Result<T, Self::Error>;

    /// Host recovers pending proving sessions and returns proving results
    fn start_session_recovery(&self)
        -> Result<Vec<oneshot::Receiver<ProofWithJob>>, anyhow::Error>;

    /// Host adds an assumption to the proving session
    /// Assumptions are used for recursive proving
    fn add_assumption(&mut self, receipt_buf: Vec<u8>);
}

/// A Zk proof system capable of proving and verifying arbitrary Rust code
/// Must support recursive proofs.
pub trait Zkvm: Send + Sync {
    /// A commitment to the zkVM program which is being proven
    type CodeCommitment: Clone
        + Debug
        + Serialize
        + DeserializeOwned
        + From<[u32; 8]>
        + Into<[u32; 8]>
        + Send
        + Sync
        + 'static;

    /// The error type which is returned when a proof fails to verify
    type Error: Debug;

    /// Interpret a sequence of a bytes as a proof and attempt to verify it against the code commitment.
    /// If the proof is valid, return Ok, else Err.
    fn verify(
        serialized_proof: &[u8],
        code_commitment: &Self::CodeCommitment,
        allow_dev_mode: bool,
    ) -> Result<(), Self::Error>;

    /// Extracts the raw output without doing any verification.
    /// The raw output is usually called "journal" which is the serialized output of the zkVM program.
    fn extract_raw_output(serialized_proof: &[u8]) -> Result<Vec<u8>, Self::Error>;

    /// Deserialize the output from the proof.
    /// This is used to extract the output from the zkVM program.
    fn deserialize_output<T: BorshDeserialize>(journal: &[u8]) -> Result<T, Self::Error>;

    /// Same as [`verify`](Zkvm::verify), except that instead of returning the output
    /// as a serialized array, it returns a state transition structure.
    /// TODO: specify a deserializer for the output
    fn verify_and_deserialize_output<T: BorshDeserialize>(
        serialized_proof: &[u8],
        code_commitment: &Self::CodeCommitment,
        allow_dev_mode: bool,
    ) -> Result<T, Self::Error>;
}

/// A trait which is accessible from within a zkVM program.
pub trait ZkvmGuest: Zkvm + Send + Sync {
    /// Obtain "advice" non-deterministically from the host
    fn read_from_host<T: BorshDeserialize>(&self) -> T;
    /// Add a public output to the zkVM proof
    fn commit<T: BorshSerialize>(&self, item: &T);
    /// Verify the ZK proof using assumption APIs
    /// Panics when a proof can't be verified
    fn verify_with_assumptions(journal: &[u8], code_commitment: &Self::CodeCommitment);
}

/// A trait expressing that two items of a type are (potentially fuzzy) matches.
/// We need a custom trait instead of relying on [`PartialEq`] because we allow fuzzy matches.
pub trait Matches<T> {
    /// Check if two items are a match
    fn matches(&self, other: &T) -> bool;
}

/// A cryptographic commitment to the contents of this storage
pub type StorageRootHash = [u8; 32];

/// Alias to jmt::proof::SparseMerkleProof.
pub type SparseMerkleProofSha2 = jmt::proof::SparseMerkleProof<sha2::Sha256>;
