[package]
name = "sov-rollup-interface"
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
repository = { workspace = true }
description = "Defines interfaces for building rollups with the Sovereign SDK"

version = { workspace = true }
exclude = [
  "specs/assets/*",
]
readme = "README.md"
resolver = "2"

[dependencies]
alloy-primitives = { workspace = true, features = ["serde"], optional = true }
anyhow = { workspace = true, features = ["default"] }
async-trait = { workspace = true, optional = true }
borsh = { workspace = true, features = ["default", "bytes", "rc"] }
bytes = { workspace = true, default-features = true }
digest = { workspace = true, features = ["default"] }
faster-hex = { workspace = true, optional = true }
hex = { workspace = true, optional = true, features = ["default"] }
jmt = { workspace = true }
uuid = { workspace = true, optional = true }
risc0-zkp = { workspace = true, optional = true }
serde = { workspace = true, features = ["default", "rc"] }
sha2 = { workspace = true }
thiserror = { workspace = true, optional = true }
# TODO: Remove tokio when https://github.com/Sovereign-Labs/sovereign-sdk/issues/1161 is resolved
tokio = { workspace = true, optional = true }
tracing = { workspace = true, optional = true }

sov-keys = { path = "../module-system/sov-keys" }

[dev-dependencies]
serde_json = { workspace = true }

[features]
default = []
native = [
  "alloy-primitives",
  "tokio",
  "tracing",
  "risc0-zkp",
  "faster-hex",
  "async-trait",
  "thiserror",
  "hex",
  "uuid",
]
testing = []
