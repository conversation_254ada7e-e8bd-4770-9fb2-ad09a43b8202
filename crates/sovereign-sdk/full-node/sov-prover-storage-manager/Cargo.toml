[package]
name = "sov-prover-storage-manager"
description = "Hierarchical storage manager for prover storage"
license = { workspace = true }
edition = { workspace = true }
authors = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }

version = { workspace = true }
readme = "README.md"
resolver = "2"

[dependencies]
anyhow = { workspace = true }
sov-rollup-interface = { path = "../../rollup-interface" }
sov-db = { path = "../db/sov-db" }
sov-schema-db = { path = "../db/sov-schema-db" }
sov-state = { path = "../../module-system/sov-state", features = ["native"] }
tracing = { workspace = true }

[dev-dependencies]
sov-schema-db = { path = "../db/sov-schema-db", features = ["test-utils"] }
tempfile = { workspace = true }
rand = { workspace = true }

[features]
default = []
test-utils = []
