#![forbid(unsafe_code)]

use alloy_primitives::{U32, U64};
use jsonrpsee::core::RpcResult;
use jsonrpsee::proc_macros::rpc;
use sov_rollup_interface::rpc::block::L2BlockResponse;
use sov_rollup_interface::rpc::{
    LastVerifiedBatchProofResponse, SequencerCommitmentResponse, VerifiedBatchProofResponse,
};

#[cfg(feature = "server")]
pub mod server;

/// A 32-byte hash [`serde`]-encoded as a hex string optionally prefixed with
/// `0x`. See [`sov_rollup_interface::rpc::utils::rpc_hex`].
#[derive(Debug, Copy, Clone, serde::Serialize, serde::Deserialize)]
pub struct HexHash(#[serde(with = "sov_rollup_interface::rpc::utils::rpc_hex")] pub [u8; 32]);

/// State root [`serde`]-encoded as a hex string prefixed with `0x`.
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct HexStateRoot(#[serde(with = "faster_hex")] pub Vec<u8>);

impl From<[u8; 32]> for HexHash {
    fn from(v: [u8; 32]) -> Self {
        Self(v)
    }
}

/// A [`jsonrpsee`] trait for interacting with the ledger JSON-RPC API.
///
/// Client and server implementations are automatically generated by
/// [`jsonrpsee`], see [`LedgerRpcClient`] and [`LedgerRpcServer`].
///
/// For more information about the specific methods, see the
/// [`sov_rollup_interface::rpc`] module.

#[cfg_attr(
    all(feature = "server", feature = "client"),
    rpc(server, client, namespace = "ledger")
)]
#[cfg_attr(
    all(feature = "server", not(feature = "client")),
    rpc(server, namespace = "ledger")
)]
#[cfg_attr(
    all(not(feature = "server"), feature = "client"),
    rpc(client, namespace = "ledger")
)]
pub trait LedgerRpc {
    /// Gets a single l2 block by number.
    #[method(name = "getL2BlockByNumber")]
    #[blocking]
    fn get_l2_block_by_number(&self, number: U64) -> RpcResult<Option<L2BlockResponse>>;

    /// Gets a single l2 block by hash.
    #[method(name = "getL2BlockByHash")]
    #[blocking]
    fn get_l2_block_by_hash(&self, hash: HexHash) -> RpcResult<Option<L2BlockResponse>>;

    /// Gets all l2 blocks with numbers `range.start` to `range.end`.
    #[method(name = "getL2BlockRange")]
    #[blocking]
    fn get_l2_block_range(&self, start: U64, end: U64) -> RpcResult<Vec<Option<L2BlockResponse>>>;

    /// Gets the L2 genesis state root.
    #[method(name = "getL2GenesisStateRoot")]
    #[blocking]
    fn get_l2_genesis_state_root(&self) -> RpcResult<Option<HexStateRoot>>;

    /// Gets the commitments in the DA slot with the given height.
    #[method(name = "getSequencerCommitmentsOnSlotByNumber")]
    #[blocking]
    fn get_sequencer_commitments_on_slot_by_number(
        &self,
        height: U64,
    ) -> RpcResult<Option<Vec<SequencerCommitmentResponse>>>;

    /// Gets the commitment by index.
    #[method(name = "getSequencerCommitmentByIndex")]
    #[blocking]
    fn get_sequencer_commitment_by_index(
        &self,
        index: U32,
    ) -> RpcResult<Option<SequencerCommitmentResponse>>;

    /// Gets the commitments in the DA slot with the given hash.
    #[method(name = "getSequencerCommitmentsOnSlotByHash")]
    #[blocking]
    fn get_sequencer_commitments_on_slot_by_hash(
        &self,
        hash: HexHash,
    ) -> RpcResult<Option<Vec<SequencerCommitmentResponse>>>;

    /// Gets the height pf most recent committed l2 block.
    #[method(name = "getHeadL2Block")]
    #[blocking]
    fn get_head_l2_block(&self) -> RpcResult<Option<L2BlockResponse>>;

    /// Gets the height pf most recent committed l2 block.
    #[method(name = "getHeadL2BlockHeight")]
    #[blocking]
    fn get_head_l2_block_height(&self) -> RpcResult<U64>;

    /// Gets verified proofs by slot height
    #[method(name = "getVerifiedBatchProofsBySlotHeight")]
    #[blocking]
    fn get_verified_batch_proofs_by_slot_height(
        &self,
        height: U64,
    ) -> RpcResult<Option<Vec<VerifiedBatchProofResponse>>>;

    /// Gets last verified proog
    #[method(name = "getLastVerifiedBatchProof")]
    #[blocking]
    fn get_last_verified_batch_proof(&self) -> RpcResult<Option<LastVerifiedBatchProofResponse>>;

    /// Get last scanned l1 height
    #[method(name = "getLastScannedL1Height")]
    #[blocking]
    fn get_last_scanned_l1_height(&self) -> RpcResult<U64>;
}
