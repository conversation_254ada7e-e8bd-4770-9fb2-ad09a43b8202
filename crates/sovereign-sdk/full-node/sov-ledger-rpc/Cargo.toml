[package]
name = "sov-ledger-rpc"
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
description = "JSON-RPC server and client implementations for Sovereign SDK rollups"
repository = { workspace = true }

version = { workspace = true }
resolver = "2"
publish = true

[dependencies]
# Common dependencies
faster-hex = { workspace = true, optional = true }
jsonrpsee = { workspace = true }
serde = {workspace = true }
sov-rollup-interface = { path = "../../rollup-interface", features = [
    "native",
] }
# Client dependencies
# (None)
# Server dependencies
anyhow = { workspace = true, optional = true }
sov-modules-api = { path = "../../module-system/sov-modules-api", features = [
    "native",
], optional = true }
alloy-primitives = { workspace = true }

[dev-dependencies]
tempfile = { workspace = true }
sov-db = { path = "../../full-node/db/sov-db" }
tokio = { workspace = true, features = ["full"] }

[features]
default = ["client", "server"]
server = ["anyhow", "jsonrpsee/server", "sov-modules-api", "faster-hex"]
client = ["jsonrpsee/client", "jsonrpsee/macros"]
