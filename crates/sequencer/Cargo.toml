[package]
name = "citrea-sequencer"
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
repository = { workspace = true }

version = { workspace = true }
publish = false
readme = "README.md"
resolver = "2"

[dependencies]
# 3rd-party deps
alloy-rlp = { workspace = true }
alloy-serde = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
backoff = { workspace = true }
borsh = { workspace = true }
chrono = { workspace = true }
digest = { workspace = true }
hex = { workspace = true }
jsonrpsee = { workspace = true, features = ["http-client", "server", "client"] }
metrics = { workspace = true }
metrics-derive = { workspace = true }
parking_lot = { workspace = true }
rs_merkle = { workspace = true }
schnellru = "0.2.1"
tokio = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

alloy-eips = { workspace = true }
alloy-genesis = { workspace = true }
alloy-network = { workspace = true }
alloy-primitives = { workspace = true }
alloy-rpc-types = { workspace = true }
alloy-rpc-types-eth = { workspace = true }
reth-chainspec = { workspace = true }
reth-db = { workspace = true }
reth-execution-types = { workspace = true }
reth-primitives = { workspace = true }
reth-provider = { workspace = true }
reth-rpc = { workspace = true }
reth-rpc-eth-api = { workspace = true }
reth-rpc-eth-types = { workspace = true }
reth-rpc-types-compat = { workspace = true }
reth-tasks = { workspace = true }
reth-transaction-pool = { workspace = true }
reth-trie = { workspace = true }
revm = { workspace = true }

# Sovereign SDK deps
sov-accounts = { path = "../sovereign-sdk/module-system/module-implementations/sov-accounts", default-features = false }
sov-db = { path = "../sovereign-sdk/full-node/db/sov-db" }
sov-keys = { path = "../sovereign-sdk/module-system/sov-keys" }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", default-features = false }
sov-modules-stf-blueprint = { path = "../sovereign-sdk/module-system/sov-modules-stf-blueprint" }
sov-prover-storage-manager = { path = "../sovereign-sdk/full-node/sov-prover-storage-manager" }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface", features = ["native"] }
sov-state = { path = "../sovereign-sdk/module-system/sov-state" }

# Citrea Deps
citrea-common = { path = "../common" }
citrea-evm = { path = "../evm", features = ["native"] }
citrea-primitives = { path = "../primitives" }
citrea-stf = { path = "../citrea-stf", features = ["native"] }
l2-block-rule-enforcer = { path = "../l2-block-rule-enforcer", features = ["native"] }

[features]
default = []
