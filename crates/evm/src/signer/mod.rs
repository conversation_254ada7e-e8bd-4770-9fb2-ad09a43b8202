use std::collections::HashMap;

use alloy_consensus::SignableTransaction;
use alloy_eips::eip7702::SignedAuthorization;
use alloy_primitives::{Address, B256};
use alloy_rpc_types::Authorization;
use reth_primitives::{sign_message, Transaction, TransactionSigned};
use reth_rpc_eth_types::SignError;
use secp256k1::{PublicKey, SecretKey};

/// Ethereum transaction signer.
#[derive(Clone)]
pub struct DevSigner {
    signers: HashMap<Address, SecretKey>,
}

impl DevSigner {
    /// Creates a new DevSigner.
    pub fn new(secret_keys: Vec<SecretKey>) -> Self {
        let mut signers = HashMap::with_capacity(secret_keys.len());

        for sk in secret_keys {
            let public_key = PublicKey::from_secret_key(secp256k1::SECP256K1, &sk);
            let address = reth_primitives::public_key_to_address(public_key);

            signers.insert(address, sk);
        }

        Self { signers }
    }

    /// Signs an ethereum transaction.
    pub fn sign_transaction(
        &self,
        transaction: Transaction,
        address: Address,
    ) -> Result<TransactionSigned, SignError> {
        let tx_signature_hash = transaction.signature_hash();
        let signer = self.signers.get(&address).ok_or(SignError::NoAccount)?;

        let signature = sign_message(B256::from_slice(signer.as_ref()), tx_signature_hash)
            .map_err(|_| SignError::CouldNotSign)?;

        Ok(TransactionSigned::new_unhashed(transaction, signature))
    }

    pub fn sign_authorization(
        &self,
        authorization: Authorization,
        address: Address,
    ) -> Result<SignedAuthorization, SignError> {
        let signer = self.signers.get(&address).ok_or(SignError::NoAccount)?;

        let signature = sign_message(
            B256::from_slice(signer.as_ref()),
            authorization.signature_hash(),
        )
        .map_err(|_| SignError::CouldNotSign)?;

        let signed = authorization.into_signed(signature);

        Ok(signed)
    }
}
