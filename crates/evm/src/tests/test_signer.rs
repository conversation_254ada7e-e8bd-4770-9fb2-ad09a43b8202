use alloy_consensus::{
    TxEip1559 as RethTxEip1559, TxEip4844 as RethTxEip4844, TxEip7702 as RethTxEip7702,
};
use alloy_eips::eip2718::Encodable2718;
use alloy_eips::eip7702::SignedAuthorization;
use alloy_primitives::{Address, Bytes as Reth<PERSON>yte<PERSON>, TxKind, B256, U256};
use alloy_rpc_types::Authorization;
use rand::rngs::StdRng;
use rand::SeedableRng;
use reth_primitives::Transaction as RethTransaction;
use reth_rpc_eth_types::SignError;
use secp256k1::{PublicKey, SecretKey};

use crate::evm::RlpEvmTransaction;
use crate::signer::DevSigner;
use crate::tests::DEFAULT_CHAIN_ID;

/// ETH transactions signer used in tests.
pub(crate) struct TestSigner {
    signer: <PERSON><PERSON><PERSON><PERSON>,
    address: Address,
}

impl TestSigner {
    /// Creates a new signer.
    pub(crate) fn new(secret_key: SecretKey) -> Self {
        let public_key = PublicKey::from_secret_key(secp256k1::SECP256K1, &secret_key);
        let address = reth_primitives::public_key_to_address(public_key);
        Self {
            signer: DevSigner::new(vec![secret_key]),
            address,
        }
    }

    /// Creates a new signer with random private key.
    pub(crate) fn new_random() -> Self {
        let mut rng = StdRng::seed_from_u64(22);
        let secret_key = SecretKey::new(&mut rng);
        Self::new(secret_key)
    }

    /// Address of the transaction signer.
    pub(crate) fn address(&self) -> Address {
        self.address
    }

    /// Signs default Eip1559 transaction with to, data and nonce overridden.
    pub(crate) fn sign_default_transaction(
        &self,
        to: TxKind,
        data: Vec<u8>,
        nonce: u64,
        value: u128,
    ) -> Result<RlpEvmTransaction, SignError> {
        self.sign_default_transaction_with_fee(to, data, nonce, value, 100000000000u128)
    }

    /// Signs default Eip1559 transaction with to, data and nonce overridden.
    pub(crate) fn sign_default_transaction_with_fee(
        &self,
        to: TxKind,
        data: Vec<u8>,
        nonce: u64,
        value: u128,
        max_fee_per_gas: u128,
    ) -> Result<RlpEvmTransaction, SignError> {
        let reth_tx = RethTxEip1559 {
            to,
            input: RethBytes::from(data),
            nonce,
            value: U256::from(value),
            chain_id: DEFAULT_CHAIN_ID,
            gas_limit: 1_000_000u64,
            max_fee_per_gas,
            ..Default::default()
        };

        let reth_tx = RethTransaction::Eip1559(reth_tx);
        let signed = self.signer.sign_transaction(reth_tx, self.address)?;
        let mut buf = vec![];
        signed.encode_2718(&mut buf);
        Ok(RlpEvmTransaction { rlp: buf })
    }

    /// Signs default Eip1559 transaction with to, data, gas limit and nonce overridden.
    pub(crate) fn sign_default_transaction_with_fee_and_gas_limit(
        &self,
        to: TxKind,
        data: Vec<u8>,
        nonce: u64,
        value: u128,
        max_fee_per_gas: u64,
        gas_limit: u64,
    ) -> Result<RlpEvmTransaction, SignError> {
        let reth_tx = RethTxEip1559 {
            to,
            input: RethBytes::from(data),
            nonce,
            value: U256::from(value),
            chain_id: DEFAULT_CHAIN_ID,
            gas_limit,
            max_fee_per_gas: max_fee_per_gas as u128,
            ..Default::default()
        };

        let reth_tx = RethTransaction::Eip1559(reth_tx);
        let signed = self.signer.sign_transaction(reth_tx, self.address)?;
        let mut buf = vec![];
        signed.encode_2718(&mut buf);
        Ok(RlpEvmTransaction { rlp: buf })
    }

    /// Signs default Eip1559 transaction with to, data and nonce overridden.
    pub(crate) fn sign_default_transaction_with_priority_fee(
        &self,
        to: TxKind,
        data: Vec<u8>,
        nonce: u64,
        value: u128,
        max_fee_per_gas: u128,
        max_priority_fee_per_gas: u128,
    ) -> Result<RlpEvmTransaction, SignError> {
        let reth_tx = RethTxEip1559 {
            to,
            input: RethBytes::from(data),
            nonce,
            value: U256::from(value),
            chain_id: DEFAULT_CHAIN_ID,
            gas_limit: 1_000_000u64,
            max_fee_per_gas,
            max_priority_fee_per_gas,
            ..Default::default()
        };

        let reth_tx = RethTransaction::Eip1559(reth_tx);
        let signed = self.signer.sign_transaction(reth_tx, self.address)?;
        let mut buf = vec![];
        signed.encode_2718(&mut buf);
        Ok(RlpEvmTransaction { rlp: buf })
    }

    pub(crate) fn sign_blob_transaction(
        &self,
        to: Address,
        blob_versioned_hashes: Vec<B256>,
        nonce: u64,
    ) -> Result<RlpEvmTransaction, SignError> {
        let reth_tx = RethTxEip4844 {
            to,
            nonce,
            chain_id: DEFAULT_CHAIN_ID,
            blob_versioned_hashes,
            max_fee_per_blob_gas: 100000000000u128,
            max_fee_per_gas: 100000000000u128,
            gas_limit: 1_000_000u64,
            ..Default::default()
        };

        let reth_tx = RethTransaction::Eip4844(reth_tx);
        let signed = self.signer.sign_transaction(reth_tx, self.address)?;
        let mut buf = vec![];
        signed.encode_2718(&mut buf);
        Ok(RlpEvmTransaction { rlp: buf })
    }

    pub(crate) fn get_signed_authorization(
        &self,
        to: Address,
        nonce: u64,
    ) -> Result<SignedAuthorization, SignError> {
        let authorization = Authorization {
            chain_id: U256::from(DEFAULT_CHAIN_ID),
            address: to,
            nonce,
        };

        self.signer.sign_authorization(authorization, self.address)
    }

    pub(crate) fn sign_eip7702_transaction(
        &self,
        to: Address,
        data: Vec<u8>,
        nonce: u64,
        authorization_list: Vec<SignedAuthorization>,
    ) -> Result<RlpEvmTransaction, SignError> {
        let tx = RethTxEip7702 {
            chain_id: DEFAULT_CHAIN_ID,
            nonce,
            gas_limit: 1_000_000,
            max_fee_per_gas: 100000000000u128,
            to,
            authorization_list,
            input: RethBytes::from(data),
            ..Default::default()
        };

        let reth_tx = RethTransaction::Eip7702(tx);
        let signed = self.signer.sign_transaction(reth_tx, self.address)?;
        let mut buf = vec![];
        signed.encode_2718(&mut buf);
        Ok(RlpEvmTransaction { rlp: buf })
    }
}
