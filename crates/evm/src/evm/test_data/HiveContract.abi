[{"constant": true, "inputs": [], "name": "ui", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "addr", "type": "address"}], "name": "getFromMap", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "addr", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "addToMap", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "ui_", "type": "uint256"}, {"name": "addr_", "type": "address"}], "name": "events", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "a", "type": "uint256"}, {"name": "b", "type": "uint256"}, {"name": "c", "type": "uint256"}], "name": "constFunc", "outputs": [{"name": "", "type": "uint256"}, {"name": "", "type": "uint256"}, {"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "ui_", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [], "name": "E0", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "", "type": "uint256"}], "name": "E1", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "", "type": "uint256"}], "name": "E2", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "", "type": "address"}], "name": "E3", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "", "type": "address"}], "name": "E4", "type": "event"}, {"anonymous": true, "inputs": [{"indexed": false, "name": "", "type": "uint256"}, {"indexed": false, "name": "", "type": "address"}], "name": "E5", "type": "event"}]