// SPDX-License-Identifier: MIT

// solc --abi --bin  Store.sol  -o . --overwrite
pragma solidity ^0.8.0;

interface SimpleStorageInterface {
    function set(uint256 _num) external;
}

contract SimpleStorageDuplicator {
    uint256 public num;
    
    function set(uint256 _num, address _address) public {
        SimpleStorageInterface(_address).set(_num);
        num = _num;
    }
    
    function get() public view returns (uint) {
        return num;
    }
}