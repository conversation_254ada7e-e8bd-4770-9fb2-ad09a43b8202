[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "contractAddress", "type": "address"}], "name": "AnotherLog", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "contractAddress", "type": "address"}, {"indexed": true, "internalType": "string", "name": "senderMessage", "type": "string"}, {"indexed": false, "internalType": "string", "name": "message", "type": "string"}], "name": "Log", "type": "event"}, {"inputs": [{"internalType": "string", "name": "_senderMessage", "type": "string"}], "name": "publishEvent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]