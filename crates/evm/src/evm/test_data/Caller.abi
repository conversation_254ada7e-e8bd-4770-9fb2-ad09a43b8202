[{"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "callget", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "uint256", "name": "num", "type": "uint256"}], "name": "callset", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "num", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}]