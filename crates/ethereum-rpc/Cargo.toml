[package]
name = "ethereum-rpc"
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
repository = { workspace = true }

version = { workspace = true }
publish = false
readme = "README.md"
resolver = "2"

[dependencies]
# 3rd-party dependencies
async-trait = { workspace = true }
borsh = { workspace = true }
citrea-evm = { path = "../evm", features = ["native"] }
citrea-primitives = { path = "../primitives" }
citrea-sequencer = { path = "../sequencer" }
futures = { workspace = true }
jsonrpsee = { workspace = true, features = ["http-client", "server"] }
parking_lot = { workspace = true }
rustc_version_runtime = { workspace = true }
schnellru = "0.2.1"
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }

# Reth deps
alloy-consensus = { workspace = true }
alloy-network = { workspace = true }
alloy-primitives = { workspace = true }
alloy-rpc-types = { workspace = true }
alloy-rpc-types-eth = { workspace = true }
alloy-rpc-types-trace = { workspace = true }
alloy-serde = { workspace = true }
reth-primitives = { workspace = true }
reth-rpc-eth-api = { workspace = true }
reth-rpc-eth-types = { workspace = true }
reth-rpc-types-compat = { workspace = true }

# Sovereign-SDK deps
sov-db = { path = "../../crates/sovereign-sdk/full-node/db/sov-db" }
sov-ledger-rpc = { path = "../sovereign-sdk/full-node/sov-ledger-rpc", features = ["client"] }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api", default-features = false }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface", features = ["native"] }
sov-state = { path = "../sovereign-sdk/module-system/sov-state", default-features = false }

[dev-dependencies]
tokio = { workspace = true }
