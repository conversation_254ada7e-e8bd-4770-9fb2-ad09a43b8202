[package]
name = "citrea-common"
authors = { workspace = true }
edition = { workspace = true }
homepage = { workspace = true }
license = { workspace = true }
repository = { workspace = true }

version = { workspace = true }
publish = false
resolver = "2"

[dependencies]
# 3rd-party deps
alloy-consensus = { workspace = true }
alloy-primitives = { workspace = true }
alloy-sol-types = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
backoff = { workspace = true }
borsh = { workspace = true }
futures = { workspace = true }
hex = { workspace = true }
hyper = { workspace = true }
jsonrpsee = { workspace = true, features = ["http-client", "server"] }
lru = { workspace = true }
metrics = { workspace = true }
reth-primitives = { workspace = true }
reth-tasks = { workspace = true }
rocksdb = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
toml = { workspace = true }
tower = { workspace = true }
tower-http = { workspace = true }
tracing = { workspace = true }

# Sov SDK deps
sov-db = { path = "../sovereign-sdk/full-node/db/sov-db" }
sov-keys = { path = "../sovereign-sdk/module-system/sov-keys" }
sov-ledger-rpc = { path = "../sovereign-sdk/full-node/sov-ledger-rpc", features = ["client"] }
sov-mock-da = { path = "../sovereign-sdk/adapters/mock-da" }
sov-modules-api = { path = "../sovereign-sdk/module-system/sov-modules-api" }
sov-modules-stf-blueprint = { path = "../sovereign-sdk/module-system/sov-modules-stf-blueprint", features = ["native"] }
sov-prover-storage-manager = { path = "../sovereign-sdk/full-node/sov-prover-storage-manager" }
sov-rollup-interface = { path = "../sovereign-sdk/rollup-interface" }
sov-schema-db = { path = "../sovereign-sdk/full-node/db/sov-schema-db" }
sov-state = { path = "../sovereign-sdk/module-system/sov-state" }
# sov-stf-runner = { path = "../sovereign-sdk/full-node/sov-stf-runner", features = ["native"] }

# Citrea
citrea-evm = { path = "../evm" }
citrea-primitives = { path = "../primitives/" }
citrea-stf = { path = "../citrea-stf", features = ["native"] }

[dev-dependencies]
sov-mock-da = { path = "../sovereign-sdk/adapters/mock-da", features = ["native"] }
tempfile = { workspace = true }
