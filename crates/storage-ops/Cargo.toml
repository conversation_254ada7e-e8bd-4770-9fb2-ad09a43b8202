[package]
name = "citrea-storage-ops"
version.workspace = true
authors.workspace = true
edition.workspace = true
homepage.workspace = true
license.workspace = true
publish.workspace = true
repository.workspace = true

[dependencies]
# Citrea dependencies
citrea-common = { path = "../common" }

# Sov SDK deps
jmt = { workspace = true }
sov-db = { path = "../sovereign-sdk/full-node/db/sov-db" }
sov-rollup-interface = { path = "../../crates/sovereign-sdk/rollup-interface" }
sov-schema-db = { path = "../sovereign-sdk/full-node/db/sov-schema-db" }

# 3rd-party dependencies
anyhow = { workspace = true }
derive_more = { workspace = true }
futures = { workspace = true }
hex = { workspace = true }
reth-tasks = { workspace = true }
serde = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }

[dev-dependencies]
sov-prover-storage-manager = { path = "../sovereign-sdk/full-node/sov-prover-storage-manager" }
sov-rollup-interface = { path = "../../crates/sovereign-sdk/rollup-interface", features = ["testing"] }
sov-state = { path = "../sovereign-sdk/module-system/sov-state", features = ["native"] }
tempfile = { workspace = true }
