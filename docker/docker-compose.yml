services:  
  citrea-bitcoin-testnet4:
    image: bitcoin/bitcoin:28.0
    container_name: bitcoin-testnet4
    ports:
      - "18443:18443"
      - "18444:18444"
    command:
      -printtoconsole
      -testnet4=1
      -rest
      -rpcbind=0.0.0.0
      -rpcallowip=0.0.0.0/0
      -rpcport=18443
      -rpcuser=citrea
      -rpcpassword=citrea
      -server
      -txindex=1
    volumes:
      - bitcoin-testnet4:/home/<USER>/.bitcoin
    networks:
      - citrea-testnet-network

  
  citrea-full-node:
    depends_on:
      - citrea-bitcoin-testnet4
    image: chainwayxyz/citrea-full-node:testnet
    platform: linux/amd64
    container_name: full-node
    environment:
      - SEQUENCER_PUBLIC_KEY=0201edff3b3ee593dbef54e2fbdd421070db55e2de2aebe75f398bd85ac97ed364
      - SEQUENCER_DA_PUB_KEY=03015a7c4d2cc1c771198686e2ebef6fe7004f4136d61f6225b061d1bb9b821b9b
      - PROVER_DA_PUB_KEY=0357d255ab93638a2d880787ebaadfefdfc9bb51a26b4a37e5d588e04e54c60a42
      - NODE_URL=http://citrea-bitcoin-testnet4:18443/
      - NODE_USERNAME=citrea
      - NODE_PASSWORD=citrea
      - NETWORK=testnet
      - TX_BACKUP_DIR=
      - STORAGE_PATH=/mnt/task/citrea-db
      - DB_MAX_OPEN_FILES=5000
      - RPC_BIND_HOST=0.0.0.0
      - RPC_BIND_PORT=8080
      - RPC_MAX_CONNECTIONS=100
      - RPC_MAX_REQUEST_BODY_SIZE=10485760
      - RPC_MAX_RESPONSE_BODY_SIZE=10485760
      - RPC_BATCH_REQUESTS_LIMIT=50
      - RPC_ENABLE_SUBSCRIPTIONS=true
      - RPC_MAX_SUBSCRIPTIONS_PER_CONNECTION=10
      - SEQUENCER_CLIENT_URL=https://rpc.testnet.citrea.xyz
      - INCLUDE_TX_BODY=false
      - SCAN_L1_START_HEIGHT=45496
      - SYNC_BLOCKS_COUNT=10
      - RUST_LOG=info
      - JSON_LOGS=1
      - RISC0_DEV_MODE=1
    ports:
      - "8080:8080"
    volumes:
      - citrea-full-node:/mnt/task/citrea-db
    networks:
      - citrea-testnet-network

volumes:
  bitcoin-testnet4:
  citrea-full-node:


networks:
  citrea-testnet-network:
    driver: bridge
