[public_keys]
sequencer_public_key = "03745871636b11562a7f2d7c0e883a960b54c7e2c0a5427d4b99ac403588530589"
sequencer_da_pub_key = "039cd55f9b3dcf306c4d54f66cd7c4b27cc788632cd6fb73d80c99d303c6536486"
prover_da_pub_key = "03fc6fb2ef68368009c895d2d4351dcca4109ec2f5f327291a0553570ce769f5e5"

[da]
# can change this
node_url = ""
# fill here
node_username = ""
# fill here
node_password = ""

[storage]
# The path to the rollup's data directory. Paths that do not begin with `/` are interpreted as relative paths.
path = "resources/dbs/full-node-db"
db_max_open_files = 5000

[rpc]
# the host and port to bind the rpc server for
bind_host = "0.0.0.0"
bind_port = 12346
enable_subscriptions = true
max_subscriptions_per_connection = 100

[runner]
sequencer_client_url = "https://rpc.devnet.citrea.xyz"
# set this to true if you want to include soft confirmation tx bodies
include_tx_body = false
scan_l1_start_height = 256

# WARNING: State pruning is not completely implemented.
# Enabling this might lead to state corruption and therefore,
# avoid using it for now.
# [runner.pruning_config]
# distance = 6000
