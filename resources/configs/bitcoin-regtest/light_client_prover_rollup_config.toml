[public_keys]
sequencer_public_key = "036360e856310ce5d294e8be33fc807077dc56ac80d95d9cd4ddbd21325eff73f7"
sequencer_da_pub_key = "02588d202afcc1ee4ab5254c7847ec25b9a135bbda0f2bc69ee1a714749fd77dc9"
prover_da_pub_key = "03eedab888e45f3bdc3ec9918c491c11e5cf7af0a91f38b97fbc1e135ae4056601"

[da]
# fill here
node_url = ""
# fill here
node_username = ""
# fill here
node_password = ""
tx_backup_dir = "resources/bitcoin/inscription_txs"

[storage]
# The path to the rollup's data directory. Paths that do not begin with `/` are interpreted as relative paths.
path = "resources/dbs/light-client-prover-db"
db_max_open_files = 5000

[rpc]
# the host and port to bind the rpc server for
bind_host = "127.0.0.1"
bind_port = 12346
enable_subscriptions = false
