private_key = "1212121212121212121212121212121212121212121212121212121212121212"
max_l2_blocks_per_commitment = 10
test_mode = false
deposit_mempool_fetch_limit = 10
block_production_interval_ms = 1000
da_update_interval_ms = 2000
bridge_initialize_params = "000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000c00000000000000000000000000000000000000000000000008ac7230489e80000000000000000000000000000000000000000000000000000000000000000002d4a209fb3a961d8b1f4ec1caa220c6a50b815febc0b689ddf0b9ddfbf99cb74479e41ac0063066369747265611400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a08000000003b9aca006800000000000000000000000000000000000000000000"

[mempool_conf] # Mempool Configuration - https://github.com/ledgerwatch/erigon/wiki/Transaction-Pool-Design
pending_tx_limit = 100000
pending_tx_size = 200
queue_tx_limit = 100000
queue_tx_size = 200
base_fee_tx_limit = 100000
base_fee_tx_size = 200
max_account_slots = 16
