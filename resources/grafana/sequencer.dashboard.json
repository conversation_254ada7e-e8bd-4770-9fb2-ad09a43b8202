{"__inputs": [{"name": "DS_PROMETHEUS", "label": "prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "12.0.1+security-01"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}, {"type": "panel", "id": "trend", "name": "Trend", "version": ""}, {"type": "panel", "id": "<PERSON><PERSON><PERSON>", "name": "XY Chart", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_current_l2_block", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Current L2 Block", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_current_l1_block", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Current L1 Block", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 31, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "rate(sequencer_deposit_data_mempool_txs_inc[1m])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "Num Of Deposit Txs In A Minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 5, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_mempool_txs", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Number Of Txs In Mempool", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The duration eth_call for deposit txs", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 30, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_deposit_tx_call_duration", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "Deposit Tx Call Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 50, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "pointShape": "circle", "pointSize": {"fixed": 5}, "pointStrokeWidth": 1, "scaleDistribution": {"type": "linear"}, "show": "points+lines"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 24, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "mapping": "auto", "series": [{"x": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_current_l2_block\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}, "y": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"evm_block_base_fee\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}}], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "evm_block_base_fee", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_current_l2_block", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}], "title": "Base Fee Per Block Number", "transformations": [{"id": "joinByField", "options": {}}], "type": "<PERSON><PERSON><PERSON>"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 37, "interval": "2s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "rate(rpc_requests_total[5m])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "Per-second average rate of RPC requests over the last 5 minutes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 33, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_unaccepted_deposit_txs", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "Unaccepted Deposit Txs", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.7, "drawStyle": "line", "fillOpacity": 46, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 3, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 22, "interval": "2s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xField": "{__name__=\"sequencer_current_l2_block\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_current_l2_block", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_dry_run_preparation_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "D", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_dry_run_system_txs_duration_secs", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "E", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_dry_run_execution_gauge", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "F", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_begin_l2_block_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "G", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_encode_and_sign_sov_tx_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "H", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_apply_l2_block_txs_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "I", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_end_l2_block_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "J", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_finalize_l2_block_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "K", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_calculate_tx_merkle_root_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_sign_l2_block_header_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "L", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_save_l2_block_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "M", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_maintain_mempool_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "C", "useBackend": false}], "title": "Stacked Sequencer Block Production Flow", "transformations": [{"id": "joinByField", "options": {}}], "type": "trend"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "bitcoin_da_transaction_queue_processing_time", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Bitcoin DA Queue Processing Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}, "id": 35, "interval": "2s", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_latest_sequencer_commitment_index", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Latest Commitment Index", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_latest_sequencer_commitment_l2_start_height", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Latest Commitment Start L2 Height", "range": true, "refId": "B", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_latest_sequencer_commitment_l2_end_height", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Latest Commitment End L2 Height", "range": true, "refId": "C", "useBackend": false}], "title": "Latest Commitment Data", "transformations": [{"id": "joinByField", "options": {}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "id": 29, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_deposit_data_mempool_txs", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "Number of txs in deposit data mempool", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 50, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "pointShape": "circle", "pointSize": {"fixed": 5}, "pointStrokeWidth": 1, "scaleDistribution": {"type": "linear"}, "show": "points+lines"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 48}, "id": 36, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "mapping": "auto", "series": [{}], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_current_l2_block", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_l1_fee_failed_txs_count", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}], "title": "L1 Fee Failed Txs Count By Block Number", "transformations": [{"id": "joinByField", "options": {}}], "type": "<PERSON><PERSON><PERSON>"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Complete Block Production Time(secs) By Block Num", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 21, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": true, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 0.001}, {"color": "red", "value": 120000}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}, "id": 1, "interval": "2s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_current_l2_block", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_block_production_time", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}], "title": "No Dry Run Block Production Time(secs) By Block Num", "transformations": [{"id": "joinByField", "options": {"byField": "Time", "mode": "outer"}}], "type": "trend"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 50, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "pointShape": "circle", "pointSize": {"fixed": 5}, "pointStrokeWidth": 1, "scaleDistribution": {"type": "linear"}, "show": "points+lines"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 56}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "mapping": "auto", "series": [{"x": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_latest_sequencer_commitment_index\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}, "y": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_latest_sequencer_commitment_process_duration_secs\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}}], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_latest_sequencer_commitment_index", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_latest_sequencer_commitment_process_duration_secs", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "D", "useBackend": false}], "title": "Commitment Process Duration By Index", "transformations": [{"id": "joinByField", "options": {}}], "type": "<PERSON><PERSON><PERSON>"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 56}, "id": 21, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_dry_run_execution", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Total Dry Run Execution Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 64}, "id": 32, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_deposit_tx_size", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "Deposit Tx Size", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["{__name__=\"rpc_response_time_seconds\", instance=\"host.docker.internal:8001\", job=\"sequencer\", method=\"ledger_getHeadL2BlockHeight\", quantile=\"0.5\", success=\"true\"}"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 64}, "id": 27, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "rpc_response_time_seconds", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "Rpc Response Times", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 50, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "pointShape": "circle", "pointSize": {"fixed": 5}, "pointStrokeWidth": 1, "scaleDistribution": {"type": "linear"}, "show": "points+lines"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 72}, "id": 25, "interval": "1s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "mapping": "auto", "series": [{"x": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_current_l2_block\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}, "y": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_l1_fee_rate\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}}], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_current_l2_block", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}, {"disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_l1_fee_rate", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "L1 Fee Rate Per Block", "transformations": [{"id": "joinByField", "options": {}}], "type": "<PERSON><PERSON><PERSON>"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 72}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_commitment_mine_da_transaction", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "Mine Sequencer Commitment Tx Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 50, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "pointShape": "circle", "pointSize": {"fixed": 5}, "pointStrokeWidth": 1, "scaleDistribution": {"type": "linear"}, "show": "points+lines"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 80}, "id": 23, "interval": "1s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "mapping": "auto", "series": [{"x": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_current_l2_block\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}, "y": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"evm_block_gas_usage\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}}], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "evm_block_gas_usage", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_current_l2_block", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}], "title": "Evm Gas Usage Per Block Num", "transformations": [{"id": "joinByField", "options": {}}], "type": "<PERSON><PERSON><PERSON>"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 80}, "id": 28, "interval": "1s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "code", "expr": "rate(sequencer_mempool_txs_inc[1m])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}}], "title": "Number of txs coming to the mempool in the last minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "This does not work perfect in scenarios where there are over couple hundered missed DA blocks, and new l2 blocks are produced for every 10 L1 block instantly", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 100, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "pointShape": "circle", "pointSize": {"fixed": 5}, "pointStrokeWidth": 1, "scaleDistribution": {"type": "linear"}, "show": "points+lines"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 88}, "id": 2, "interval": "1", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "mapping": "auto", "series": [{"x": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_current_l2_block\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}, "y": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_l2_block_tx_count\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}}], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_current_l2_block", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_l2_block_tx_count", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}], "title": "Tx Count By Block Number", "transformations": [{"id": "joinByField", "options": {}}], "type": "<PERSON><PERSON><PERSON>"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 50, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "pointShape": "circle", "pointSize": {"fixed": 5}, "pointStrokeWidth": 1, "scaleDistribution": {"type": "linear"}, "show": "points+lines"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 88}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "mapping": "auto", "series": [{"frame": {"matcher": {"id": "byIndex", "options": 0}}, "x": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_currently_committing_index\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}, "y": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"sequencer_commitment_blocks_count\", instance=\"host.docker.internal:8001\", job=\"sequencer\"}"}}}], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_currently_committing_index", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_commitment_blocks_count", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}], "title": "Commitment Block Count By Index", "transformations": [{"id": "joinByField", "options": {}}], "type": "<PERSON><PERSON><PERSON>"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 96}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sequencer_block_production_execution", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Complete Produce L2 Block", "type": "timeseries"}], "refresh": "", "schemaVersion": 41, "tags": [], "templating": {"list": []}, "time": {"from": "now-30s", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Sequencer Dashboard", "uid": "d1faed80-531c-4c31-bd06-acc46d388c84", "version": 10, "weekStart": ""}