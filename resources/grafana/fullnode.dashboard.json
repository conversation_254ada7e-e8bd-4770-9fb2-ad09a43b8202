{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 3, "interval": "1s", "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_current_l2_block", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Current L2 Block", "range": true, "refId": "B", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_current_l1_block", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Current L1 Block", "range": true, "refId": "A", "useBackend": false}], "title": "Current L1 and L2 Block", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "interval": "1s", "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_highest_committed_index", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Highest Committed Index", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "bes3v6ugacbnkd"}, "disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_highest_committed_l2_height", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Highest Committed L2 Height", "range": true, "refId": "B", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "bes3v6ugacbnkd"}, "disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_highest_proven_l2_height", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Highest Proven L2 Height", "range": true, "refId": "C", "useBackend": false}], "title": "Highest Committed Index, L2 Height, Proven L2 Height", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "full_node_rpc_response_time", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Rpc Response Times", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 50, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "pointShape": "circle", "pointSize": {"fixed": 5}, "pointStrokeWidth": 1, "scaleDistribution": {"type": "linear"}, "show": "points+lines"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 4, "interval": "1s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "mapping": "auto", "series": [{"x": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"fullnode_current_l1_block\", instance=\"host.docker.internal:8002\", job=\"fullnode\"}"}}, "y": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"fullnode_scan_l1_block\", instance=\"host.docker.internal:8002\", job=\"fullnode\"}"}}}], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "bes3v6ugacbnkd"}, "disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_current_l1_block", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}, {"disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_scan_l1_block", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}], "title": "Scan L1 Block Time By L1 Height", "transformations": [{"id": "joinByField", "options": {}}], "type": "<PERSON><PERSON><PERSON>"}, {"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_sequencer_commitment_processing_time", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Commitment Processing Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_process_l2_block", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Process L2 Block Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_batch_proof_processing_time", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Batch Proof Processing Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 50, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "pointShape": "circle", "pointSize": {"fixed": 5}, "pointStrokeWidth": 1, "scaleDistribution": {"type": "linear"}, "show": "points+lines"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 1, "interval": "1s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "mapping": "auto", "series": [{"x": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"fullnode_current_l2_block\", instance=\"host.docker.internal:8002\", job=\"fullnode\"}"}}, "y": {"matcher": {"id": "by<PERSON><PERSON>", "options": "{__name__=\"fullnode_l2_block_size\", instance=\"host.docker.internal:8002\", job=\"fullnode\", quantile=\"0.0\"}"}}}], "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"datasource": {"type": "prometheus", "uid": "bes3v6ugacbnkd"}, "disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_l2_block_size", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "bes3v6ugacbnkd"}, "disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_current_l2_block", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "useBackend": false}], "title": "Block Size Per Block Num", "transformations": [{"id": "joinByField", "options": {}}], "type": "<PERSON><PERSON><PERSON>"}, {"datasource": {"type": "prometheus", "uid": "eesojxal9f11cb"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.1+security-01", "targets": [{"disableTextWrap": false, "editorMode": "builder", "expr": "fullnode_l2_block_size", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "L2 Block Size Bytes", "type": "timeseries"}], "preload": false, "schemaVersion": 41, "tags": [], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Full Node Dashboard", "uid": "146a0ace-198d-4cf1-b537-46b24ad05d4b", "version": 4}