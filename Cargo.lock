# This file is automatically @generated by Cargo.
# It is not intended for manual editing.
version = 4

[[package]]
name = "addr2line"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfbe277e56a376000877090da837660b4427aad530e3028d44e0bffe4f89a1c1"
dependencies = [
 "gimli",
]

[[package]]
name = "adler2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "512761e0bb2578dd7380c6baaa0f4ce03e84f95e960231d1dec8bf4d7d6e2627"

[[package]]
name = "aead"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d122413f284cf2d62fb1b7db97e02edb8cda96d769b16e443a4f6195e35662b0"
dependencies = [
 "crypto-common",
 "generic-array",
]

[[package]]
name = "aes"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b169f7a6d4742236a0a00c541b845991d0ac43e546831af1249753ab4c3aa3a0"
dependencies = [
 "cfg-if",
 "cipher",
 "cpufeatures",
]

[[package]]
name = "aes-gcm"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "831010a0f742e1209b3bcea8fab6a8e149051ba6099432c8cb2cc117dec3ead1"
dependencies = [
 "aead",
 "aes",
 "cipher",
 "ctr",
 "ghash",
 "subtle",
]

[[package]]
name = "ahash"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89da841a80418a9b391ebaea17f5c112ffaaa96f621d2c285b5174da76b9011"
dependencies = [
 "cfg-if",
 "getrandom 0.2.15",
 "once_cell",
 "version_check",
 "zerocopy 0.7.35",
]

[[package]]
name = "aho-corasick"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e60d3430d3a69478ad0993f19238d2df97c507009a52b3c10addcd7f6bcb916"
dependencies = [
 "memchr",
]

[[package]]
name = "alloc-no-stdlib"
version = "2.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc7bb162ec39d46ab1ca8c77bf72e890535becd1751bb45f64c597edb4c8c6b3"

[[package]]
name = "alloc-stdlib"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94fb8275041c72129eb51b7d0322c29b8387a0386127718b096429201a5d6ece"
dependencies = [
 "alloc-no-stdlib",
]

[[package]]
name = "allocator-api2"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "683d7910e743518b0e34f1186f92494becacb047c7b6bf616c96772180fef923"

[[package]]
name = "alloy"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "239e728d663a3bdababa24dfdc697faec987593161c5ff54d72ee01df6721d59"
dependencies = [
 "alloy-consensus",
 "alloy-core",
 "alloy-eips",
 "alloy-network",
 "alloy-provider",
 "alloy-rpc-client",
 "alloy-rpc-types",
 "alloy-serde",
 "alloy-signer",
 "alloy-signer-local",
 "alloy-transport",
 "alloy-transport-http",
]

[[package]]
name = "alloy-chains"
version = "0.1.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28e2652684758b0d9b389d248b209ed9fd9989ef489a550265fe4bb8454fe7eb"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "num_enum",
 "serde",
 "strum 0.27.1",
]

[[package]]
name = "alloy-consensus"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "27d301f5bcfd37e3aac727c360d8b50c33ddff9169ce0370198dedda36a9927d"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-serde",
 "alloy-trie",
 "auto_impl",
 "c-kzg",
 "derive_more 2.0.1",
 "either",
 "k256",
 "once_cell",
 "rand 0.8.5",
 "serde",
 "serde_with",
 "thiserror 2.0.12",
]

[[package]]
name = "alloy-consensus-any"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f4f97a85a45965e0e4f9f5b94bbafaa3e4ee6868bdbcf2e4a9acb4b358038fe"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-serde",
 "serde",
]

[[package]]
name = "alloy-core"
version = "0.8.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca1380cc3c81b83d5234865779494970c83b5893b423c59cdd68c3cd1ed0b671"
dependencies = [
 "alloy-primitives",
]

[[package]]
name = "alloy-dyn-abi"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb8e762aefd39a397ff485bc86df673465c4ad3ec8819cc60833a8a3ba5cdc87"
dependencies = [
 "alloy-json-abi",
 "alloy-primitives",
 "alloy-sol-type-parser",
 "alloy-sol-types",
 "const-hex",
 "derive_more 2.0.1",
 "itoa",
 "serde",
 "serde_json",
 "winnow",
]

[[package]]
name = "alloy-eip2124"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "675264c957689f0fd75f5993a73123c2cc3b5c235a38f5b9037fe6c826bfb2c0"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "crc",
 "serde",
 "thiserror 2.0.12",
]

[[package]]
name = "alloy-eip2930"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0069cf0642457f87a01a014f6dc29d5d893cd4fd8fddf0c3cdfad1bb3ebafc41"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "serde",
]

[[package]]
name = "alloy-eip7702"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b15b13d38b366d01e818fe8e710d4d702ef7499eacd44926a06171dd9585d0c"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "k256",
 "serde",
 "serde_with",
 "thiserror 2.0.12",
]

[[package]]
name = "alloy-eips"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10b11c382ca8075128d1ae6822b60921cf484c911d9a5831797a01218f98125f"
dependencies = [
 "alloy-eip2124",
 "alloy-eip2930",
 "alloy-eip7702",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-serde",
 "auto_impl",
 "c-kzg",
 "derive_more 2.0.1",
 "either",
 "ethereum_ssz",
 "ethereum_ssz_derive",
 "serde",
 "sha2 0.10.8",
]

[[package]]
name = "alloy-evm"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e158fd08c4be4fafe8c9fb7cb661f3b9585038446df0cd20b3d99e71f4166748"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-hardforks",
 "alloy-primitives",
 "alloy-sol-types",
 "auto_impl",
 "derive_more 2.0.1",
 "op-alloy-consensus",
 "op-revm",
 "revm",
 "thiserror 2.0.12",
]

[[package]]
name = "alloy-genesis"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bd9e75c5dd40319ebbe807ebe9dfb10c24e4a70d9c7d638e62921d8dd093c8b"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "alloy-serde",
 "alloy-trie",
 "serde",
]

[[package]]
name = "alloy-hardforks"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb3a420b513e00937442db75c5c9c8287fd0615a1f60cc3335d7246c870440ed"
dependencies = [
 "alloy-chains",
 "alloy-eip2124",
 "alloy-primitives",
 "auto_impl",
 "dyn-clone",
 "serde",
]

[[package]]
name = "alloy-json-abi"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe6beff64ad0aa6ad1019a3db26fef565aefeb011736150ab73ed3366c3cfd1b"
dependencies = [
 "alloy-primitives",
 "alloy-sol-type-parser",
 "serde",
 "serde_json",
]

[[package]]
name = "alloy-json-rpc"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbcf26d02a72e23d5bc245425ea403c93ba17d254f20f9c23556a249c6c7e143"
dependencies = [
 "alloy-primitives",
 "alloy-sol-types",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
 "tracing",
]

[[package]]
name = "alloy-network"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b44dd4429e190f727358571175ebf323db360a303bf4e1731213f510ced1c2e6"
dependencies = [
 "alloy-consensus",
 "alloy-consensus-any",
 "alloy-eips",
 "alloy-json-rpc",
 "alloy-network-primitives",
 "alloy-primitives",
 "alloy-rpc-types-any",
 "alloy-rpc-types-eth",
 "alloy-serde",
 "alloy-signer",
 "alloy-sol-types",
 "async-trait",
 "auto_impl",
 "derive_more 2.0.1",
 "futures-utils-wasm",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
]

[[package]]
name = "alloy-network-primitives"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86f736e1d1eb1b770dbd32919bdf46d4dcd4617f2eed07947dfb32649962baba"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-serde",
 "serde",
]

[[package]]
name = "alloy-primitives"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c77490fe91a0ce933a1f219029521f20fc28c2c0ca95d53fa4da9c00b8d9d4e"
dependencies = [
 "alloy-rlp",
 "bytes",
 "cfg-if",
 "const-hex",
 "derive_more 2.0.1",
 "foldhash",
 "getrandom 0.2.15",
 "hashbrown 0.15.2",
 "indexmap 2.7.1",
 "itoa",
 "k256",
 "keccak-asm",
 "paste",
 "proptest",
 "rand 0.8.5",
 "ruint",
 "rustc-hash 2.1.1",
 "serde",
 "sha3",
 "tiny-keccak",
]

[[package]]
name = "alloy-provider"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a557f9e3ec89437b06db3bfc97d20782b1f7cc55b5b602b6a82bf3f64d7efb0e"
dependencies = [
 "alloy-chains",
 "alloy-consensus",
 "alloy-eips",
 "alloy-json-rpc",
 "alloy-network",
 "alloy-network-primitives",
 "alloy-primitives",
 "alloy-rpc-client",
 "alloy-rpc-types-eth",
 "alloy-signer",
 "alloy-sol-types",
 "alloy-transport",
 "alloy-transport-http",
 "async-stream",
 "async-trait",
 "auto_impl",
 "dashmap",
 "either",
 "futures",
 "futures-utils-wasm",
 "lru 0.13.0",
 "parking_lot",
 "pin-project",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "url",
 "wasmtimer",
]

[[package]]
name = "alloy-rlp"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d6c1d995bff8d011f7cd6c81820d51825e6e06d6db73914c1630ecf544d83d6"
dependencies = [
 "alloy-rlp-derive",
 "arrayvec",
 "bytes",
]

[[package]]
name = "alloy-rlp-derive"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a40e1ef334153322fd878d07e86af7a529bcb86b2439525920a88eba87bcf943"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "alloy-rpc-client"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cec6dc89c4c3ef166f9fa436d1831f8142c16cf2e637647c936a6aaaabd8d898"
dependencies = [
 "alloy-json-rpc",
 "alloy-primitives",
 "alloy-transport",
 "alloy-transport-http",
 "async-stream",
 "futures",
 "pin-project",
 "serde",
 "serde_json",
 "tokio",
 "tokio-stream",
 "tower 0.5.2",
 "tracing",
 "tracing-futures",
 "url",
 "wasmtimer",
]

[[package]]
name = "alloy-rpc-types"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3849f8131a18cc5d7f95f301d68a6af5aa2db28ad8522fb9db1f27b3794e8b68"
dependencies = [
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "alloy-rpc-types-eth",
 "alloy-serde",
 "serde",
]

[[package]]
name = "alloy-rpc-types-admin"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d13e905b0348666e10119d39b1ffb7ab4e000b4f4e5ffed920b57f8745b2440"
dependencies = [
 "alloy-genesis",
 "alloy-primitives",
 "serde",
 "serde_json",
]

[[package]]
name = "alloy-rpc-types-anvil"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19051fd5e8de7e1f95ec228c9303debd776dcc7caf8d1ece3191f711f5c06541"
dependencies = [
 "alloy-primitives",
 "alloy-rpc-types-eth",
 "alloy-serde",
 "serde",
]

[[package]]
name = "alloy-rpc-types-any"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ecd6d480e4e6e456f30eeeb3aef1512aaecb68df2a35d1f78865dbc4d20dc0fd"
dependencies = [
 "alloy-consensus-any",
 "alloy-rpc-types-eth",
 "alloy-serde",
]

[[package]]
name = "alloy-rpc-types-beacon"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b821fd7c93738d5ec972d4d329eb05c896721f467556fbae171294ddd9ac829"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "ethereum_ssz",
 "ethereum_ssz_derive",
 "serde",
 "serde_with",
 "thiserror 2.0.12",
 "tree_hash",
 "tree_hash_derive",
]

[[package]]
name = "alloy-rpc-types-debug"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "805eb9fa07f92f1225253e842b5454b4b3e258813445c1a1c9d8dd0fd90817c1"
dependencies = [
 "alloy-primitives",
 "serde",
]

[[package]]
name = "alloy-rpc-types-engine"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "689521777149dabe210ef122605fb00050e038f2e85b8c9897534739f1a904f8"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-serde",
 "derive_more 2.0.1",
 "ethereum_ssz",
 "ethereum_ssz_derive",
 "jsonrpsee-types",
 "jsonwebtoken",
 "rand 0.8.5",
 "serde",
 "strum 0.27.1",
]

[[package]]
name = "alloy-rpc-types-eth"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a8b6d55bdaa0c4a08650d4b32f174494cbade56adf6f2fcfa2a4f3490cb5511"
dependencies = [
 "alloy-consensus",
 "alloy-consensus-any",
 "alloy-eips",
 "alloy-network-primitives",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-serde",
 "alloy-sol-types",
 "itertools 0.14.0",
 "jsonrpsee-types",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
]

[[package]]
name = "alloy-rpc-types-mev"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93d1e3fbbf9b2eb2509546b4e47f67ee8a3b246ef3f7eb678bcb97d399c755b4"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-eth",
 "alloy-serde",
 "serde",
 "serde_json",
]

[[package]]
name = "alloy-rpc-types-trace"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6019cd6a89230d765a621a7b1bc8af46a6a9cde2d2e540e6f9ce930e0fb7c6db"
dependencies = [
 "alloy-primitives",
 "alloy-rpc-types-eth",
 "alloy-serde",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
]

[[package]]
name = "alloy-rpc-types-txpool"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee36e5404642696af511f09991f9f54a11b90e86e55efad868f8f56350eff5b0"
dependencies = [
 "alloy-primitives",
 "alloy-rpc-types-eth",
 "alloy-serde",
 "serde",
]

[[package]]
name = "alloy-serde"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1824791912f468a481dedc1db50feef3e85a078f6d743a62db2ee9c2ca674882"
dependencies = [
 "alloy-primitives",
 "serde",
 "serde_json",
]

[[package]]
name = "alloy-signer"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d087fe5aea96a93fbe71be8aaed5c57c3caac303c09e674bc5b1647990d648b"
dependencies = [
 "alloy-primitives",
 "async-trait",
 "auto_impl",
 "either",
 "elliptic-curve",
 "k256",
 "thiserror 2.0.12",
]

[[package]]
name = "alloy-signer-local"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2940353d2425bb75965cd5101075334e6271051e35610f903bf8099a52b0b1a9"
dependencies = [
 "alloy-consensus",
 "alloy-network",
 "alloy-primitives",
 "alloy-signer",
 "async-trait",
 "k256",
 "rand 0.8.5",
 "thiserror 2.0.12",
]

[[package]]
name = "alloy-sol-macro"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e10ae8e9a91d328ae954c22542415303919aabe976fe7a92eb06db1b68fd59f2"
dependencies = [
 "alloy-sol-macro-expander",
 "alloy-sol-macro-input",
 "proc-macro-error2",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "alloy-sol-macro-expander"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83ad5da86c127751bc607c174d6c9fe9b85ef0889a9ca0c641735d77d4f98f26"
dependencies = [
 "alloy-json-abi",
 "alloy-sol-macro-input",
 "const-hex",
 "heck 0.5.0",
 "indexmap 2.7.1",
 "proc-macro-error2",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "syn-solidity",
 "tiny-keccak",
]

[[package]]
name = "alloy-sol-macro-input"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba3d30f0d3f9ba3b7686f3ff1de9ee312647aac705604417a2f40c604f409a9e"
dependencies = [
 "alloy-json-abi",
 "const-hex",
 "dunce",
 "heck 0.5.0",
 "macro-string",
 "proc-macro2",
 "quote",
 "serde_json",
 "syn 2.0.98",
 "syn-solidity",
]

[[package]]
name = "alloy-sol-type-parser"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d162f8524adfdfb0e4bd0505c734c985f3e2474eb022af32eef0d52a4f3935c"
dependencies = [
 "serde",
 "winnow",
]

[[package]]
name = "alloy-sol-types"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d43d5e60466a440230c07761aa67671d4719d46f43be8ea6e7ed334d8db4a9ab"
dependencies = [
 "alloy-json-abi",
 "alloy-primitives",
 "alloy-sol-macro",
 "const-hex",
 "serde",
]

[[package]]
name = "alloy-transport"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6818b4c82a474cc01ac9e88ccfcd9f9b7bc893b2f8aea7e890a28dcd55c0a7aa"
dependencies = [
 "alloy-json-rpc",
 "base64 0.22.1",
 "derive_more 2.0.1",
 "futures",
 "futures-utils-wasm",
 "parking_lot",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
 "tokio",
 "tower 0.5.2",
 "tracing",
 "url",
 "wasmtimer",
]

[[package]]
name = "alloy-transport-http"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cc3079a33483afa1b1365a3add3ea3e21c75b10f704870198ba7846627d10f2"
dependencies = [
 "alloy-json-rpc",
 "alloy-transport",
 "http-body-util",
 "hyper",
 "hyper-util",
 "serde_json",
 "tower 0.5.2",
 "tracing",
 "url",
]

[[package]]
name = "alloy-trie"
version = "0.7.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d95a94854e420f07e962f7807485856cde359ab99ab6413883e15235ad996e8b"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "arrayvec",
 "derive_more 1.0.0",
 "nybbles",
 "serde",
 "smallvec",
 "tracing",
]

[[package]]
name = "android-tzdata"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999941b234f3131b00bc13c22d06e8c5ff726d1b6318ac7eb276997bbb4fef0"

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc",
]

[[package]]
name = "anes"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b46cbb362ab8752921c97e041f5e366ee6297bd428a31275b9fcf1e380f7299"

[[package]]
name = "anstream"
version = "0.6.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8acc5369981196006228e28809f761875c0327210a891e941f4c683b3a99529b"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon",
 "colorchoice",
 "is_terminal_polyfill",
 "utf8parse",
]

[[package]]
name = "anstyle"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55cc3b69f167a1ef2e161439aa98aed94e6028e5f9a59be9a6ffb47aef1651f9"

[[package]]
name = "anstyle-parse"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b2d16507662817a6a20a9ea92df6652ee4f94f914589377d69f3b21bc5798a9"
dependencies = [
 "utf8parse",
]

[[package]]
name = "anstyle-query"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79947af37f4177cfead1110013d678905c37501914fba0efea834c3fe9a8d60c"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "anstyle-wincon"
version = "3.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3534e77181a9cc07539ad51f2141fe32f6c3ffd4df76db8ad92346b003ae4e"
dependencies = [
 "anstyle",
 "once_cell",
 "windows-sys 0.59.0",
]

[[package]]
name = "anyhow"
version = "1.0.96"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b964d184e89d9b6b67dd2715bc8e74cf3107fb2b529990c90cf517326150bf4"

[[package]]
name = "aquamarine"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f50776554130342de4836ba542aa85a4ddb361690d7e8df13774d7284c3d5c2"
dependencies = [
 "include_dir",
 "itertools 0.10.5",
 "proc-macro-error2",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "arbitrary"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dde20b3d026af13f561bdd0f15edf01fc734f0dafcedbaf42bba506a9517f223"
dependencies = [
 "derive_arbitrary",
]

[[package]]
name = "ark-bls12-381"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3df4dcc01ff89867cd86b0da835f23c3f02738353aaee7dde7495af71363b8d5"
dependencies = [
 "ark-ec",
 "ark-ff 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
]

[[package]]
name = "ark-bn254"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d69eab57e8d2663efa5c63135b2af4f396d66424f88954c21104125ab6b3e6bc"
dependencies = [
 "ark-ec",
 "ark-ff 0.5.0",
 "ark-r1cs-std",
 "ark-std 0.5.0",
]

[[package]]
name = "ark-crypto-primitives"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e0c292754729c8a190e50414fd1a37093c786c709899f29c9f7daccecfa855e"
dependencies = [
 "ahash",
 "ark-crypto-primitives-macros",
 "ark-ec",
 "ark-ff 0.5.0",
 "ark-relations",
 "ark-serialize 0.5.0",
 "ark-snark",
 "ark-std 0.5.0",
 "blake2",
 "derivative",
 "digest 0.10.7",
 "fnv",
 "merlin",
 "sha2 0.10.8",
]

[[package]]
name = "ark-crypto-primitives-macros"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7e89fe77d1f0f4fe5b96dfc940923d88d17b6a773808124f21e764dfb063c6a"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "ark-ec"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43d68f2d516162846c1238e755a7c4d131b892b70cc70c471a8e3ca3ed818fce"
dependencies = [
 "ahash",
 "ark-ff 0.5.0",
 "ark-poly",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "educe",
 "fnv",
 "hashbrown 0.15.2",
 "itertools 0.13.0",
 "num-bigint",
 "num-integer",
 "num-traits",
 "zeroize",
]

[[package]]
name = "ark-ff"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b3235cc41ee7a12aaaf2c575a2ad7b46713a8a50bda2fc3b003a04845c05dd6"
dependencies = [
 "ark-ff-asm 0.3.0",
 "ark-ff-macros 0.3.0",
 "ark-serialize 0.3.0",
 "ark-std 0.3.0",
 "derivative",
 "num-bigint",
 "num-traits",
 "paste",
 "rustc_version 0.3.3",
 "zeroize",
]

[[package]]
name = "ark-ff"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec847af850f44ad29048935519032c33da8aa03340876d351dfab5660d2966ba"
dependencies = [
 "ark-ff-asm 0.4.2",
 "ark-ff-macros 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "digest 0.10.7",
 "itertools 0.10.5",
 "num-bigint",
 "num-traits",
 "paste",
 "rustc_version 0.4.1",
 "zeroize",
]

[[package]]
name = "ark-ff"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a177aba0ed1e0fbb62aa9f6d0502e9b46dad8c2eab04c14258a1212d2557ea70"
dependencies = [
 "ark-ff-asm 0.5.0",
 "ark-ff-macros 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "arrayvec",
 "digest 0.10.7",
 "educe",
 "itertools 0.13.0",
 "num-bigint",
 "num-traits",
 "paste",
 "zeroize",
]

[[package]]
name = "ark-ff-asm"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db02d390bf6643fb404d3d22d31aee1c4bc4459600aef9113833d17e786c6e44"
dependencies = [
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-asm"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ed4aa4fe255d0bc6d79373f7e31d2ea147bcf486cba1be5ba7ea85abdb92348"
dependencies = [
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-asm"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62945a2f7e6de02a31fe400aa489f0e0f5b2502e69f95f853adb82a96c7a6b60"
dependencies = [
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "ark-ff-macros"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db2fd794a08ccb318058009eefdf15bcaaaaf6f8161eb3345f907222bac38b20"
dependencies = [
 "num-bigint",
 "num-traits",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-macros"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7abe79b0e4288889c4574159ab790824d0033b9fdcb2a112a3182fac2e514565"
dependencies = [
 "num-bigint",
 "num-traits",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-macros"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09be120733ee33f7693ceaa202ca41accd5653b779563608f1234f78ae07c4b3"
dependencies = [
 "num-bigint",
 "num-traits",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "ark-groth16"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88f1d0f3a534bb54188b8dcc104307db6c56cdae574ddc3212aec0625740fc7e"
dependencies = [
 "ark-crypto-primitives",
 "ark-ec",
 "ark-ff 0.5.0",
 "ark-poly",
 "ark-relations",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
]

[[package]]
name = "ark-poly"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "579305839da207f02b89cd1679e50e67b4331e2f9294a57693e5051b7703fe27"
dependencies = [
 "ahash",
 "ark-ff 0.5.0",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
 "educe",
 "fnv",
 "hashbrown 0.15.2",
]

[[package]]
name = "ark-r1cs-std"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "941551ef1df4c7a401de7068758db6503598e6f01850bdb2cfdb614a1f9dbea1"
dependencies = [
 "ark-ec",
 "ark-ff 0.5.0",
 "ark-relations",
 "ark-std 0.5.0",
 "educe",
 "num-bigint",
 "num-integer",
 "num-traits",
 "tracing",
]

[[package]]
name = "ark-relations"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec46ddc93e7af44bcab5230937635b06fb5744464dd6a7e7b083e80ebd274384"
dependencies = [
 "ark-ff 0.5.0",
 "ark-std 0.5.0",
 "tracing",
 "tracing-subscriber 0.2.25",
]

[[package]]
name = "ark-serialize"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d6c2b318ee6e10f8c2853e73a83adc0ccb88995aa978d8a3408d492ab2ee671"
dependencies = [
 "ark-std 0.3.0",
 "digest 0.9.0",
]

[[package]]
name = "ark-serialize"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adb7b85a02b83d2f22f89bd5cac66c9c89474240cb6207cb1efc16d098e822a5"
dependencies = [
 "ark-std 0.4.0",
 "digest 0.10.7",
 "num-bigint",
]

[[package]]
name = "ark-serialize"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f4d068aaf107ebcd7dfb52bc748f8030e0fc930ac8e360146ca54c1203088f7"
dependencies = [
 "ark-serialize-derive",
 "ark-std 0.5.0",
 "arrayvec",
 "digest 0.10.7",
 "num-bigint",
]

[[package]]
name = "ark-serialize-derive"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "213888f660fddcca0d257e88e54ac05bca01885f258ccdf695bafd77031bb69d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "ark-snark"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d368e2848c2d4c129ce7679a7d0d2d612b6a274d3ea6a13bad4445d61b381b88"
dependencies = [
 "ark-ff 0.5.0",
 "ark-relations",
 "ark-serialize 0.5.0",
 "ark-std 0.5.0",
]

[[package]]
name = "ark-std"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1df2c09229cbc5a028b1d70e00fdb2acee28b1055dfb5ca73eea49c5a25c4e7c"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "ark-std"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94893f1e0c6eeab764ade8dc4c0db24caf4fe7cbbaafc0eba0a9030f447b5185"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "ark-std"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "246a225cc6131e9ee4f24619af0f19d67761fff15d7ccc22e42b80846e69449a"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "arraydeque"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d902e3d592a523def97af8f317b08ce16b7ab854c1985a0c671e6f15cebc236"

[[package]]
name = "arrayref"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76a2e8124351fda1ef8aaaa3bbd7ebbcb486bbcd4225aca0aa0d84bb2db8fecb"

[[package]]
name = "arrayvec"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c02d123df017efcdfbd739ef81735b36c5ba83ec3c59c80a9d7ecc718f92e50"
dependencies = [
 "serde",
]

[[package]]
name = "asn1_der"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "155a5a185e42c6b77ac7b88a15143d930a9e9727a5b7b77eed417404ab15c247"

[[package]]
name = "async-compression"
version = "0.4.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df895a515f70646414f4b45c0b79082783b80552b373a68283012928df56f522"
dependencies = [
 "brotli",
 "flate2",
 "futures-core",
 "memchr",
 "pin-project-lite",
 "tokio",
 "zstd",
 "zstd-safe",
]

[[package]]
name = "async-recursion"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b43422f69d8ff38f95f1b2bb76517c91589a924d1559a0e935d7c8ce0274c11"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "async-stream"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b5a71a6f37880a80d1d7f19efd781e4b5de42c88f0722cc13bcb6cc2cfe8476"
dependencies = [
 "async-stream-impl",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-stream-impl"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7c24de15d275a1ecfd47a380fb4d5ec9bfe0933f309ed5e705b775596a3574d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "async-trait"
version = "0.1.86"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "644dd749086bf3771a2fbc5f256fdb982d53f011c7d5d560304eafeecebce79d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "atomic-waker"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1505bd5d3d116872e7271a6d4e16d81d0c8570876c8de68093a09ac269d8aac0"

[[package]]
name = "aurora-engine-modexp"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "518bc5745a6264b5fd7b09dffb9667e400ee9e2bbe18555fac75e1fe9afa0df9"
dependencies = [
 "hex",
 "num",
]

[[package]]
name = "auto_impl"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e12882f59de5360c748c4cbf569a042d5fb0eb515f7bea9c1f470b47f6ffbd73"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "autocfg"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ace50bade8e6234aa140d9a2f552bbee1db4d353f69b8217bc503490fc1a9f26"

[[package]]
name = "aws-lc-rs"
version = "1.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd755adf9707cf671e31d944a189be3deaaeee11c8bc1d669bb8022ac90fbd0"
dependencies = [
 "aws-lc-sys",
 "paste",
 "zeroize",
]

[[package]]
name = "aws-lc-sys"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f9dd2e03ee80ca2822dd6ea431163d2ef259f2066a4d6ccaca6d9dcb386aa43"
dependencies = [
 "bindgen 0.69.5",
 "cc",
 "cmake",
 "dunce",
 "fs_extra",
 "paste",
]

[[package]]
name = "backoff"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b62ddb9cb1ec0a098ad4bbf9344d0713fa193ae1a80af55febcff2627b6a00c1"
dependencies = [
 "futures-core",
 "getrandom 0.2.15",
 "instant",
 "pin-project-lite",
 "rand 0.8.5",
 "tokio",
]

[[package]]
name = "backtrace"
version = "0.3.74"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d82cb332cdfaed17ae235a638438ac4d4839913cc2af585c3c6746e8f8bee1a"
dependencies = [
 "addr2line",
 "cfg-if",
 "libc",
 "miniz_oxide",
 "object",
 "rustc-demangle",
 "windows-targets 0.52.6",
]

[[package]]
name = "base-x"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cbbc9d0964165b47557570cce6c952866c2678457aca742aafc9fb771d30270"

[[package]]
name = "base16ct"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c7f02d4ea65f2c1853089ffd8d2787bdbc63de2f0d29dedbcf8ccdfa0ccd4cf"

[[package]]
name = "base58ck"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c8d66485a3a2ea485c1913c4572ce0256067a5377ac8c75c4960e1cda98605f"
dependencies = [
 "bitcoin-internals",
 "bitcoin_hashes",
]

[[package]]
name = "base64"
version = "0.21.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d297deb1925b89f2ccc13d7635fa0714f12c87adce1c75356b39ca9b7178567"

[[package]]
name = "base64"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b3254f16251a8381aa12e40e3c4d2f0199f8c6508fbecb9d91f575e0fbb8c6"

[[package]]
name = "base64-compat"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a8d4d2746f89841e49230dd26917df1876050f95abafafbe34f47cb534b88d7"
dependencies = [
 "byteorder",
]

[[package]]
name = "base64ct"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c3c1a368f70d6cf7302d78f8f7093da241fb8e8807c05cc9e51a125895a6d5b"

[[package]]
name = "bcs"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85b6598a2f5d564fb7855dc6b06fd1c38cff5a72bd8b863a4d021938497b440a"
dependencies = [
 "serde",
 "thiserror 1.0.69",
]

[[package]]
name = "bech32"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d86b93f97252c47b41663388e6d155714a9d0c398b99f1005cbc5f978b29f445"

[[package]]
name = "bech32"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d965446196e3b7decd44aa7ee49e31d630118f90ef12f97900f262eb915c951d"

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bindgen"
version = "0.69.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271383c67ccabffb7381723dea0672a673f292304fcb45c01cc648c7a8d58088"
dependencies = [
 "bitflags 2.8.0",
 "cexpr",
 "clang-sys",
 "itertools 0.10.5",
 "lazy_static",
 "lazycell",
 "log",
 "prettyplease",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 1.1.0",
 "shlex",
 "syn 2.0.98",
 "which",
]

[[package]]
name = "bindgen"
version = "0.70.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f49d8fed880d473ea71efb9bf597651e77201bdd4893efe54c9e5d65ae04ce6f"
dependencies = [
 "bitflags 2.8.0",
 "cexpr",
 "clang-sys",
 "itertools 0.13.0",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 1.1.0",
 "shlex",
 "syn 2.0.98",
]

[[package]]
name = "bit-set"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08807e080ed7f9d5433fa9b275196cfc35414f66a0c79d864dc51a0d825231a3"
dependencies = [
 "bit-vec",
]

[[package]]
name = "bit-vec"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e764a1d40d510daf35e07be9eb06e75770908c27d411ee6c92109c9840eaaf7"

[[package]]
name = "bitcoin"
version = "0.32.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce6bc65742dea50536e35ad42492b234c27904a27f0abdcbce605015cb4ea026"
dependencies = [
 "base58ck",
 "bech32 0.11.0",
 "bitcoin-internals",
 "bitcoin-io",
 "bitcoin-units",
 "bitcoin_hashes",
 "hex-conservative",
 "hex_lit",
 "secp256k1 0.29.0",
 "serde",
]

[[package]]
name = "bitcoin-da"
version = "0.7.2"
dependencies = [
 "anyhow",
 "async-trait",
 "backoff",
 "bitcoin",
 "bitcoincore-rpc",
 "borsh",
 "citrea-common",
 "citrea-primitives",
 "crypto-bigint",
 "futures",
 "hex",
 "itertools 0.13.0",
 "jsonrpsee",
 "k256",
 "lru 0.13.0",
 "metrics 0.23.0",
 "metrics-derive",
 "rand 0.8.5",
 "reqwest",
 "reth-tasks",
 "secp256k1 0.29.0",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "sov-rollup-interface",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
]

[[package]]
name = "bitcoin-internals"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30bdbe14aa07b06e6cfeffc529a1f099e5fbe249524f8125358604df99a4bed2"
dependencies = [
 "serde",
]

[[package]]
name = "bitcoin-io"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b47c4ab7a93edb0c7198c5535ed9b52b63095f4e9b45279c6736cec4b856baf"

[[package]]
name = "bitcoin-units"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5285c8bcaa25876d07f37e3d30c303f2609179716e11d688f51e8f1fe70063e2"
dependencies = [
 "bitcoin-internals",
 "serde",
]

[[package]]
name = "bitcoin_hashes"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb18c03d0db0247e147a21a6faafd5a7eb851c743db062de72018b6b7e8e4d16"
dependencies = [
 "bitcoin-io",
 "hex-conservative",
 "serde",
]

[[package]]
name = "bitcoincore-rpc"
version = "0.18.0"
source = "git+https://github.com/chainwayxyz/rust-bitcoincore-rpc.git?rev=0fe8a8b#0fe8a8b8811b9891b37fec2a305f1af6b324111f"
dependencies = [
 "async-trait",
 "bitcoincore-rpc-json",
 "jsonrpc-async",
 "log",
 "reqwest",
 "serde",
 "serde_json",
 "url",
]

[[package]]
name = "bitcoincore-rpc-json"
version = "0.18.0"
source = "git+https://github.com/chainwayxyz/rust-bitcoincore-rpc.git?rev=0fe8a8b#0fe8a8b8811b9891b37fec2a305f1af6b324111f"
dependencies = [
 "bitcoin",
 "serde",
 "serde_json",
]

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f68f53c83ab957f72c32642f3868eec03eb974d1fb82e453128456482613d36"
dependencies = [
 "serde",
]

[[package]]
name = "bitvec"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bc2832c24239b0141d5674bb9174f9d68a8b5b3f2753311927c172ca46f7e9c"
dependencies = [
 "funty",
 "radium",
 "serde",
 "tap",
 "wyz",
]

[[package]]
name = "blake2"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46502ad458c9a52b69d4d4d32775c788b7a1b85e8bc9d482d92250fc0e3f8efe"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "blake3"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1230237285e3e10cde447185e8975408ae24deaa67205ce684805c25bc0c7937"
dependencies = [
 "arrayref",
 "arrayvec",
 "cc",
 "cfg-if",
 "constant_time_eq",
]

[[package]]
name = "block"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d8c1fef690941d3e7788d328517591fecc684c084084702d6ff1641e993699a"

[[package]]
name = "block-buffer"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4152116fd6e9dadb291ae18fc1ec3575ed6d84c29642d97890f4b4a3417297e4"
dependencies = [
 "generic-array",
]

[[package]]
name = "block-buffer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3078c7629b62d3f0439517fa394996acacc5cbc91c5a20d8c658e77abd503a71"
dependencies = [
 "generic-array",
]

[[package]]
name = "block-padding"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8894febbff9f758034a5b8e12d87918f56dfc64a8e1fe757d65e29041538d93"
dependencies = [
 "generic-array",
]

[[package]]
name = "blst"
version = "0.3.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47c79a94619fade3c0b887670333513a67ac28a6a7e653eb260bf0d4103db38d"
dependencies = [
 "cc",
 "glob",
 "threadpool",
 "zeroize",
]

[[package]]
name = "boa_ast"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c340fe0f0b267787095cbe35240c6786ff19da63ec7b69367ba338eace8169b"
dependencies = [
 "bitflags 2.8.0",
 "boa_interner",
 "boa_macros",
 "boa_string",
 "indexmap 2.7.1",
 "num-bigint",
 "rustc-hash 2.1.1",
]

[[package]]
name = "boa_engine"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f620c3f06f51e65c0504ddf04978be1b814ac6586f0b45f6019801ab5efd37f9"
dependencies = [
 "arrayvec",
 "bitflags 2.8.0",
 "boa_ast",
 "boa_gc",
 "boa_interner",
 "boa_macros",
 "boa_parser",
 "boa_profiler",
 "boa_string",
 "bytemuck",
 "cfg-if",
 "dashmap",
 "fast-float2",
 "hashbrown 0.15.2",
 "icu_normalizer",
 "indexmap 2.7.1",
 "intrusive-collections",
 "itertools 0.13.0",
 "num-bigint",
 "num-integer",
 "num-traits",
 "num_enum",
 "once_cell",
 "pollster",
 "portable-atomic",
 "rand 0.8.5",
 "regress",
 "rustc-hash 2.1.1",
 "ryu-js",
 "serde",
 "serde_json",
 "sptr",
 "static_assertions",
 "tap",
 "thin-vec",
 "thiserror 2.0.12",
 "time",
]

[[package]]
name = "boa_gc"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2425c0b7720d42d73eaa6a883fbb77a5c920da8694964a3d79a67597ac55cce2"
dependencies = [
 "boa_macros",
 "boa_profiler",
 "boa_string",
 "hashbrown 0.15.2",
 "thin-vec",
]

[[package]]
name = "boa_interner"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42407a3b724cfaecde8f7d4af566df4b56af32a2f11f0956f5570bb974e7f749"
dependencies = [
 "boa_gc",
 "boa_macros",
 "hashbrown 0.15.2",
 "indexmap 2.7.1",
 "once_cell",
 "phf",
 "rustc-hash 2.1.1",
 "static_assertions",
]

[[package]]
name = "boa_macros"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fd3f870829131332587f607a7ff909f1af5fc523fd1b192db55fbbdf52e8d3c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "synstructure",
]

[[package]]
name = "boa_parser"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9cc142dac798cdc6e2dbccfddeb50f36d2523bb977a976e19bdb3ae19b740804"
dependencies = [
 "bitflags 2.8.0",
 "boa_ast",
 "boa_interner",
 "boa_macros",
 "boa_profiler",
 "fast-float2",
 "icu_properties",
 "num-bigint",
 "num-traits",
 "regress",
 "rustc-hash 2.1.1",
]

[[package]]
name = "boa_profiler"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4064908e7cdf9b6317179e9b04dcb27f1510c1c144aeab4d0394014f37a0f922"

[[package]]
name = "boa_string"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7debc13fbf7997bf38bf8e9b20f1ad5e2a7d27a900e1f6039fe244ce30f589b5"
dependencies = [
 "fast-float2",
 "paste",
 "rustc-hash 2.1.1",
 "sptr",
 "static_assertions",
]

[[package]]
name = "bollard"
version = "0.19.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "899ca34eb6924d6ec2a77c6f7f5c7339e60fd68235eaf91edd5a15f12958bb06"
dependencies = [
 "base64 0.22.1",
 "bollard-stubs",
 "bytes",
 "futures-core",
 "futures-util",
 "hex",
 "http",
 "http-body-util",
 "hyper",
 "hyper-named-pipe",
 "hyper-util",
 "hyperlocal",
 "log",
 "pin-project-lite",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_repr",
 "serde_urlencoded",
 "thiserror 2.0.12",
 "tokio",
 "tokio-util",
 "tower-service",
 "url",
 "winapi",
]

[[package]]
name = "bollard-stubs"
version = "1.48.3-rc.28.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64ea257e555d16a2c01e5593f40b73865cdf12efbceda33c6d14a2d8d1490368"
dependencies = [
 "serde",
 "serde_json",
 "serde_repr",
 "serde_with",
]

[[package]]
name = "bonsai-sdk"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bce8d6acc5286a16e94c29e9c885d1869358885e08a6feeb6bc54e36fe20055"
dependencies = [
 "duplicate",
 "maybe-async",
 "reqwest",
 "serde",
 "thiserror 1.0.69",
]

[[package]]
name = "borsh"
version = "1.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5430e3be710b68d984d1391c854eb431a9d548640711faa54eecb1df93db91cc"
dependencies = [
 "borsh-derive",
 "bytes",
 "cfg_aliases",
]

[[package]]
name = "borsh-derive"
version = "1.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8b668d39970baad5356d7c83a86fee3a539e6f93bf6764c97368243e17a0487"
dependencies = [
 "once_cell",
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "brotli"
version = "7.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc97b8f16f944bba54f0433f07e30be199b6dc2bd25937444bbad560bcea29bd"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
 "brotli-decompressor",
]

[[package]]
name = "brotli-decompressor"
version = "4.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74fa05ad7d803d413eb8380983b092cbbaf9a85f151b871360e7b00cd7060b37"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
]

[[package]]
name = "bs58"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf88ba1141d185c399bee5288d850d63b8369520c1eafc32a0430b5b6c287bf4"
dependencies = [
 "tinyvec",
]

[[package]]
name = "bumpalo"
version = "3.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1628fb46dfa0b37568d12e5edd512553eccf6a22a78e8bde00bb4aed84d5bdbf"

[[package]]
name = "byte-slice-cast"
version = "1.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3ac9f8b63eca6fd385229b3675f6cc0dc5c8a5c8a54a59d4f52ffd670d87b0c"

[[package]]
name = "bytemuck"
version = "1.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef657dfab802224e671f5818e9a4935f9b1957ed18e58292690cc39e7a4092a3"
dependencies = [
 "bytemuck_derive",
]

[[package]]
name = "bytemuck_derive"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fa76293b4f7bb636ab88fd78228235b5248b4d05cc589aed610f954af5d7c7a"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f61dac84819c6588b558454b194026eb1f09c293b9036ae9b159e74e73ab6cf9"
dependencies = [
 "serde",
]

[[package]]
name = "bzip2-sys"
version = "0.1.12****.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72ebc2f1a417f01e1da30ef264ee86ae31d2dcd2d603ea283d3c244a883ca2a9"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
]

[[package]]
name = "c-kzg"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e7e3c397401eb76228c89561cf22f85f41c95aa799ee9d860de3ea1cbc728fc"
dependencies = [
 "arbitrary",
 "blst",
 "cc",
 "glob",
 "hex",
 "libc",
 "once_cell",
 "serde",
]

[[package]]
name = "camino"
version = "1.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b96ec4966b5813e2c0507c1f86115c8c5abaadc3980879c3424042a02fd1ad3"
dependencies = [
 "serde",
]

[[package]]
name = "cargo-platform"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e35af189006b9c0f00a064685c727031e3ed2d8020f7ba284d78cc2671bd36ea"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d886547e41f740c616ae73108f6eb70afe6d940c7bc697cb30f13daec073037"
dependencies = [
 "camino",
 "cargo-platform",
 "semver 1.0.25",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "cargo_metadata"
version = "0.19.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd5eb614ed4c27c5d706420e4320fbe3216ab31fa1c33cd8246ac36dae4479ba"
dependencies = [
 "camino",
 "cargo-platform",
 "semver 1.0.25",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
]

[[package]]
name = "cast"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37b2a672a2cb129a2e41c10b1224bb368f9f37a2b16b612598138befd7b37eb5"

[[package]]
name = "cc"
version = "1.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c736e259eea577f443d5c86c304f9f4ae0295c43f3ba05c21f1d66b5f06001af"
dependencies = [
 "jobserver",
 "libc",
 "shlex",
]

[[package]]
name = "cesu8"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d43a04d8753f35258c91f8ec639f792891f748a1edbd759cf1dcea3382ad83c"

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom",
]

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "cfg_aliases"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "613afe47fcd5fac7ccf1db93babcb082c5994d996f20b8b159f2ad1658eb5724"

[[package]]
name = "chrono"
version = "0.4.39"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e36cc9d416881d2e24f9a963be5fb1cd90966419ac844274161d10488b3e825"
dependencies = [
 "android-tzdata",
 "iana-time-zone",
 "js-sys",
 "num-traits",
 "serde",
 "wasm-bindgen",
 "windows-targets 0.52.6",
]

[[package]]
name = "ciborium"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42e69ffd6f0917f5c029256a24d0161db17cea3997d185db0d35926308770f0e"
dependencies = [
 "ciborium-io",
 "ciborium-ll",
 "serde",
]

[[package]]
name = "ciborium-io"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05afea1e0a06c9be33d539b876f1ce3692f4afea2cb41f740e7743225ed1c757"

[[package]]
name = "ciborium-ll"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57663b653d948a338bfb3eeba9bb2fd5fcfaecb9e199e87e1eda4d9e8b240fd9"
dependencies = [
 "ciborium-io",
 "half",
]

[[package]]
name = "cipher"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773f3b9af64447d2ce9850330c473515014aa235e6a783b02db81ff39e4a3dad"
dependencies = [
 "crypto-common",
 "inout",
]

[[package]]
name = "citrea"
version = "0.7.2"
dependencies = [
 "alloy",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-rpc-types",
 "alloy-rpc-types-trace",
 "alloy-sol-types",
 "anyhow",
 "async-trait",
 "base64 0.22.1",
 "bincode",
 "bitcoin",
 "bitcoin-da",
 "bitcoincore-rpc",
 "borsh",
 "chrono",
 "citrea-batch-prover",
 "citrea-common",
 "citrea-e2e",
 "citrea-evm",
 "citrea-fullnode",
 "citrea-light-client-prover",
 "citrea-primitives",
 "citrea-risc0-adapter",
 "citrea-risc0-batch-proof",
 "citrea-risc0-light-client",
 "citrea-sequencer",
 "citrea-stf",
 "citrea-storage-ops",
 "clap",
 "ethereum-rpc",
 "futures",
 "hex",
 "jmt",
 "jsonrpsee",
 "l2-block-rule-enforcer",
 "log",
 "log-panics",
 "metrics 0.23.0",
 "metrics-exporter-prometheus",
 "metrics-util",
 "prover-services",
 "rand 0.8.5",
 "regex",
 "reqwest",
 "reth-primitives",
 "reth-tasks",
 "reth-transaction-pool",
 "revm",
 "risc0-binfmt",
 "risc0-zkvm",
 "rs_merkle",
 "rustc_version_runtime",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "short-header-proof-provider",
 "sov-db",
 "sov-keys",
 "sov-ledger-rpc",
 "sov-mock-da",
 "sov-modules-api",
 "sov-modules-rollup-blueprint",
 "sov-modules-stf-blueprint",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-schema-db",
 "sov-state",
 "sp1-helper",
 "tempfile",
 "tokio",
 "tracing",
 "tracing-subscriber 0.3.19",
 "uuid",
]

[[package]]
name = "citrea-batch-prover"
version = "0.7.2"
dependencies = [
 "alloy-primitives",
 "anyhow",
 "async-trait",
 "backoff",
 "base64 0.22.1",
 "bincode",
 "borsh",
 "citrea-common",
 "citrea-primitives",
 "citrea-stf",
 "faster-hex",
 "futures",
 "hex",
 "jsonrpsee",
 "metrics 0.23.0",
 "metrics-derive",
 "parking_lot",
 "prover-services",
 "rand 0.8.5",
 "rayon",
 "reth-tasks",
 "risc0-zkvm",
 "rs_merkle",
 "serde",
 "short-header-proof-provider",
 "sov-db",
 "sov-keys",
 "sov-ledger-rpc",
 "sov-mock-da",
 "sov-mock-zkvm",
 "sov-modules-api",
 "sov-modules-core",
 "sov-modules-stf-blueprint",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "tempfile",
 "tokio",
 "tower 0.4.13",
 "tracing",
 "tracing-subscriber 0.3.19",
 "uuid",
]

[[package]]
name = "citrea-cli"
version = "0.7.2"
dependencies = [
 "anyhow",
 "citrea-common",
 "citrea-storage-ops",
 "clap",
 "derive_more 1.0.0",
 "sov-db",
 "tokio",
 "tracing",
 "tracing-subscriber 0.3.19",
]

[[package]]
name = "citrea-common"
version = "0.7.2"
dependencies = [
 "alloy-consensus",
 "alloy-primitives",
 "alloy-sol-types",
 "anyhow",
 "async-trait",
 "backoff",
 "borsh",
 "citrea-evm",
 "citrea-primitives",
 "citrea-stf",
 "futures",
 "hex",
 "hyper",
 "jsonrpsee",
 "lru 0.13.0",
 "metrics 0.23.0",
 "reth-primitives",
 "reth-tasks",
 "rocksdb",
 "serde",
 "serde_json",
 "sov-db",
 "sov-keys",
 "sov-ledger-rpc",
 "sov-mock-da",
 "sov-modules-api",
 "sov-modules-stf-blueprint",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-schema-db",
 "sov-state",
 "tempfile",
 "tokio",
 "toml",
 "tower 0.4.13",
 "tower-http",
 "tracing",
]

[[package]]
name = "citrea-e2e"
version = "0.1.0"
source = "git+https://github.com/chainwayxyz/citrea-e2e?rev=859cddf#859cddf2f7f6ecc92be0cd5f99d282bff2b632ce"
dependencies = [
 "alloy-primitives",
 "anyhow",
 "async-trait",
 "bitcoin",
 "bitcoincore-rpc",
 "bollard",
 "futures",
 "hex",
 "jsonrpsee",
 "nix",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "tempfile",
 "tokio",
 "toml",
 "tracing",
 "tracing-subscriber 0.3.19",
]

[[package]]
name = "citrea-evm"
version = "0.7.2"
dependencies = [
 "alloy",
 "alloy-consensus",
 "alloy-eips",
 "alloy-network",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-rpc-types",
 "alloy-rpc-types-eth",
 "alloy-rpc-types-trace",
 "alloy-serde",
 "alloy-sol-types",
 "bcs",
 "borsh",
 "bytes",
 "citrea-primitives",
 "hex",
 "itertools 0.13.0",
 "jsonrpsee",
 "k256",
 "metrics 0.23.0",
 "metrics-derive",
 "rand 0.8.5",
 "rayon",
 "reth-chainspec",
 "reth-db",
 "reth-errors",
 "reth-primitives",
 "reth-primitives-traits",
 "reth-provider",
 "reth-rpc",
 "reth-rpc-eth-api",
 "reth-rpc-eth-types",
 "reth-rpc-server-types",
 "reth-rpc-types-compat",
 "reth-transaction-pool",
 "revm",
 "revm-inspectors",
 "revm-precompile",
 "rstest",
 "secp256k1 0.30.0",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "short-header-proof-provider",
 "sov-db",
 "sov-keys",
 "sov-modules-api",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "tempfile",
 "thiserror 2.0.12",
 "tracing",
 "tracing-subscriber 0.3.19",
 "walkdir",
]

[[package]]
name = "citrea-fullnode"
version = "0.7.2"
dependencies = [
 "alloy-primitives",
 "anyhow",
 "async-trait",
 "backoff",
 "bincode",
 "borsh",
 "citrea-common",
 "citrea-primitives",
 "citrea-stf",
 "citrea-storage-ops",
 "hex",
 "jsonrpsee",
 "metrics 0.23.0",
 "metrics-derive",
 "reth-tasks",
 "rs_merkle",
 "serde",
 "sov-db",
 "sov-keys",
 "sov-ledger-rpc",
 "sov-modules-api",
 "sov-modules-stf-blueprint",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
]

[[package]]
name = "citrea-light-client-prover"
version = "0.7.2"
dependencies = [
 "alloy-primitives",
 "anyhow",
 "async-trait",
 "bitcoin-da",
 "borsh",
 "citrea-common",
 "citrea-primitives",
 "citrea-risc0-batch-proof",
 "const-hex",
 "constmuck",
 "hex",
 "jsonrpsee",
 "metrics 0.23.0",
 "metrics-derive",
 "prover-services",
 "rand 0.8.5",
 "reth-tasks",
 "sov-db",
 "sov-mock-da",
 "sov-mock-zkvm",
 "sov-modules-api",
 "sov-modules-core",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "tempfile",
 "tokio",
 "tracing",
]

[[package]]
name = "citrea-primitives"
version = "0.7.2"
dependencies = [
 "alloy-eips",
 "brotli",
 "rs_merkle",
 "sha2 0.10.8",
 "sov-rollup-interface",
]

[[package]]
name = "citrea-risc0-adapter"
version = "0.7.2"
dependencies = [
 "anyhow",
 "bincode",
 "bonsai-sdk",
 "borsh",
 "hex",
 "metrics 0.23.0",
 "risc0-zkp",
 "risc0-zkvm",
 "rzup",
 "serde",
 "sov-db",
 "sov-rollup-interface",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "uuid",
]

[[package]]
name = "citrea-risc0-batch-proof"
version = "0.7.2"
dependencies = [
 "risc0-build",
]

[[package]]
name = "citrea-risc0-light-client"
version = "0.7.2"
dependencies = [
 "risc0-build",
]

[[package]]
name = "citrea-sequencer"
version = "0.7.2"
dependencies = [
 "alloy-eips",
 "alloy-genesis",
 "alloy-network",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-rpc-types",
 "alloy-rpc-types-eth",
 "alloy-serde",
 "anyhow",
 "async-trait",
 "backoff",
 "borsh",
 "chrono",
 "citrea-common",
 "citrea-evm",
 "citrea-primitives",
 "citrea-stf",
 "digest 0.10.7",
 "hex",
 "jsonrpsee",
 "l2-block-rule-enforcer",
 "metrics 0.23.0",
 "metrics-derive",
 "parking_lot",
 "reth-chainspec",
 "reth-db",
 "reth-execution-types",
 "reth-primitives",
 "reth-provider",
 "reth-rpc",
 "reth-rpc-eth-api",
 "reth-rpc-eth-types",
 "reth-rpc-types-compat",
 "reth-tasks",
 "reth-transaction-pool",
 "reth-trie",
 "revm",
 "rs_merkle",
 "schnellru",
 "sov-accounts",
 "sov-db",
 "sov-keys",
 "sov-modules-api",
 "sov-modules-stf-blueprint",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "tokio",
 "tracing",
 "tracing-subscriber 0.3.19",
]

[[package]]
name = "citrea-stf"
version = "0.7.2"
dependencies = [
 "anyhow",
 "borsh",
 "citrea-evm",
 "citrea-primitives",
 "citrea-stf",
 "jsonrpsee",
 "l2-block-rule-enforcer",
 "rs_merkle",
 "serde",
 "serde_json",
 "short-header-proof-provider",
 "sov-accounts",
 "sov-db",
 "sov-keys",
 "sov-mock-da",
 "sov-mock-zkvm",
 "sov-modules-api",
 "sov-modules-core",
 "sov-modules-stf-blueprint",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "tempfile",
 "tracing",
]

[[package]]
name = "citrea-storage-ops"
version = "0.7.2"
dependencies = [
 "anyhow",
 "citrea-common",
 "derive_more 1.0.0",
 "futures",
 "hex",
 "jmt",
 "reth-tasks",
 "serde",
 "sov-db",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-schema-db",
 "sov-state",
 "tempfile",
 "tokio",
 "tracing",
]

[[package]]
name = "clang-sys"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b023947811758c97c59bf9d1c188fd619ad4718dcaa767947df1cadb14f39f4"
dependencies = [
 "glob",
 "libc",
 "libloading",
]

[[package]]
name = "clap"
version = "4.5.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "027bb0d98429ae334a8698531da7077bdf906419543a35a55c2cb1b66437d767"
dependencies = [
 "clap_builder",
 "clap_derive",
]

[[package]]
name = "clap_builder"
version = "4.5.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5589e0cba072e0f3d23791efac0fd8627b49c829c196a492e88168e6a669d863"
dependencies = [
 "anstream",
 "anstyle",
 "clap_lex",
 "strsim",
]

[[package]]
name = "clap_derive"
version = "4.5.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf4ced95c6f4a675af3da73304b9ac4ed991640c36374e4b46795c49e17cf1ed"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "clap_lex"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46ad14479a25103f283c0f10005961cf086d8dc42205bb44c46ac563475dca6"

[[package]]
name = "cmake"
version = "0.1.54"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7caa3f9de89ddbe2c607f4101924c5abec803763ae9534e4f4d7d8f84aa81f0"
dependencies = [
 "cc",
]

[[package]]
name = "cobs"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67ba02a97a2bd10f4b59b25c7973101c79642302776489e030cd13cdab09ed15"

[[package]]
name = "colorchoice"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b63caa9aa9397e2d9480a9b13673856c78d8ac123288526c37d7839f2a86990"

[[package]]
name = "colored"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "117725a109d387c937a1533ce01b450cbde6b88abceea8473c4d7a85853cda3c"
dependencies = [
 "lazy_static",
 "windows-sys 0.59.0",
]

[[package]]
name = "combine"
version = "4.6.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba5a308b75df32fe02788e748662718f03fde005016435c444eea572398219fd"
dependencies = [
 "bytes",
 "memchr",
]

[[package]]
name = "concat-kdf"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d72c1252426a83be2092dd5884a5f6e3b8e7180f6891b6263d2c21b92ec8816"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "console"
version = "0.15.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea3c6ecd8059b57859df5c69830340ed3c41d30e3da0c1cbed90a96ac853041b"
dependencies = [
 "encode_unicode",
 "libc",
 "once_cell",
 "unicode-width",
 "windows-sys 0.59.0",
]

[[package]]
name = "const-hex"
version = "1.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b0485bab839b018a8f1723fc5391819fea5f8f0f32288ef8a735fd096b6160c"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "hex",
 "proptest",
 "serde",
]

[[package]]
name = "const-oid"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2459377285ad874054d797f3ccebf984978aa39129f6eafde5cdc8315b612f8"

[[package]]
name = "const_panic"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2459fc9262a1aa204eb4b5764ad4f189caec88aea9634389c0a25f8be7f6265e"

[[package]]
name = "constant_time_eq"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c74b8349d32d297c9134b8c88677813a227df8f779daa29bfc29c183fe3dca6"

[[package]]
name = "constmuck"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0e253ed9cc2e1bcc63d791dbe28f818fdff4fceb00d2ff1d3eb943574c623f8"
dependencies = [
 "bytemuck",
 "constmuck_internal",
 "typewit",
]

[[package]]
name = "constmuck_internal"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d5927bf986ef0398efc2725a986975c1bad3140c883e7bf102f3dec8bcdf0375"
dependencies = [
 "bytemuck",
 "const_panic",
]

[[package]]
name = "convert_case"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb402b8d4c85569410425650ce3eddc7d698ed96d39a73f941b08fb63082f1e7"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "core-foundation"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e195e091a93c46f7102ec7818a2aa394e1e1771c3ab4825963fa03e45afb8f"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b55271e5c8c478ad3f38ad24ef34923091e0548492a266d19b3c0b4d82574c63"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773648b94d0e5d620f64f280777445740e61fe701025087ec8b57f45c791888b"

[[package]]
name = "core-graphics-types"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45390e6114f68f718cc7a830514a96f903cccd70d02a8f6d9f643ac4ba45afaf"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation 0.9.4",
 "libc",
]

[[package]]
name = "core2"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b49ba7ef1ad6107f8824dbe97de947cbaac53c44e7f9756a1fba0d37c1eec505"
dependencies = [
 "memchr",
]

[[package]]
name = "cpufeatures"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59ed5838eebb26a2bb2e58f6d5b5316989ae9d08bab10e0e6d103e656d1b0280"
dependencies = [
 "libc",
]

[[package]]
name = "crc"
version = "3.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69e6e4d7b33a94f0991c26729976b10ebde1d34c3ee82408fb536164fa10d636"
dependencies = [
 "crc-catalog",
]

[[package]]
name = "crc-catalog"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19d374276b40fb8bbdee95aef7c7fa6b5316ec764510eb64b8dd0e2ed0d7e7f5"

[[package]]
name = "crc32fast"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97769d94ddab943e4510d138150169a2758b5ef3eb191a9ee688de3e23ef7b3"
dependencies = [
 "cfg-if",
]

[[package]]
name = "criterion"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2b12d017a929603d80db1831cd3a24082f8137ce19c69e6447f54f5fc8d692f"
dependencies = [
 "anes",
 "cast",
 "ciborium",
 "clap",
 "criterion-plot",
 "is-terminal",
 "itertools 0.10.5",
 "num-traits",
 "once_cell",
 "oorandom",
 "plotters",
 "rayon",
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
 "tinytemplate",
 "walkdir",
]

[[package]]
name = "criterion-plot"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b50826342786a51a89e2da3a28f1c32b06e387201bc2d19791f622c673706b1"
dependencies = [
 "cast",
 "itertools 0.10.5",
]

[[package]]
name = "critical-section"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "790eea4361631c5e7d22598ecd5723ff611904e3344ce8720784c93e3d83d40b"

[[package]]
name = "crossbeam-channel"
version = "0.5.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82b8f8f868b36967f9606790d1903570de9ceaf870a7bf9fbbd3016d636a2cb2"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dd111b7b7f7d55b72c0a6ae361660ee5853c9af73f70c3c2ef6858b950e2e51"
dependencies = [
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b82ac4a3c2ca9c3460964f020e1402edd5753411d7737aa39c3714ad1b5420e"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0a5c400df2834b80a4c3327b3aad3a4c4cd4de0629063962b03235697506a28"

[[package]]
name = "crunchy"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43da5946c66ffcc7745f48db692ffbb10a83bfe0afd96235c5c2a4fb23994929"

[[package]]
name = "crypto-bigint"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dc92fb57ca44df6db8059111ab3af99a63d5d0f8375d9972e319a379c6bab76"
dependencies = [
 "generic-array",
 "rand_core 0.6.4",
 "subtle",
 "zeroize",
]

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array",
 "rand_core 0.6.4",
 "typenum",
]

[[package]]
name = "crypto-mac"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b584a330336237c1eecd3e94266efb216c56ed91225d634cb2991c5f3fd1aeab"
dependencies = [
 "generic-array",
 "subtle",
]

[[package]]
name = "ctr"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0369ee1ad671834580515889b80f2ea915f23b8be8d0daa4bbaf2ac5c7590835"
dependencies = [
 "cipher",
]

[[package]]
name = "curve25519-dalek"
version = "4.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fb8b7c4503de7d6ae7b42ab72a5a59857b4c937ec27a3d4539dba95b5ab2be"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "curve25519-dalek-derive",
 "digest 0.10.7",
 "fiat-crypto",
 "rustc_version 0.4.1",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek-derive"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46882e17999c6cc590af592290432be3bce0428cb0d5f8b6715e4dc7b383eb3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "darling"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f63b86c8a8826a49b8c21f08a2d07338eec8d900540f8630dc76284be802989"
dependencies = [
 "darling_core",
 "darling_macro",
]

[[package]]
name = "darling_core"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95133861a8032aaea082871032f5815eb9e98cef03fa916ab4500513994df9e5"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim",
 "syn 2.0.98",
]

[[package]]
name = "darling_macro"
version = "0.20.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d336a2a514f6ccccaa3e09b02d41d35330c07ddf03a62165fcec10bb561c7806"
dependencies = [
 "darling_core",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "dashmap"
version = "6.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5041cc499144891f3790297212f32a74fb938e5136a14943f338ef9e0ae276cf"
dependencies = [
 "cfg-if",
 "crossbeam-utils",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core",
]

[[package]]
name = "data-encoding"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "575f75dfd25738df5b91b8e43e14d44bda14637a58fae779fd2b064f8bf3e010"

[[package]]
name = "data-encoding-macro"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f9724adfcf41f45bf652b3995837669d73c4d49a1b5ac1ff82905ac7d9b5558"
dependencies = [
 "data-encoding",
 "data-encoding-macro-internal",
]

[[package]]
name = "data-encoding-macro-internal"
version = "0.1.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18e4fdb82bd54a12e42fb58a800dcae6b9e13982238ce2296dc3570b92148e1f"
dependencies = [
 "data-encoding",
 "syn 2.0.98",
]

[[package]]
name = "delay_map"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df941644b671f05f59433e481ba0d31ac10e3667de725236a4c0d587c496fba1"
dependencies = [
 "futures",
 "tokio",
 "tokio-util",
]

[[package]]
name = "der"
version = "0.7.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f55bf8e7b65898637379c1b74eb1551107c8294ed26d855ceb9fd1a09cfc9bc0"
dependencies = [
 "const-oid",
 "pem-rfc7468",
 "zeroize",
]

[[package]]
name = "deranged"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b42b6fa04a440b495c8b04d0e71b707c585f83cb9cb28cf8cd0d976c315e31b4"
dependencies = [
 "powerfmt",
 "serde",
]

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "derive-where"
version = "1.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62d671cc41a825ebabc75757b62d3d168c577f9149b2d49ece1dad1f72119d25"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "derive_arbitrary"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30542c1ad912e0e3d22a1935c290e12e8a29d704a420177a31faad4a601a0800"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "derive_builder"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "507dfb09ea8b7fa618fcf76e953f4f5e192547945816d5358edffe39f6f94947"
dependencies = [
 "derive_builder_macro",
]

[[package]]
name = "derive_builder_core"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d5bcf7b024d6835cfb3d473887cd966994907effbe9227e8c8219824d06c4e8"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "derive_builder_macro"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab63b0e2bf4d5928aff72e83a7dace85d7bba5fe12dcc3c5a572d78caffd3f3c"
dependencies = [
 "derive_builder_core",
 "syn 2.0.98",
]

[[package]]
name = "derive_more"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a9b99b9cbbe49445b21764dc0625032a89b145a2642e67603e1c936f5458d05"
dependencies = [
 "derive_more-impl 1.0.0",
]

[[package]]
name = "derive_more"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "093242cf7570c207c83073cf82f79706fe7b8317e98620a47d5be7c3d8497678"
dependencies = [
 "derive_more-impl 2.0.1",
]

[[package]]
name = "derive_more-impl"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb7330aeadfbe296029522e6c40f315320aba36fc43a5b3632f3795348f3bd22"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "unicode-xid",
]

[[package]]
name = "derive_more-impl"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bda628edc44c4bb645fbe0f758797143e4e07926f7ebf4e9bdfbd3d2ce621df3"
dependencies = [
 "convert_case",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "unicode-xid",
]

[[package]]
name = "digest"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3dd60d1080a57a05ab032377049e0591415d2b31afd7028356dbf3cc6dcb066"
dependencies = [
 "generic-array",
]

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer 0.10.4",
 "const-oid",
 "crypto-common",
 "subtle",
]

[[package]]
name = "dirs"
version = "5.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44c45a9d03d6676652bcb5e724c7e988de1acad23a711b5217ab9cbecbec2225"
dependencies = [
 "dirs-sys",
]

[[package]]
name = "dirs-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b98cf8ebf19c3d1b223e151f99a4f9f0690dca41414773390fc824184ac833e1"
dependencies = [
 "cfg-if",
 "dirs-sys-next",
]

[[package]]
name = "dirs-sys"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "520f05a5cbd335fae5a99ff7a6ab8627577660ee5cfd6a94a6a929b52ff0321c"
dependencies = [
 "libc",
 "option-ext",
 "redox_users",
 "windows-sys 0.48.0",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "discv5"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4b4e7798d2ff74e29cee344dc490af947ae657d6ab5273dde35d58ce06a4d71"
dependencies = [
 "aes",
 "aes-gcm",
 "alloy-rlp",
 "arrayvec",
 "ctr",
 "delay_map",
 "enr",
 "fnv",
 "futures",
 "hashlink 0.9.1",
 "hex",
 "hkdf",
 "lazy_static",
 "libp2p-identity",
 "lru 0.12.5",
 "more-asserts",
 "multiaddr",
 "parking_lot",
 "rand 0.8.5",
 "smallvec",
 "socket2",
 "tokio",
 "tracing",
 "uint 0.10.0",
 "zeroize",
]

[[package]]
name = "displaydoc"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97369cbbc041bc366949bc74d34658d6cda5621039731c6310521892a3a20ae0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "docker-generate"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccf673e0848ef09fa4aeeba78e681cf651c0c7d35f76ee38cec8e55bc32fa111"

[[package]]
name = "downcast-rs"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b325c5dbd37f80359721ad39aca5a29fb04c89279657cffdda8736d0c0b9d2"

[[package]]
name = "dunce"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92773504d58c093f6de2459af4af33faa518c13451eb8f2b5698ed3d36e7c813"

[[package]]
name = "duplicate"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de78e66ac9061e030587b2a2e75cc88f22304913c907b11307bca737141230cb"
dependencies = [
 "heck 0.4.1",
 "proc-macro-error",
]

[[package]]
name = "dyn-clone"
version = "1.0.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "feeef44e73baff3a26d371801df019877a9866a8c493d315ab00177843314f35"

[[package]]
name = "ecdsa"
version = "0.16.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee27f32b5c5292967d2d4a9d7f1e0b0aed2c15daded5a60300e4abb9d8020bca"
dependencies = [
 "der",
 "digest 0.10.7",
 "elliptic-curve",
 "rfc6979",
 "serdect",
 "signature",
 "spki",
]

[[package]]
name = "ed25519"
version = "2.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "115531babc129696a58c64a4fef0a8bf9e9698629fb97e9e40767d235cfbcd53"
dependencies = [
 "pkcs8",
 "signature",
]

[[package]]
name = "ed25519-dalek"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a3daa8e81a3963a60642bcc1f90a670680bd4a77535faa384e9d1c79d620871"
dependencies = [
 "curve25519-dalek",
 "ed25519",
 "rand_core 0.6.4",
 "serde",
 "sha2 0.10.8",
 "subtle",
 "zeroize",
]

[[package]]
name = "educe"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d7bc049e1bd8cdeb31b68bbd586a9464ecf9f3944af3958a7a9d0f8b9799417"
dependencies = [
 "enum-ordinalize",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "either"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c757948c5ede0e46177b7add2e67155f70e33c07fea8284df6576da70b3719"
dependencies = [
 "serde",
]

[[package]]
name = "elf"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4445909572dbd556c457c849c4ca58623d84b27c8fff1e74b0b4227d8b90d17b"

[[package]]
name = "elliptic-curve"
version = "0.13.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5e6043086bf7973472e0c7dff2142ea0b680d30e18d9cc40f267efbf222bd47"
dependencies = [
 "base16ct",
 "crypto-bigint",
 "digest 0.10.7",
 "ff",
 "generic-array",
 "group",
 "pem-rfc7468",
 "pkcs8",
 "rand_core 0.6.4",
 "sec1",
 "serdect",
 "subtle",
 "zeroize",
]

[[package]]
name = "embedded-io"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef1a6892d9eef45c8fa6b9e0086428a2cca8491aca8f787c534a3d6d0bcb3ced"

[[package]]
name = "embedded-io"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edd0f118536f44f5ccd48bcb8b111bdc3de888b58c74639dfb034a357d0f206d"

[[package]]
name = "encode_unicode"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34aa73646ffb006b8f5147f3dc182bd4bcb190227ce861fc4a4844bf8e3cb2c0"

[[package]]
name = "encoding_rs"
version = "0.8.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75030f3c4f45dafd7586dd6780965a8c7e8e285a5ecb86713e63a79c5b2766f3"
dependencies = [
 "cfg-if",
]

[[package]]
name = "endian-type"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c34f04666d835ff5d62e058c3995147c06f42fe86ff053337632bca83e42702d"

[[package]]
name = "enr"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "851bd664a3d3a3c175cff92b2f0df02df3c541b4895d0ae307611827aae46152"
dependencies = [
 "alloy-rlp",
 "base64 0.22.1",
 "bytes",
 "ed25519-dalek",
 "hex",
 "k256",
 "log",
 "rand 0.8.5",
 "secp256k1 0.30.0",
 "serde",
 "sha3",
 "zeroize",
]

[[package]]
name = "enum-as-inner"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1e6a265c649f3f5979b601d26f1d05ada116434c87741c9493cb56218f76cbc"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "enum-ordinalize"
version = "4.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fea0dcfa4e54eeb516fe454635a95753ddd39acda650ce703031c6973e315dd5"
dependencies = [
 "enum-ordinalize-derive",
]

[[package]]
name = "enum-ordinalize-derive"
version = "4.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d28318a75d4aead5c4db25382e8ef717932d0346600cacae6357eb5941bc5ff"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "enumn"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f9ed6b3789237c8a0c1c505af1c7eb2c560df6186f01b098c3a1064ea532f38"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "equivalent"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "877a4ace8713b0bcf2a4e7eec82529c029f1d0619886d18145fea96c3ffe5c0f"

[[package]]
name = "errno"
version = "0.3.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33d852cb9b869c2a9b3df2f71a3074817f01e1844f839a144f5fcef059a4eb5d"
dependencies = [
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "ethereum-rpc"
version = "0.7.2"
dependencies = [
 "alloy-consensus",
 "alloy-network",
 "alloy-primitives",
 "alloy-rpc-types",
 "alloy-rpc-types-eth",
 "alloy-rpc-types-trace",
 "alloy-serde",
 "async-trait",
 "borsh",
 "citrea-evm",
 "citrea-primitives",
 "citrea-sequencer",
 "futures",
 "jsonrpsee",
 "parking_lot",
 "reth-primitives",
 "reth-rpc-eth-api",
 "reth-rpc-eth-types",
 "reth-rpc-types-compat",
 "rustc_version_runtime",
 "schnellru",
 "serde",
 "serde_json",
 "sov-db",
 "sov-ledger-rpc",
 "sov-modules-api",
 "sov-rollup-interface",
 "sov-state",
 "tokio",
 "tracing",
]

[[package]]
name = "ethereum_hashing"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c853bd72c9e5787f8aafc3df2907c2ed03cff3150c3acd94e2e53a98ab70a8ab"
dependencies = [
 "cpufeatures",
 "ring",
 "sha2 0.10.8",
]

[[package]]
name = "ethereum_serde_utils"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70cbccfccf81d67bff0ab36e591fa536c8a935b078a7b0e58c1d00d418332fc9"
dependencies = [
 "alloy-primitives",
 "hex",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "ethereum_ssz"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86da3096d1304f5f28476ce383005385459afeaf0eea08592b65ddbc9b258d16"
dependencies = [
 "alloy-primitives",
 "ethereum_serde_utils",
 "itertools 0.13.0",
 "serde",
 "serde_derive",
 "smallvec",
 "typenum",
]

[[package]]
name = "ethereum_ssz_derive"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d832a5c38eba0e7ad92592f7a22d693954637fbb332b4f669590d66a5c3183e5"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "eyre"
version = "0.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cd915d99f24784cdc19fd37ef22b97e3ff0ae756c7e492e9fbfe897d61e2aec"
dependencies = [
 "indenter",
 "once_cell",
]

[[package]]
name = "fallible-iterator"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2acce4a10f12dc2fb14a218589d4f1f62ef011b2d0cc4b3cb1bba8e94da14649"

[[package]]
name = "fallible-streaming-iterator"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7360491ce676a36bf9bb3c56c1aa791658183a54d2744120f27285738d90465a"

[[package]]
name = "fast-float2"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8eb564c5c7423d25c886fb561d1e4ee69f72354d16918afa32c08811f6b6a55"

[[package]]
name = "faster-hex"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7223ae2d2f179b803433d9c830478527e92b8117eab39460edae7f1614d9fb73"
dependencies = [
 "heapless",
 "serde",
]

[[package]]
name = "fastrand"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37909eebbb50d72f9059c3b6d82c0463f2ff062c9e95845c43a6c9c0355411be"

[[package]]
name = "fastrlp"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "139834ddba373bbdd213dffe02c8d110508dcf1726c2be27e8d1f7d7e1856418"
dependencies = [
 "arrayvec",
 "auto_impl",
 "bytes",
]

[[package]]
name = "fastrlp"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce8dba4714ef14b8274c371879b175aa55b16b30f269663f19d576f380018dc4"
dependencies = [
 "arrayvec",
 "auto_impl",
 "bytes",
]

[[package]]
name = "ff"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ded41244b729663b1e574f1b4fb731469f69f79c17667b5d776b16cda0479449"
dependencies = [
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "fiat-crypto"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28dea519a9695b9977216879a3ebfddf92f1c08c05d984f8996aecd6ecdc811d"

[[package]]
name = "filetime"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35c0522e981e68cbfa8c3f978441a5f34b30b96e146b33cd3359176b50fe8586"
dependencies = [
 "cfg-if",
 "libc",
 "libredox",
 "windows-sys 0.59.0",
]

[[package]]
name = "fixed-hash"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835c052cb0c08c1acf6ffd71c022172e18723949c8282f2b9f27efbc51e64534"
dependencies = [
 "byteorder",
 "rand 0.8.5",
 "rustc-hex",
 "static_assertions",
]

[[package]]
name = "flate2"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11faaf5a5236997af9848be0bef4db95824b1d534ebc64d0f0c6cf3e67bd38dc"
dependencies = [
 "crc32fast",
 "miniz_oxide",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "foldhash"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0d2fde1f7b3d48b8395d5f2de76c18a528bd6a9cdde438df747bfcba3e05d6f"

[[package]]
name = "foreign-types"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d737d9aa519fb7b749cbc3b962edcf310a8dd1f4b67c91c4f83975dbdd17d965"
dependencies = [
 "foreign-types-macros",
 "foreign-types-shared",
]

[[package]]
name = "foreign-types-macros"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a5c6c585bc94aaf2c7b51dd4c2ba22680844aba4c687be581871a6f518c5742"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "foreign-types-shared"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa9a19cbb55df58761df49b23516a86d432839add4af60fc256da840f66ed35b"

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding",
]

[[package]]
name = "fs2"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9564fc758e15025b46aa6643b1b77d047d1a56a1aea6e01002ac0c7026876213"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "fs_extra"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42703706b716c37f96a77aea830392ad231f44c9e9a67872fa5548707e11b11c"

[[package]]
name = "fsevent-sys"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76ee7a02da4d231650c7cea31349b889be2f45ddb3ef3032d2ec8185f6313fd2"
dependencies = [
 "libc",
]

[[package]]
name = "funty"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6d5a32815ae3f33302d95fdcb2ce17862f8c65363dcfd29360480ba1001fc9c"

[[package]]
name = "futures"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65bc07b1a8bc7c85c5f2e110c476c7389b4554ba72af57d8445ea63a576b0876"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2dff15bf788c671c1934e366d07e30c1814a8ef514e1af724a602e8a2fbe1b10"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f29059c0c2090612e8d742178b0580d2dc940c837851ad723096f87af6663e"

[[package]]
name = "futures-executor"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e28d1d997f585e54aebc3f97d39e72338912123a67330d723fdbb564d646c9f"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-io"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e5c1b78ca4aae1ac06c48a526a655760685149f0d465d21f37abfe57ce075c6"

[[package]]
name = "futures-macro"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "162ee34ebcb7c64a8abebc059ce0fee27c2262618d7b60ed8faf72fef13c3650"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "futures-sink"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e575fab7d1e0dcb8d0c7bcf9a63ee213816ab51902e6d244a95819acacf1d4f7"

[[package]]
name = "futures-task"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f90f7dce0722e95104fcb095585910c0977252f286e354b5e3bd38902cd99988"

[[package]]
name = "futures-timer"
version = "3.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f288b0a4f20f9a56b5d1da57e2227c661b7b16168e2f72365f57b63326e29b24"
dependencies = [
 "gloo-timers",
 "send_wrapper",
]

[[package]]
name = "futures-util"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fa08315bb612088cc391249efdc3bc77536f16c91f6cf495e6fbe85b20a4a81"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
]

[[package]]
name = "futures-utils-wasm"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42012b0f064e01aa58b545fe3727f90f7dd4020f4a3ea735b50344965f5a57e9"

[[package]]
name = "generator"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc6bd114ceda131d3b1d665eba35788690ad37f5916457286b32ab6fd3c438dd"
dependencies = [
 "cfg-if",
 "libc",
 "log",
 "rustversion",
 "windows 0.58.0",
]

[[package]]
name = "generic-array"
version = "0.14.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85649ca51fd72272d7821adaf274ad91c288277713d9c18820d8499a7ff69e9a"
dependencies = [
 "serde",
 "typenum",
 "version_check",
 "zeroize",
]

[[package]]
name = "getrandom"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4567c8db10ae91089c99af84c68c38da3ec2f087c3f82960bcdbf3656b6f4d7"
dependencies = [
 "cfg-if",
 "js-sys",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "getrandom"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73fea8450eea4bac3940448fb7ae50d91f034f941199fcd9d909a5a07aa455f0"
dependencies = [
 "cfg-if",
 "libc",
 "r-efi",
 "wasi 0.14.2+wasi-0.2.4",
]

[[package]]
name = "ghash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0d8a4362ccb29cb0b265253fb0a2728f592895ee6854fd9bc13f2ffda266ff1"
dependencies = [
 "opaque-debug",
 "polyval",
]

[[package]]
name = "gimli"
version = "0.31.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07e28edb80900c19c28f1072f2e8aeca7fa06b23cd4169cefe1af5aa3260783f"

[[package]]
name = "git2"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5220b8ba44c68a9a7f7a7659e864dd73692e417ef0211bea133c7b74e031eeb9"
dependencies = [
 "bitflags 2.8.0",
 "libc",
 "libgit2-sys",
 "log",
 "url",
]

[[package]]
name = "glob"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8d1add55171497b4705a648c6b583acafb01d58050a51727785f0b2c8e0a2b2"

[[package]]
name = "gloo-net"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c06f627b1a58ca3d42b45d6104bf1e1a03799df472df00988b6ba21accc10580"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-sink",
 "gloo-utils",
 "http",
 "js-sys",
 "pin-project",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
]

[[package]]
name = "gloo-timers"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b995a66bb87bebce9a0f4a95aed01daca4872c050bfcb21653361c03bc35e5c"
dependencies = [
 "futures-channel",
 "futures-core",
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "gloo-utils"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b5555354113b18c547c1d3a98fbf7fb32a9ff4f6fa112ce823a21641a0ba3aa"
dependencies = [
 "js-sys",
 "serde",
 "serde_json",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "group"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0f9ef7462f7c099f518d754361858f86d8a07af53ba9af0fe635bbccb151a63"
dependencies = [
 "ff",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "h2"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5017294ff4bb30944501348f6f8e42e6ad28f42c8bbef7a74029aff064a4e3c2"
dependencies = [
 "atomic-waker",
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "http",
 "indexmap 2.7.1",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "half"
version = "2.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6dd08c532ae367adf81c312a4580bc67f1d0fe8bc9c460520283f4c0ff277888"
dependencies = [
 "cfg-if",
 "crunchy",
]

[[package]]
name = "hash32"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47d60b12902ba28e2730cd37e95b8c9223af2808df9e902d4df49588d1470606"
dependencies = [
 "byteorder",
]

[[package]]
name = "hashbrown"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888"

[[package]]
name = "hashbrown"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43a3c133739dddd0d2990f9a4bdf8eb4b21ef50e4851ca85ab661199821d510e"
dependencies = [
 "ahash",
]

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"
dependencies = [
 "ahash",
 "allocator-api2",
]

[[package]]
name = "hashbrown"
version = "0.15.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf151400ff0baff5465007dd2f3e717f3fe502074ca563069ce3a6629d07b289"
dependencies = [
 "allocator-api2",
 "equivalent",
 "foldhash",
 "serde",
]

[[package]]
name = "hashlink"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ba4ff7128dee98c7dc9794b6a411377e1404dba1c97deb8d1a55297bd25d8af"
dependencies = [
 "hashbrown 0.14.5",
]

[[package]]
name = "hashlink"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7382cf6263419f2d8df38c55d7da83da5c18aef87fc7a7fc1fb1e344edfe14c1"
dependencies = [
 "hashbrown 0.15.2",
]

[[package]]
name = "hdrhistogram"
version = "7.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "765c9198f173dd59ce26ff9f95ef0aafd0a0fe01fb9d72841bc5066a4c06511d"
dependencies = [
 "byteorder",
 "num-traits",
]

[[package]]
name = "heapless"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bfb9eb618601c89945a70e254898da93b13be0388091d42117462b265bb3fad"
dependencies = [
 "hash32",
 "stable_deref_trait",
]

[[package]]
name = "heck"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95505c38b4572b2d910cecb0281560f54b440a19336cbbcb27bf6ce6adc6f5a8"

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "hermit-abi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d231dfb89cfffdbc30e7fc41579ed6066ad03abda9e567ccafae602b97ec5024"

[[package]]
name = "hermit-abi"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbf6a919d6cf397374f7dfeeea91d974c7c0a7221d0d0f4f20d859d329e53fcc"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"
dependencies = [
 "serde",
]

[[package]]
name = "hex-conservative"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5313b072ce3c597065a808dbf612c4c8e8590bdbf8b579508bf7a762c5eae6cd"
dependencies = [
 "arrayvec",
]

[[package]]
name = "hex-literal"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fe2267d4ed49bc07b63801559be28c718ea06c4738b7a03c94df7386d2cde46"

[[package]]
name = "hex_lit"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3011d1213f159867b13cfd6ac92d2cd5f1345762c63be3554e84092d85a50bbd"

[[package]]
name = "hickory-proto"
version = "0.25.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d844af74f7b799e41c78221be863bade11c430d46042c3b49ca8ae0c6d27287"
dependencies = [
 "async-recursion",
 "async-trait",
 "cfg-if",
 "critical-section",
 "data-encoding",
 "enum-as-inner",
 "futures-channel",
 "futures-io",
 "futures-util",
 "idna",
 "ipnet",
 "once_cell",
 "rand 0.9.0",
 "ring",
 "serde",
 "thiserror 2.0.12",
 "tinyvec",
 "tokio",
 "tracing",
 "url",
]

[[package]]
name = "hickory-resolver"
version = "0.25.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a128410b38d6f931fcc6ca5c107a3b02cabd6c05967841269a4ad65d23c44331"
dependencies = [
 "cfg-if",
 "futures-util",
 "hickory-proto",
 "ipconfig",
 "moka",
 "once_cell",
 "parking_lot",
 "rand 0.9.0",
 "resolv-conf",
 "serde",
 "smallvec",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
]

[[package]]
name = "hkdf"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5f8eb2ad728638ea2c7d47a21db23b7b58a72ed6a38256b8a1849f15fbbdf7"
dependencies = [
 "hmac 0.12.1",
]

[[package]]
name = "hmac"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126888268dcc288495a26bf004b38c5fdbb31682f992c84ceb046a1f0fe38840"
dependencies = [
 "crypto-mac",
 "digest 0.9.0",
]

[[package]]
name = "hmac"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c49c37c09c17a53d937dfbb742eb3a961d65a994e6bcdcf37e7399d0cc8ab5e"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "hmac-drbg"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17ea0a1394df5b6574da6e0c1ade9e78868c9fb0a4e5ef4428e32da4676b85b1"
dependencies = [
 "digest 0.9.0",
 "generic-array",
 "hmac 0.8.1",
]

[[package]]
name = "home"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589533453244b0995c858700322199b2becb13b627df2851f64a2775d024abcf"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "hostname"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9c7c7c8ac16c798734b8a24560c1362120597c40d5e1459f09498f8f6c8f2ba"
dependencies = [
 "cfg-if",
 "libc",
 "windows 0.52.0",
]

[[package]]
name = "http"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f16ca2af56261c99fba8bac40a10251ce8188205a4c448fbb745a2e4daa76fea"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http-body"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1efedce1fb8e6913f23e0c92de8e62cd5b772a67e7b3946df930a62566c93184"
dependencies = [
 "bytes",
 "http",
]

[[package]]
name = "http-body-util"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "793429d76616a256bcb62c2a2ec2bed781c8307e797e2598c50010f2bee2544f"
dependencies = [
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "pin-project-lite",
]

[[package]]
name = "http-range-header"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9171a2ea8a68358193d15dd5d70c1c10a2afc3e7e4c5bc92bc9f025cebd7359c"

[[package]]
name = "httparse"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2d708df4e7140240a16cd6ab0ab65c972d7433ab77819ea693fde9c43811e2a"

[[package]]
name = "httpdate"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df3b46402a9d5adb4c86a0cf463f42e19994e3ee891101b1841f30a545cb49a9"

[[package]]
name = "humantime"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a3a5bfb195931eeb336b2a7b4d761daec841b97f947d34394601737a7bba5e4"

[[package]]
name = "humantime-serde"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57a3db5ea5923d99402c94e9feb261dc5ee9b4efa158b0315f788cf549cc200c"
dependencies = [
 "humantime",
 "serde",
]

[[package]]
name = "hyper"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc2b571658e38e0c01b1fdca3bbbe93c00d3d71693ff2770043f8c29bc7d6f80"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite",
 "smallvec",
 "tokio",
 "want",
]

[[package]]
name = "hyper-named-pipe"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73b7d8abf35697b81a825e386fc151e0d503e8cb5fcb93cc8669c376dfd6f278"
dependencies = [
 "hex",
 "hyper",
 "hyper-util",
 "pin-project-lite",
 "tokio",
 "tower-service",
 "winapi",
]

[[package]]
name = "hyper-rustls"
version = "0.27.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d191583f3da1305256f22463b9bb0471acad48a4e534a5218b9963e9c1f59b2"
dependencies = [
 "futures-util",
 "http",
 "hyper",
 "hyper-util",
 "log",
 "rustls",
 "rustls-native-certs",
 "rustls-pki-types",
 "tokio",
 "tokio-rustls",
 "tower-service",
 "webpki-roots",
]

[[package]]
name = "hyper-util"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df2dcfbe0677734ab2f3ffa7fa7bfd4706bfdc1ef393f2ee30184aed67e631b4"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-util",
 "http",
 "http-body",
 "hyper",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
]

[[package]]
name = "hyperlocal"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "986c5ce3b994526b3cd75578e62554abd09f0899d6206de48b3e96ab34ccc8c7"
dependencies = [
 "hex",
 "http-body-util",
 "hyper",
 "hyper-util",
 "pin-project-lite",
 "tokio",
 "tower-service",
]

[[package]]
name = "iana-time-zone"
version = "0.1.61"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "235e081f3925a06703c2d0117ea8b91f042756fd6e7a6e5d901e8ca1a996b220"
dependencies = [
 "android_system_properties",
 "core-foundation-sys",
 "iana-time-zone-haiku",
 "js-sys",
 "wasm-bindgen",
 "windows-core 0.52.0",
]

[[package]]
name = "iana-time-zone-haiku"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f31827a206f56af32e590ba56d5d2d085f558508192593743f16b2306495269f"
dependencies = [
 "cc",
]

[[package]]
name = "ics23"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73b17f1a5bd7d12ad30a21445cfa5f52fd7651cb3243ba866f9916b1ec112f12"
dependencies = [
 "anyhow",
 "blake2",
 "blake3",
 "bytes",
 "hex",
 "informalsystems-pbjson",
 "prost",
 "ripemd",
 "serde",
 "sha2 0.10.8",
 "sha3",
]

[[package]]
name = "icu_collections"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db2fa452206ebee18c4b5c2274dbf1de17008e874b4dc4f0aea9d01ca79e4526"
dependencies = [
 "displaydoc",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_locid"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13acbb8371917fc971be86fc8057c41a64b521c184808a698c02acc242dbf637"
dependencies = [
 "displaydoc",
 "litemap",
 "tinystr",
 "writeable",
 "zerovec",
]

[[package]]
name = "icu_locid_transform"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01d11ac35de8e40fdeda00d9e1e9d92525f3f9d887cdd7aa81d727596788b54e"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_locid_transform_data",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_locid_transform_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdc8ff3388f852bede6b579ad4e978ab004f139284d7b28715f773507b946f6e"

[[package]]
name = "icu_normalizer"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19ce3e0da2ec68599d193c93d088142efd7f9c5d6fc9b803774855747dc6a84f"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_normalizer_data",
 "icu_properties",
 "icu_provider",
 "smallvec",
 "utf16_iter",
 "utf8_iter",
 "write16",
 "zerovec",
]

[[package]]
name = "icu_normalizer_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8cafbf7aa791e9b22bec55a167906f9e1215fd475cd22adfcf660e03e989516"

[[package]]
name = "icu_properties"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93d6020766cfc6302c15dbbc9c8778c37e62c14427cb7f6e601d849e092aeef5"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_locid_transform",
 "icu_properties_data",
 "icu_provider",
 "tinystr",
 "zerovec",
]

[[package]]
name = "icu_properties_data"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67a8effbc3dd3e4ba1afa8ad918d5684b8868b3b26500753effea8d2eed19569"

[[package]]
name = "icu_provider"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ed421c8a8ef78d3e2dbc98a973be2f3770cb42b606e3ab18d6237c4dfde68d9"
dependencies = [
 "displaydoc",
 "icu_locid",
 "icu_provider_macros",
 "stable_deref_trait",
 "tinystr",
 "writeable",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_provider_macros"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ec89e9337638ecdc08744df490b221a7399bf8d164eb52a665454e60e075ad6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "686f825264d630750a544639377bae737628043f20d38bbc029e8f29ea968a7e"
dependencies = [
 "idna_adapter",
 "smallvec",
 "utf8_iter",
]

[[package]]
name = "idna_adapter"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daca1df1c957320b2cf139ac61e7bd64fed304c5040df000a745aa1de3b4ef71"
dependencies = [
 "icu_normalizer",
 "icu_properties",
]

[[package]]
name = "if-addrs"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a78a89907582615b19f6f0da1af18abf6ff08be259395669b834b057a7ee92d8"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "impl-codec"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba6a270039626615617f3f36d15fc827041df3b78c439da2cadfa47455a77f2f"
dependencies = [
 "parity-scale-codec",
]

[[package]]
name = "impl-trait-for-tuples"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0eb5a3343abf848c0984fe4604b2b105da9539376e24fc0a3b0007411ae4fd9"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "include_bytes_aligned"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ee796ad498c8d9a1d68e477df8f754ed784ef875de1414ebdaf169f70a6a784"

[[package]]
name = "include_dir"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "923d117408f1e49d914f1a379a309cffe4f18c05cf4e3d12e613a15fc81bd0dd"
dependencies = [
 "include_dir_macros",
]

[[package]]
name = "include_dir_macros"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cab85a7ed0bd5f0e76d93846e0147172bed2e2d3f859bcc33a8d9699cad1a75"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "indenter"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce23b50ad8242c51a442f3ff322d56b02f08852c77e4c0b4d3fd684abc89c683"

[[package]]
name = "indexmap"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd070e393353796e801d209ad339e89596eb4c8d430d18ede6a1cced8fafbd99"
dependencies = [
 "autocfg",
 "hashbrown 0.12.3",
 "serde",
]

[[package]]
name = "indexmap"
version = "2.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c9c992b02b5b4c94ea26e32fe5bccb7aa7d9f390ab5c1221ff895bc7ea8b652"
dependencies = [
 "equivalent",
 "hashbrown 0.15.2",
 "serde",
]

[[package]]
name = "indicatif"
version = "0.17.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "183b3088984b400f4cfac3620d5e076c84da5364016b4f49473de574b2586235"
dependencies = [
 "console",
 "number_prefix",
 "portable-atomic",
 "unicode-width",
 "web-time",
]

[[package]]
name = "informalsystems-pbjson"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aa4a0980c8379295100d70854354e78df2ee1c6ca0f96ffe89afeb3140e3a3d"
dependencies = [
 "base64 0.21.7",
 "serde",
]

[[package]]
name = "inotify"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f37dccff2791ab604f9babef0ba14fbe0be30bd368dc541e2b08d07c8aa908f3"
dependencies = [
 "bitflags 2.8.0",
 "inotify-sys",
 "libc",
]

[[package]]
name = "inotify-sys"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e05c02b5e89bff3b946cedeca278abc628fe811e604f027c45a8aa3cf793d0eb"
dependencies = [
 "libc",
]

[[package]]
name = "inout"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "879f10e63c20629ecabbb64a8010319738c66a5cd0c29b02d63d272b03751d01"
dependencies = [
 "block-padding",
 "generic-array",
]

[[package]]
name = "instant"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0242819d153cba4b4b05a5a8f2a7e9bbf97b6055b2a002b395c96b5ff3c0222"
dependencies = [
 "cfg-if",
]

[[package]]
name = "integration-tests"
version = "0.7.2"
dependencies = [
 "anyhow",
 "borsh",
 "jsonrpsee",
 "serde",
 "sov-modules-api",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-schema-db",
 "sov-state",
 "tempfile",
]

[[package]]
name = "intrusive-collections"
version = "0.9.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "189d0897e4cbe8c75efedf3502c18c887b05046e59d28404d4d8e46cbc4d1e86"
dependencies = [
 "memoffset",
]

[[package]]
name = "ipconfig"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b58db92f96b720de98181bbbe63c831e87005ab460c1bf306eb2622b4707997f"
dependencies = [
 "socket2",
 "widestring",
 "windows-sys 0.48.0",
 "winreg",
]

[[package]]
name = "ipnet"
version = "2.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "469fb0b9cefa57e3ef31275ee7cacb78f2fdca44e4765491884a2b119d4eb130"

[[package]]
name = "iri-string"
version = "0.7.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc0f0a572e8ffe56e2ff4f769f32ffe919282c3916799f8b68688b6030063bea"
dependencies = [
 "memchr",
 "serde",
]

[[package]]
name = "is-terminal"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e19b23d53f35ce9f56aebc7d1bb4e6ac1e9c0db7ac85c8d1760c04379edced37"
dependencies = [
 "hermit-abi 0.4.0",
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "is_terminal_polyfill"
version = "1.70.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7943c866cc5cd64cbc25b2e01621d07fa8eb2a1a23160ee81ce38704e97b8ecf"

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "413ee7dfc52ee1a4949ceeb7dbc8a33f2d6c088194d9f922fb8318faf1f01186"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b192c782037fadd9cfa75548310488aabdbf3d2da73885b31bd0abd03351285"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d75a2a4b1b190afb6f5425f10f6a8f959d2ea0b9c2b1d79553551850539e4674"

[[package]]
name = "jmt"
version = "0.11.0"
source = "git+https://github.com/penumbra-zone/jmt.git?rev=550a2f2#550a2f20984a5c31c51715381d3f67390e138ffa"
dependencies = [
 "anyhow",
 "borsh",
 "digest 0.10.7",
 "hashbrown 0.13.2",
 "hex",
 "ics23",
 "itertools 0.10.5",
 "mirai-annotations",
 "num-derive",
 "num-traits",
 "serde",
 "sha2 0.10.8",
 "thiserror 1.0.69",
 "tracing",
]

[[package]]
name = "jni"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a87aa2bb7d2af34197c04845522473242e1aa17c12f4935d5856491a7fb8c97"
dependencies = [
 "cesu8",
 "cfg-if",
 "combine",
 "jni-sys",
 "log",
 "thiserror 1.0.69",
 "walkdir",
 "windows-sys 0.45.0",
]

[[package]]
name = "jni-sys"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8eaf4bc02d17cbdd7ff4c7438cafcdf7fb9a4613313ad11b4f8fefe7d3fa0130"

[[package]]
name = "jobserver"
version = "0.1.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48d1dbcbbeb6a7fec7e059840aa538bd62aaccf972c7346c4d9d2059312853d0"
dependencies = [
 "libc",
]

[[package]]
name = "js-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cfaf33c695fc6e08064efbc1f72ec937429614f25eef83af942d0e227c3a28f"
dependencies = [
 "once_cell",
 "wasm-bindgen",
]

[[package]]
name = "jsonrpc-async"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a20e8e4ed08ee58717113cbf277b1ecef5cd9554d3e48c114de338289727d466"
dependencies = [
 "async-trait",
 "base64-compat",
 "serde",
 "serde_derive",
 "serde_json",
 "tokio",
]

[[package]]
name = "jsonrpsee"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37b26c20e2178756451cfeb0661fb74c47dd5988cb7e3939de7e9241fd604d42"
dependencies = [
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-http-client",
 "jsonrpsee-proc-macros",
 "jsonrpsee-server",
 "jsonrpsee-types",
 "jsonrpsee-wasm-client",
 "jsonrpsee-ws-client",
 "tokio",
 "tracing",
]

[[package]]
name = "jsonrpsee-client-transport"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bacb85abf4117092455e1573625e21b8f8ef4dec8aff13361140b2dc266cdff2"
dependencies = [
 "base64 0.22.1",
 "futures-channel",
 "futures-util",
 "gloo-net",
 "http",
 "jsonrpsee-core",
 "pin-project",
 "rustls",
 "rustls-pki-types",
 "rustls-platform-verifier",
 "soketto",
 "thiserror 1.0.69",
 "tokio",
 "tokio-rustls",
 "tokio-util",
 "tracing",
 "url",
]

[[package]]
name = "jsonrpsee-core"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "456196007ca3a14db478346f58c7238028d55ee15c1df15115596e411ff27925"
dependencies = [
 "async-trait",
 "bytes",
 "futures-timer",
 "futures-util",
 "http",
 "http-body",
 "http-body-util",
 "jsonrpsee-types",
 "parking_lot",
 "pin-project",
 "rand 0.8.5",
 "rustc-hash 2.1.1",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
 "tracing",
 "wasm-bindgen-futures",
]

[[package]]
name = "jsonrpsee-http-client"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c872b6c9961a4ccc543e321bb5b89f6b2d2c7fe8b61906918273a3333c95400c"
dependencies = [
 "async-trait",
 "base64 0.22.1",
 "http-body",
 "hyper",
 "hyper-rustls",
 "hyper-util",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "rustls",
 "rustls-platform-verifier",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "tokio",
 "tower 0.4.13",
 "tracing",
 "url",
]

[[package]]
name = "jsonrpsee-proc-macros"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e65763c942dfc9358146571911b0cd1c361c2d63e2d2305622d40d36376ca80"
dependencies = [
 "heck 0.5.0",
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "jsonrpsee-server"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55e363146da18e50ad2b51a0a7925fc423137a0b1371af8235b1c231a0647328"
dependencies = [
 "futures-util",
 "http",
 "http-body",
 "http-body-util",
 "hyper",
 "hyper-util",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "pin-project",
 "route-recognizer",
 "serde",
 "serde_json",
 "soketto",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tower 0.4.13",
 "tracing",
]

[[package]]
name = "jsonrpsee-types"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08a8e70baf945b6b5752fc8eb38c918a48f1234daf11355e07106d963f860089"
dependencies = [
 "http",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "jsonrpsee-wasm-client"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6558a9586cad43019dafd0b6311d0938f46efc116b34b28c74778bc11a2edf6"
dependencies = [
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-types",
]

[[package]]
name = "jsonrpsee-ws-client"
version = "0.24.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01b3323d890aa384f12148e8d2a1fd18eb66e9e7e825f9de4fa53bcc19b93eef"
dependencies = [
 "http",
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "url",
]

[[package]]
name = "jsonwebtoken"
version = "9.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a87cc7a48537badeae96744432de36f4be2b4a34a05a5ef32e9dd8a1c169dde"
dependencies = [
 "base64 0.22.1",
 "js-sys",
 "pem",
 "ring",
 "serde",
 "serde_json",
 "simple_asn1",
]

[[package]]
name = "k256"
version = "0.13.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6e3919bbaa2945715f0bb6d3934a173d1e9a59ac23767fbaaef277265a7411b"
dependencies = [
 "cfg-if",
 "ecdsa",
 "elliptic-curve",
 "once_cell",
 "serdect",
 "sha2 0.10.8",
 "signature",
]

[[package]]
name = "keccak"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ecc2af9a1119c51f12a14607e783cb977bde58bc069ff0c3da1095e635d70654"
dependencies = [
 "cpufeatures",
]

[[package]]
name = "keccak-asm"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "505d1856a39b200489082f90d897c3f07c455563880bc5952e38eabf731c83b6"
dependencies = [
 "digest 0.10.7",
 "sha3-asm",
]

[[package]]
name = "kqueue"
version = "1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7447f1ca1b7b563588a205fe93dea8df60fd981423a768bc1c0ded35ed147d0c"
dependencies = [
 "kqueue-sys",
 "libc",
]

[[package]]
name = "kqueue-sys"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed9625ffda8729b85e45cf04090035ac368927b8cebc34898e7c120f52e4838b"
dependencies = [
 "bitflags 1.3.2",
 "libc",
]

[[package]]
name = "l2-block-rule-enforcer"
version = "0.7.2"
dependencies = [
 "borsh",
 "chrono",
 "citrea-evm",
 "hex",
 "jsonrpsee",
 "serde",
 "sov-db",
 "sov-keys",
 "sov-mock-da",
 "sov-modules-api",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "tempfile",
 "tracing",
]

[[package]]
name = "lazy-regex"
version = "3.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60c7310b93682b36b98fa7ea4de998d3463ccbebd94d935d6b48ba5b6ffa7126"
dependencies = [
 "lazy-regex-proc_macros",
 "once_cell",
 "regex",
]

[[package]]
name = "lazy-regex-proc_macros"
version = "3.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ba01db5ef81e17eb10a5e0f2109d1b3a3e29bac3070fdbd7d156bf7dbd206a1"
dependencies = [
 "proc-macro2",
 "quote",
 "regex",
 "syn 2.0.98",
]

[[package]]
name = "lazy_static"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbd2bcb4c963f2ddae06a2efc7e9f3591312473c50c6685e1f298068316e66fe"
dependencies = [
 "spin",
]

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "libc"
version = "0.2.170"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "875b3680cb2f8f71bdcf9a30f38d48282f5d3c95cbf9b3fa57269bb5d5c06828"

[[package]]
name = "libgit2-sys"
version = "0.18.1****.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1dcb20f84ffcdd825c7a311ae347cce604a6f084a767dec4a4929829645290e"
dependencies = [
 "cc",
 "libc",
 "libz-sys",
 "pkg-config",
]

[[package]]
name = "libloading"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc2f4eb4bc735547cfed7c0a4922cbd04a4655978c09b54f1f7b228750664c34"
dependencies = [
 "cfg-if",
 "windows-targets 0.52.6",
]

[[package]]
name = "libm"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8355be11b20d696c8f18f6cc018c4e372165b1fa8126cef092399c9951984ffa"

[[package]]
name = "libp2p-identity"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "257b5621d159b32282eac446bed6670c39c7dc68a200a992d8f056afa0066f6d"
dependencies = [
 "asn1_der",
 "bs58",
 "ed25519-dalek",
 "hkdf",
 "libsecp256k1",
 "multihash",
 "quick-protobuf",
 "sha2 0.10.8",
 "thiserror 1.0.69",
 "tracing",
 "zeroize",
]

[[package]]
name = "libredox"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0ff37bd590ca25063e35af745c343cb7a0271906fb7b37e4813e8f79f00268d"
dependencies = [
 "bitflags 2.8.0",
 "libc",
 "redox_syscall",
]

[[package]]
name = "librocksdb-sys"
version = "0.16.0+8.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce3d60bc059831dc1c83903fb45c103f75db65c5a7bf22272764d9cc683e348c"
dependencies = [
 "bindgen 0.69.5",
 "bzip2-sys",
 "cc",
 "glob",
 "libc",
 "libz-sys",
 "lz4-sys",
]

[[package]]
name = "libsecp256k1"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e79019718125edc905a079a70cfa5f3820bc76139fc91d6f9abc27ea2a887139"
dependencies = [
 "arrayref",
 "base64 0.22.1",
 "digest 0.9.0",
 "hmac-drbg",
 "libsecp256k1-core",
 "libsecp256k1-gen-ecmult",
 "libsecp256k1-gen-genmult",
 "rand 0.8.5",
 "serde",
 "sha2 0.9.9",
 "typenum",
]

[[package]]
name = "libsecp256k1-core"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5be9b9bb642d8522a44d533eab56c16c738301965504753b03ad1de3425d5451"
dependencies = [
 "crunchy",
 "digest 0.9.0",
 "subtle",
]

[[package]]
name = "libsecp256k1-gen-ecmult"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3038c808c55c87e8a172643a7d87187fc6c4174468159cb3090659d55bcb4809"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "libsecp256k1-gen-genmult"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3db8d6ba2cec9eacc40e6e8ccc98931840301f1006e95647ceb2dd5c3aa06f7c"
dependencies = [
 "libsecp256k1-core",
]

[[package]]
name = "libsqlite3-sys"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbb8270bb4060bd76c6e96f20c52d80620f1d82a3470885694e41e0f81ef6fe7"
dependencies = [
 "cc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "libz-sys"
version = "1.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df9b68e50e6e0b26f672573834882eb57759f6db9b3be2ea3c35c91188bb4eaa"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "linked-hash-map"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0717cef1bc8b636c6e1c1bbdefc09e6322da8a9321966e8928ef80d20f7f770f"

[[package]]
name = "linked_hash_set"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bae85b5be22d9843c80e5fc80e9b64c8a3b1f98f867c709956eca3efff4e92e2"
dependencies = [
 "linked-hash-map",
 "serde",
]

[[package]]
name = "linux-raw-sys"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d26c52dbd32dccf2d10cac7725f8eae5296885fb5703b261f7d0a0739ec807ab"

[[package]]
name = "linux-raw-sys"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd945864f07fe9f5371a27ad7b52a172b4b499999f1d97574c9fa68373937e12"

[[package]]
name = "litemap"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ee93343901ab17bd981295f2cf0026d4ad018c7c31ba84549a4ddbb47a45104"

[[package]]
name = "lock_api"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07af8b9cdd281b7915f413fa73f29ebd5d55d0d3f0155584dade1ff18cea1b17"
dependencies = [
 "autocfg",
 "scopeguard",
 "serde",
]

[[package]]
name = "log"
version = "0.4.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30bde2b3dc3671ae49d8e2e9f044c7c005836e7a023ee57cffa25ab82764bb9e"

[[package]]
name = "log-panics"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68f9dd8546191c1850ecf67d22f5ff00a935b890d0e84713159a55495cc2ac5f"
dependencies = [
 "backtrace",
 "log",
]

[[package]]
name = "loom"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "419e0dc8046cb947daa77eb95ae174acfbddb7673b4151f56d1eed8e93fbfaca"
dependencies = [
 "cfg-if",
 "generator",
 "scoped-tls",
 "tracing",
 "tracing-subscriber 0.3.19",
]

[[package]]
name = "lru"
version = "0.12.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "234cf4f4a04dc1f57e24b96cc0cd600cf2af460d4161ac5ecdd0af8e1f3b2a38"
dependencies = [
 "hashbrown 0.15.2",
]

[[package]]
name = "lru"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "227748d55f2f0ab4735d87fd623798cb6b664512fe979705f829c9f81c934465"
dependencies = [
 "hashbrown 0.15.2",
]

[[package]]
name = "lz4-sys"
version = "1.11.1+lz4-1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6bd8c0d6c6ed0cd30b3652886bb8711dc4bb01d637a68105a3d5158039b418e6"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "lz4_flex"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75761162ae2b0e580d7e7c390558127e5f01b4194debd6221fd8c207fc80e3f5"

[[package]]
name = "lzma-sys"
version = "0.1.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5fda04ab3764e6cde78b9974eec4f779acaba7c4e84b36eca3cf77c581b85d27"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
]

[[package]]
name = "macro-string"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b27834086c65ec3f9387b096d66e99f221cf081c2b738042aa252bcd41204e3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "malloc_buf"
version = "0.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62bb907fe88d54d8d9ce32a3cceab4218ed2f6b7d35617cafe9adf84e43919cb"
dependencies = [
 "libc",
]

[[package]]
name = "matchers"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8263075bb86c5a1b1427b5ae862e8889656f126e9f77c484496e8b47cf5c5558"
dependencies = [
 "regex-automata 0.1.10",
]

[[package]]
name = "maybe-async"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5cf92c10c7e361d6b99666ec1c6f9805b0bea2c3bd8c78dc6fe98ac5bd78db11"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "memchr"
version = "2.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78ca9ab1a0babb1e7d5695e3530886289c18cf2f87ec19a575a0abdce112e3a3"

[[package]]
name = "memmap2"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd3f7eed9d3848f8b98834af67102b720745c4ec028fcd0aa0239277e7de374f"
dependencies = [
 "libc",
]

[[package]]
name = "memoffset"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "488016bfae457b036d996092f6cb448677611ce4449e970ceaf42695203f218a"
dependencies = [
 "autocfg",
]

[[package]]
name = "merlin"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58c38e2799fc0978b65dfff8023ec7843e2330bb462f19198840b34b6582397d"
dependencies = [
 "byteorder",
 "keccak",
 "rand_core 0.6.4",
 "zeroize",
]

[[package]]
name = "metal"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ecfd3296f8c56b7c1f6fbac3c71cefa9d78ce009850c45000015f206dc7fa21"
dependencies = [
 "bitflags 2.8.0",
 "block",
 "core-graphics-types",
 "foreign-types",
 "log",
 "objc",
 "paste",
]

[[package]]
name = "metrics"
version = "0.23.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "884adb57038347dfbaf2d5065887b6cf4312330dc8e94bc30a1a839bd79d3261"
dependencies = [
 "ahash",
 "portable-atomic",
]

[[package]]
name = "metrics"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a7deb012b3b2767169ff203fadb4c6b0b82b947512e5eb9e0b78c2e186ad9e3"
dependencies = [
 "ahash",
 "portable-atomic",
]

[[package]]
name = "metrics-derive"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3dbdd96ed57d565ec744cba02862d707acf373c5772d152abae6ec5c4e24f6c"
dependencies = [
 "proc-macro2",
 "quote",
 "regex",
 "syn 2.0.98",
]

[[package]]
name = "metrics-exporter-prometheus"
version = "0.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4f0c8427b39666bf970460908b213ec09b3b350f20c0c2eabcbba51704a08e6"
dependencies = [
 "base64 0.22.1",
 "http-body-util",
 "hyper",
 "hyper-rustls",
 "hyper-util",
 "indexmap 2.7.1",
 "ipnet",
 "metrics 0.23.0",
 "metrics-util",
 "quanta",
 "thiserror 1.0.69",
 "tokio",
 "tracing",
]

[[package]]
name = "metrics-util"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4259040465c955f9f2f1a4a8a16dc46726169bca0f88e8fb2dbeced487c3e828"
dependencies = [
 "aho-corasick",
 "crossbeam-epoch",
 "crossbeam-utils",
 "hashbrown 0.14.5",
 "indexmap 2.7.1",
 "metrics 0.23.0",
 "num_cpus",
 "ordered-float",
 "quanta",
 "radix_trie",
 "sketches-ddsketch",
]

[[package]]
name = "mime"
version = "0.3.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6877bb514081ee2a7ff5ef9de3281f14a4dd4bceac4c09388074a6b5df8a139a"

[[package]]
name = "mime_guess"
version = "2.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7c44f8e672c00fe5308fa235f821cb4198414e1c77935c1ab6948d3fd78550e"
dependencies = [
 "mime",
 "unicase",
]

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e3e04debbb59698c15bacbb6d93584a8c0ca9cc3213cb423d31f760d8843ce5"
dependencies = [
 "adler2",
]

[[package]]
name = "mio"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2886843bf800fba2e3377cff24abf6379b4c4d5c6681eaf9ea5b0d15090450bd"
dependencies = [
 "libc",
 "log",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.52.0",
]

[[package]]
name = "mirai-annotations"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9be0862c1b3f26a88803c4a49de6889c10e608b3ee9344e6ef5b45fb37ad3d1"

[[package]]
name = "modular-bitfield"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a53d79ba8304ac1c4f9eb3b9d281f21f7be9d4626f72ce7df4ad8fbde4f38a74"
dependencies = [
 "modular-bitfield-impl",
 "static_assertions",
]

[[package]]
name = "modular-bitfield-impl"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a7d5f7076603ebc68de2dc6a650ec331a062a13abaa346975be747bbfa4b789"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "moka"
version = "0.12.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9321642ca94a4282428e6ea4af8cc2ca4eac48ac7a6a4ea8f33f76d0ce70926"
dependencies = [
 "crossbeam-channel",
 "crossbeam-epoch",
 "crossbeam-utils",
 "loom",
 "parking_lot",
 "portable-atomic",
 "rustc_version 0.4.1",
 "smallvec",
 "tagptr",
 "thiserror 1.0.69",
 "uuid",
]

[[package]]
name = "more-asserts"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fafa6961cabd9c63bcd77a45d7e3b7f3b552b70417831fb0f56db717e72407e"

[[package]]
name = "multiaddr"
version = "0.18.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe6351f60b488e04c1d21bc69e56b89cb3f5e8f5d22557d6e8031bdfd79b6961"
dependencies = [
 "arrayref",
 "byteorder",
 "data-encoding",
 "libp2p-identity",
 "multibase",
 "multihash",
 "percent-encoding",
 "serde",
 "static_assertions",
 "unsigned-varint",
 "url",
]

[[package]]
name = "multibase"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b3539ec3c1f04ac9748a260728e855f261b4977f5c3406612c884564f329404"
dependencies = [
 "base-x",
 "data-encoding",
 "data-encoding-macro",
]

[[package]]
name = "multihash"
version = "0.19.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b430e7953c29dd6a09afc29ff0bb69c6e306329ee6794700aee27b76a1aea8d"
dependencies = [
 "core2",
 "unsigned-varint",
]

[[package]]
name = "nibble_vec"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77a5d83df9f36fe23f0c3648c6bbb8b0298bb5f1939c8f2704431371f4b84d43"
dependencies = [
 "smallvec",
]

[[package]]
name = "nix"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "71e2746dc3a24dd78b3cfcb7be93368c6de9963d30f43a6a73998a9cf4b17b46"
dependencies = [
 "bitflags 2.8.0",
 "cfg-if",
 "cfg_aliases",
 "libc",
]

[[package]]
name = "no_std_strings"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5b0c77c1b780822bc749a33e39aeb2c07584ab93332303babeabb645298a76e"

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "notify"
version = "8.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fee8403b3d66ac7b26aee6e40a897d85dc5ce26f44da36b8b73e987cc52e943"
dependencies = [
 "bitflags 2.8.0",
 "filetime",
 "fsevent-sys",
 "inotify",
 "kqueue",
 "libc",
 "log",
 "mio",
 "notify-types",
 "walkdir",
 "windows-sys 0.59.0",
]

[[package]]
name = "notify-types"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e0826a989adedc2a244799e823aece04662b66609d96af8dff7ac6df9a8925d"

[[package]]
name = "ntapi"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a3895c6391c39d7fe7ebc444a87eb2991b2a0bc718fdabd071eec617fc68e4"
dependencies = [
 "winapi",
]

[[package]]
name = "nu-ansi-term"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77a8165726e8236064dbb45459242600304b42a5ea24ee2948e18e023bf7ba84"
dependencies = [
 "overload",
 "winapi",
]

[[package]]
name = "num"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35bd024e8b2ff75562e5f34e7f4905839deb4b22955ef5e73d2fea1b9813cb23"
dependencies = [
 "num-bigint",
 "num-complex",
 "num-integer",
 "num-iter",
 "num-rational",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5e44f723f1133c9deac646763579fdb3ac745e418f2a7af9cd0c431da1f20b9"
dependencies = [
 "num-integer",
 "num-traits",
 "serde",
]

[[package]]
name = "num-complex"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73f88a1307638156682bada9d7604135552957b7818057dcef22705b4d509495"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-derive"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "876a53fff98e03a936a674b29568b0e605f06b29372c2489ff4de23f1949743d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "num-integer"
version = "0.1.46"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7969661fd2958a5cb096e56c8e1ad0444ac2bbcd0061bd28660485a44879858f"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-iter"
version = "0.1.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1429034a0490724d0075ebb2bc9e875d6503c3cf69e235a8941aa757d83ef5bf"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f83d14da390562dca69fc84082e73e548e1ad308d24accdedd2720017cb37824"
dependencies = [
 "num-bigint",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "071dfc062690e90b734c0b2273ce72ad0ffa95f0c74596bc250dcfd960262841"
dependencies = [
 "autocfg",
 "libm",
]

[[package]]
name = "num_cpus"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4161fcb6d602d4d2081af7c3a45852d875a03dd337a6bfdd6e06407b61342a43"
dependencies = [
 "hermit-abi 0.3.9",
 "libc",
]

[[package]]
name = "num_enum"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e613fc340b2220f734a8595782c551f1250e969d87d3be1ae0579e8d4065179"
dependencies = [
 "num_enum_derive",
]

[[package]]
name = "num_enum_derive"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af1844ef2428cc3e1cb900be36181049ef3d3193c63e43026cfe202983b27a56"
dependencies = [
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "num_threads"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c7398b9c8b70908f6371f47ed36737907c87c52af34c268fed0bf0ceb92ead9"
dependencies = [
 "libc",
]

[[package]]
name = "number_prefix"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830b246a0e5f20af87141b25c173cd1b609bd7779a4617d6ec582abaf90870f3"

[[package]]
name = "nybbles"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8983bb634df7248924ee0c4c3a749609b5abcb082c28fffe3254b3eb3602b307"
dependencies = [
 "alloy-rlp",
 "const-hex",
 "proptest",
 "serde",
 "smallvec",
]

[[package]]
name = "objc"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "915b1b472bc21c53464d6c8461c9d3af805ba1ef837e1cac254428f4a77177b1"
dependencies = [
 "malloc_buf",
]

[[package]]
name = "object"
version = "0.36.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62948e14d923ea95ea2c7c86c71013138b66525b86bdc08d2dcc262bdb497b87"
dependencies = [
 "memchr",
]

[[package]]
name = "once_cell"
version = "1.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d75b0bedcc4fe52caa0e03d9f1151a323e4aa5e2d78ba3580400cd3c9e2bc4bc"
dependencies = [
 "critical-section",
 "portable-atomic",
]

[[package]]
name = "oorandom"
version = "11.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b410bbe7e14ab526a0e86877eb47c6996a2bd7746f027ba551028c925390e4e9"

[[package]]
name = "op-alloy-consensus"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91764ebe0eddf6e3cfff41650332ff4e01defe372386027703f2e7e334734a05"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-serde",
 "derive_more 2.0.1",
 "serde",
 "thiserror 2.0.12",
]

[[package]]
name = "op-alloy-rpc-types-engine"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc26f8288839926d0137d39d70628bb5ac00fca449bab24c54cebd66c71b9cf4"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "derive_more 2.0.1",
 "ethereum_ssz",
 "op-alloy-consensus",
 "snap",
 "thiserror 2.0.12",
]

[[package]]
name = "op-revm"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e981d234dcfd3a3de7480e5a5cf7439071af39d15b7d258188cc4c69b9d1f26e"
dependencies = [
 "auto_impl",
 "once_cell",
 "revm",
 "serde",
]

[[package]]
name = "opaque-debug"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c08d65885ee38876c4f86fa503fb49d7b507c2b62552df7c70b2fce627e06381"

[[package]]
name = "openssl-probe"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d05e27ee213611ffe7d6348b942e8f942b37114c00cc03cec254295a4a17852e"

[[package]]
name = "option-ext"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04744f49eae99ab78e0d5c0b603ab218f515ea8cfe5a456d7629ad883a3b6e7d"

[[package]]
name = "ordered-float"
version = "4.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bb71e1b3fa6ca1c61f383464aaf2bb0e2f8e772a1f01d486832464de363b951"
dependencies = [
 "num-traits",
]

[[package]]
name = "overload"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b15813163c1d831bf4a13c3610c05c0d03b39feb07f7e09fa234dac9b15aaf39"

[[package]]
name = "p256"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9863ad85fa8f4460f9c48cb909d38a0d689dba1f6f6988a5e3e0d31071bcd4b"
dependencies = [
 "ecdsa",
 "elliptic-curve",
 "primeorder",
 "sha2 0.10.8",
]

[[package]]
name = "page_size"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30d5b2194ed13191c1999ae0704b7839fb18384fa22e49b57eeaa97d79ce40da"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "parity-scale-codec"
version = "3.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "306800abfa29c7f16596b5970a588435e3d5b3149683d00c12b699cc19f895ee"
dependencies = [
 "arrayvec",
 "bitvec",
 "byte-slice-cast",
 "bytes",
 "impl-trait-for-tuples",
 "parity-scale-codec-derive",
 "serde",
]

[[package]]
name = "parity-scale-codec-derive"
version = "3.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d830939c76d294956402033aee57a6da7b438f2294eb94864c37b0569053a42c"
dependencies = [
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "parking_lot"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1bf18183cf54e8d6059647fc3063646a1801cf30896933ec2311622cc4b9a27"
dependencies = [
 "lock_api",
 "parking_lot_core",
]

[[package]]
name = "parking_lot_core"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e401f977ab385c9e4e3ab30627d6f26d00e2c73eef317493c4ec6d468726cf8"
dependencies = [
 "cfg-if",
 "libc",
 "redox_syscall",
 "smallvec",
 "windows-targets 0.52.6",
]

[[package]]
name = "paste"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57c0d7b74b563b49d38dae00a0c37d4d6de9b432382b2892f0574ddcae73fd0a"

[[package]]
name = "pem"
version = "3.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38af38e8470ac9dee3ce1bae1af9c1671fffc44ddfd8bd1d0a3445bf349a8ef3"
dependencies = [
 "base64 0.22.1",
 "serde",
]

[[package]]
name = "pem-rfc7468"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88b39c9bfcfc231068454382784bb460aae594343fb030d46e9f50a645418412"
dependencies = [
 "base64ct",
]

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "pest"
version = "2.7.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "879952a81a83930934cbf1786752d6dedc3b1f29e8f8fb2ad1d0a36f377cf442"
dependencies = [
 "memchr",
 "thiserror 1.0.69",
 "ucd-trie",
]

[[package]]
name = "phf"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd6780a80ae0c52cc120a26a1a42c1ae51b247a253e4e06113d23d2c2edd078"
dependencies = [
 "phf_macros",
 "phf_shared",
 "serde",
]

[[package]]
name = "phf_generator"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c80231409c20246a13fddb31776fb942c38553c51e871f8cbd687a4cfb5843d"
dependencies = [
 "phf_shared",
 "rand 0.8.5",
]

[[package]]
name = "phf_macros"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f84ac04429c13a7ff43785d75ad27569f2951ce0ffd30a3321230db2fc727216"
dependencies = [
 "phf_generator",
 "phf_shared",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "phf_shared"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67eabc2ef2a60eb7faa00097bd1ffdb5bd28e62bf39990626a582201b7a754e5"
dependencies = [
 "siphasher",
]

[[package]]
name = "pin-project"
version = "1.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfe2e71e1471fe07709406bf725f710b02927c9c54b2b5b2ec0e8087d97c327d"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6e859e6e5bd50440ab63c47e3ebabc90f26251f7c73c3d3e837b74a1cc3fa67"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "pin-project-lite"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b3cff922bd51709b605d9ead9aa71031d81447142d828eb4a6eba76fe619f9b"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "pkcs8"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f950b2377845cebe5cf8b5165cb3cc1a5e0fa5cfa3e1f7f55707d8fd82e0a7b7"
dependencies = [
 "der",
 "spki",
]

[[package]]
name = "pkg-config"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "953ec861398dccce10c670dfeaf3ec4911ca479e9c02154b3a215178c5f566f2"

[[package]]
name = "plotters"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5aeb6f403d7a4911efb1e33402027fc44f29b5bf6def3effcc22d7bb75f2b747"
dependencies = [
 "num-traits",
 "plotters-backend",
 "plotters-svg",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "plotters-backend"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df42e13c12958a16b3f7f4386b9ab1f3e7933914ecea48da7139435263a4172a"

[[package]]
name = "plotters-svg"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51bae2ac328883f7acdfea3d66a7c35751187f870bc81f94563733a154d7a670"
dependencies = [
 "plotters-backend",
]

[[package]]
name = "pollster"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f3a9f18d041e6d0e102a0a46750538147e5e8992d3b4873aaafee2520b00ce3"

[[package]]
name = "polyval"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d1fe60d06143b2430aa532c94cfe9e29783047f06c0d7fd359a9a51b729fa25"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "opaque-debug",
 "universal-hash",
]

[[package]]
name = "portable-atomic"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "350e9b48cbc6b0e028b0473b114454c6316e57336ee184ceab6e53f72c178b3e"

[[package]]
name = "postcard"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "170a2601f67cc9dba8edd8c4870b15f71a6a2dc196daec8c83f72b59dff628a8"
dependencies = [
 "cobs",
 "embedded-io 0.4.0",
 "embedded-io 0.6.1",
 "serde",
]

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "ppv-lite86"
version = "0.2.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77957b295656769bb8ad2b6a6b09d897d94f05c41b069aede1fcdaa675eaea04"
dependencies = [
 "zerocopy 0.7.35",
]

[[package]]
name = "prettyplease"
version = "0.2.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6924ced06e1f7dfe3fa48d57b9f74f55d8915f5036121bef647ef4b204895fac"
dependencies = [
 "proc-macro2",
 "syn 2.0.98",
]

[[package]]
name = "primeorder"
version = "0.13.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "353e1ca18966c16d9deb1c69278edbc5f194139612772bd9537af60ac231e1e6"
dependencies = [
 "elliptic-curve",
]

[[package]]
name = "primitive-types"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b34d9fd68ae0b74a41b21c03c2f62847aa0ffea044eee893b4c140b37e244e2"
dependencies = [
 "fixed-hash",
 "impl-codec",
 "uint 0.9.5",
]

[[package]]
name = "proc-macro-crate"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecf48c7ca261d60b74ab1a7b20da18bede46776b2e55535cb958eb595c5fa7b"
dependencies = [
 "toml_edit",
]

[[package]]
name = "proc-macro-error"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c"
dependencies = [
 "proc-macro-error-attr",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869"
dependencies = [
 "proc-macro2",
 "quote",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96de42df36bb9bba5542fe9f1a054b8cc87e172759a1868aa05c1f3acc89dfc5"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "proc-macro-error2"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11ec05c52be0a07b08061f7dd003e7d7092e0472bc731b4af7bb1ef876109802"
dependencies = [
 "proc-macro-error-attr2",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "proc-macro2"
version = "1.0.93"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60946a68e5f9d28b0dc1c21bb8a97ee7d018a8b322fa57838ba31cc878e22d99"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "proptest"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14cae93065090804185d3b75f0bf93b8eeda30c7a9b4a33d3bdb3988d6229e50"
dependencies = [
 "bit-set",
 "bit-vec",
 "bitflags 2.8.0",
 "lazy_static",
 "num-traits",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rand_xorshift",
 "regex-syntax 0.8.5",
 "rusty-fork",
 "tempfile",
 "unarray",
]

[[package]]
name = "prost"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2796faa41db3ec313a31f7624d9286acf277b52de526150b7e69f3debf891ee5"
dependencies = [
 "bytes",
 "prost-derive",
]

[[package]]
name = "prost-derive"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a56d757972c98b346a9b766e3f02746cde6dd1cd1d1d563472929fdd74bec4d"
dependencies = [
 "anyhow",
 "itertools 0.14.0",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "prover-services"
version = "0.7.2"
dependencies = [
 "anyhow",
 "async-trait",
 "borsh",
 "metrics 0.23.0",
 "metrics-derive",
 "rand 0.8.5",
 "sov-mock-da",
 "sov-mock-zkvm",
 "sov-rollup-interface",
 "tempfile",
 "tokio",
 "tracing",
 "uuid",
]

[[package]]
name = "quanta"
version = "0.12.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bd1fe6824cea6538803de3ff1bc0cf3949024db3d43c9643024bfb33a807c0e"
dependencies = [
 "crossbeam-utils",
 "libc",
 "once_cell",
 "raw-cpuid",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "web-sys",
 "winapi",
]

[[package]]
name = "quick-error"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d01941d82fa2ab50be1e79e6714289dd7cde78eba4c074bc5a4374f650dfe0"

[[package]]
name = "quick-protobuf"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d6da84cc204722a989e01ba2f6e1e276e190f22263d0cb6ce8526fcdb0d2e1f"
dependencies = [
 "byteorder",
]

[[package]]
name = "quinn"
version = "0.11.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c7c5fdde3cdae7203427dc4f0a68fe0ed09833edc525a03456b153b79828684"
dependencies = [
 "bytes",
 "pin-project-lite",
 "quinn-proto",
 "quinn-udp",
 "rustc-hash 2.1.1",
 "rustls",
 "socket2",
 "thiserror 1.0.69",
 "tokio",
 "tracing",
]

[[package]]
name = "quinn-proto"
version = "0.11.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fadfaed2cd7f389d0161bb73eeb07b7b78f8691047a6f3e73caaeae55310a4a6"
dependencies = [
 "bytes",
 "rand 0.8.5",
 "ring",
 "rustc-hash 2.1.1",
 "rustls",
 "slab",
 "thiserror 1.0.69",
 "tinyvec",
 "tracing",
]

[[package]]
name = "quinn-udp"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c40286217b4ba3a71d644d752e6a0b71f13f1b6a2c5311acfcbe0c2418ed904"
dependencies = [
 "cfg_aliases",
 "libc",
 "once_cell",
 "socket2",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "quote"
version = "1.0.38"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e4dccaaaf89514f546c693ddc140f729f958c247918a13380cccc6078391acc"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "r-efi"
version = "5.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74765f6d916ee2faa39bc8e68e4f3ed8949b48cccdac59983d287a7cb71ce9c5"

[[package]]
name = "radium"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc33ff2d4973d518d823d61aa239014831e521c75da58e3df4840d3f47749d09"

[[package]]
name = "radix_trie"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c069c179fcdc6a2fe24d8d18305cf085fdbd4f922c041943e203685d6a1c58fd"
dependencies = [
 "endian-type",
 "nibble_vec",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
 "serde",
]

[[package]]
name = "rand"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3779b94aeb87e8bd4e834cee3650289ee9e0d5677f976ecdb6d219e5f4f6cd94"
dependencies = [
 "rand_chacha 0.9.0",
 "rand_core 0.9.3",
 "zerocopy 0.8.23",
]

[[package]]
name = "rand_chacha"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6c10a63a0fa32252be49d21e7709d4d4baf8d231c2dbce1eaa8141b9b127d88"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_chacha"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3022b5f1df60f26e1ffddd6c66e8aa15de382ae63b3a0c1bfc0e4d3e3f325cb"
dependencies = [
 "ppv-lite86",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_core"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec0be4795e2f6a28069bec0b5ff3e2ac9bafc99e6a9a7dc3547996c5c816922c"
dependencies = [
 "getrandom 0.2.15",
]

[[package]]
name = "rand_core"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99d9a13982dcf210057a8a78572b2217b667c3beacbf3a0d8b454f6f82837d38"
dependencies = [
 "getrandom 0.3.2",
]

[[package]]
name = "rand_xorshift"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d25bf25ec5ae4a3f1b92f929810509a2f53d7dca2f50b794ff57e3face536c8f"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "raw-cpuid"
version = "11.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "529468c1335c1c03919960dfefdb1b3648858c20d7ec2d0663e728e4a717efbc"
dependencies = [
 "bitflags 2.8.0",
]

[[package]]
name = "rayon"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b418a60154510ca1a002a752ca9714984e21e4241e804d32555251faf8b78ffa"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "redox_syscall"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82b568323e98e49e2a0899dcee453dd679fae22d69adf9b11dd508d1549b7e2f"
dependencies = [
 "bitflags 2.8.0",
]

[[package]]
name = "redox_users"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba009ff324d1fc1b900bd1fdb31564febe58a8ccc8a6fdbb93b543d33b13ca43"
dependencies = [
 "getrandom 0.2.15",
 "libredox",
 "thiserror 1.0.69",
]

[[package]]
name = "regex"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b544ef1b4eac5dc2db33ea63606ae9ffcfac26c1416a2806ae0bf5f56b201191"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata 0.4.9",
 "regex-syntax 0.8.5",
]

[[package]]
name = "regex-automata"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c230d73fb8d8c1b9c0b3135c5142a8acee3a0558fb8db5cf1cb65f8d7862132"
dependencies = [
 "regex-syntax 0.6.29",
]

[[package]]
name = "regex-automata"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "809e8dc61f6de73b46c85f4c96486310fe304c434cfa43669d7b40f711150908"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax 0.8.5",
]

[[package]]
name = "regex-syntax"
version = "0.6.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f162c6dd7b008981e4d40210aca20b4bd0f9b60ca9271061b07f78537722f2e1"

[[package]]
name = "regex-syntax"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b15c43186be67a4fd63bee50d0303afffcef381492ebe2c5d87f324e1b8815c"

[[package]]
name = "regress"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1541daf4e4ed43a0922b7969bdc2170178bcacc5dabf7e39bc508a9fa3953a7a"
dependencies = [
 "hashbrown 0.14.5",
 "memchr",
]

[[package]]
name = "relative-path"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba39f3699c378cd8970968dcbff9c43159ea4cfbd88d43c00b22f2ef10a435d2"

[[package]]
name = "reqwest"
version = "0.12.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43e734407157c3c2034e0258f5e4473ddb361b1e85f95a66690d67264d7cd1da"
dependencies = [
 "base64 0.22.1",
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "http-body-util",
 "hyper",
 "hyper-rustls",
 "hyper-util",
 "ipnet",
 "js-sys",
 "log",
 "mime",
 "once_cell",
 "percent-encoding",
 "pin-project-lite",
 "quinn",
 "rustls",
 "rustls-pemfile",
 "rustls-pki-types",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "sync_wrapper",
 "tokio",
 "tokio-rustls",
 "tokio-util",
 "tower 0.5.2",
 "tower-service",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "wasm-streams",
 "web-sys",
 "webpki-roots",
 "windows-registry",
]

[[package]]
name = "resolv-conf"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48375394603e3dd4b2d64371f7148fd8c7baa2680e28741f2cb8d23b59e3d4c4"
dependencies = [
 "hostname",
]

[[package]]
name = "reth-basic-payload-builder"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "futures-core",
 "futures-util",
 "metrics 0.24.1",
 "reth-chain-state",
 "reth-metrics",
 "reth-payload-builder",
 "reth-payload-builder-primitives",
 "reth-payload-primitives",
 "reth-primitives-traits",
 "reth-revm",
 "reth-storage-api",
 "reth-tasks",
 "tokio",
 "tracing",
]

[[package]]
name = "reth-chain-state"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "derive_more 2.0.1",
 "metrics 0.24.1",
 "parking_lot",
 "pin-project",
 "reth-chainspec",
 "reth-errors",
 "reth-ethereum-primitives",
 "reth-execution-types",
 "reth-metrics",
 "reth-primitives-traits",
 "reth-storage-api",
 "reth-trie",
 "revm-database",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "reth-chainspec"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-chains",
 "alloy-consensus",
 "alloy-eips",
 "alloy-evm",
 "alloy-genesis",
 "alloy-primitives",
 "alloy-trie",
 "auto_impl",
 "derive_more 2.0.1",
 "reth-ethereum-forks",
 "reth-network-peers",
 "reth-primitives-traits",
 "serde_json",
]

[[package]]
name = "reth-cli-util"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "cfg-if",
 "eyre",
 "libc",
 "rand 0.8.5",
 "reth-fs-util",
 "secp256k1 0.30.0",
 "serde",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-codecs"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-genesis",
 "alloy-primitives",
 "alloy-trie",
 "bytes",
 "modular-bitfield",
 "op-alloy-consensus",
 "reth-codecs-derive",
 "reth-zstd-compressors",
 "serde",
]

[[package]]
name = "reth-codecs-derive"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "convert_case",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "reth-config"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "eyre",
 "humantime-serde",
 "reth-network-types",
 "reth-prune-types",
 "reth-stages-types",
 "serde",
 "toml",
]

[[package]]
name = "reth-consensus"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-primitives",
 "auto_impl",
 "reth-execution-types",
 "reth-primitives-traits",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-db"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "derive_more 2.0.1",
 "eyre",
 "metrics 0.24.1",
 "page_size",
 "reth-db-api",
 "reth-fs-util",
 "reth-libmdbx",
 "reth-metrics",
 "reth-nippy-jar",
 "reth-static-file-types",
 "reth-storage-errors",
 "reth-tracing",
 "rustc-hash 2.1.1",
 "strum 0.27.1",
 "sysinfo",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-db-api"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-genesis",
 "alloy-primitives",
 "bytes",
 "derive_more 2.0.1",
 "metrics 0.24.1",
 "modular-bitfield",
 "parity-scale-codec",
 "reth-codecs",
 "reth-db-models",
 "reth-ethereum-primitives",
 "reth-primitives-traits",
 "reth-prune-types",
 "reth-stages-types",
 "reth-storage-errors",
 "reth-trie-common",
 "roaring",
 "serde",
]

[[package]]
name = "reth-db-models"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "bytes",
 "modular-bitfield",
 "reth-codecs",
 "reth-primitives-traits",
 "serde",
]

[[package]]
name = "reth-discv4"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "discv5",
 "enr",
 "generic-array",
 "itertools 0.14.0",
 "parking_lot",
 "rand 0.8.5",
 "reth-ethereum-forks",
 "reth-net-banlist",
 "reth-net-nat",
 "reth-network-peers",
 "schnellru",
 "secp256k1 0.30.0",
 "serde",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "reth-discv5"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "derive_more 2.0.1",
 "discv5",
 "enr",
 "futures",
 "itertools 0.14.0",
 "metrics 0.24.1",
 "rand 0.8.5",
 "reth-chainspec",
 "reth-ethereum-forks",
 "reth-metrics",
 "reth-network-peers",
 "secp256k1 0.30.0",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
]

[[package]]
name = "reth-dns-discovery"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "data-encoding",
 "enr",
 "hickory-resolver",
 "linked_hash_set",
 "parking_lot",
 "reth-ethereum-forks",
 "reth-network-peers",
 "reth-tokio-util",
 "schnellru",
 "secp256k1 0.30.0",
 "serde",
 "serde_with",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "reth-ecies"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "aes",
 "alloy-primitives",
 "alloy-rlp",
 "block-padding",
 "byteorder",
 "cipher",
 "concat-kdf",
 "ctr",
 "digest 0.10.7",
 "futures",
 "generic-array",
 "hmac 0.12.1",
 "pin-project",
 "rand 0.8.5",
 "reth-network-peers",
 "secp256k1 0.30.0",
 "sha2 0.10.8",
 "sha3",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tracing",
 "typenum",
]

[[package]]
name = "reth-engine-primitives"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "auto_impl",
 "futures",
 "reth-chain-state",
 "reth-errors",
 "reth-ethereum-primitives",
 "reth-execution-types",
 "reth-payload-builder-primitives",
 "reth-payload-primitives",
 "reth-primitives-traits",
 "reth-trie",
 "reth-trie-common",
 "serde",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "reth-errors"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "reth-consensus",
 "reth-execution-errors",
 "reth-fs-util",
 "reth-storage-errors",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-eth-wire"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-chains",
 "alloy-primitives",
 "alloy-rlp",
 "bytes",
 "derive_more 2.0.1",
 "futures",
 "pin-project",
 "reth-codecs",
 "reth-ecies",
 "reth-eth-wire-types",
 "reth-ethereum-forks",
 "reth-metrics",
 "reth-network-peers",
 "reth-primitives-traits",
 "serde",
 "snap",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tracing",
]

[[package]]
name = "reth-eth-wire-types"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-chains",
 "alloy-consensus",
 "alloy-eips",
 "alloy-hardforks",
 "alloy-primitives",
 "alloy-rlp",
 "bytes",
 "derive_more 2.0.1",
 "reth-chainspec",
 "reth-codecs-derive",
 "reth-ethereum-primitives",
 "reth-primitives-traits",
 "serde",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-ethereum-engine-primitives"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-rpc-types-engine",
 "reth-engine-primitives",
 "reth-ethereum-primitives",
 "reth-payload-primitives",
 "reth-primitives-traits",
 "serde",
 "sha2 0.10.8",
]

[[package]]
name = "reth-ethereum-forks"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eip2124",
 "alloy-hardforks",
 "alloy-primitives",
 "auto_impl",
 "once_cell",
 "rustc-hash 2.1.1",
]

[[package]]
name = "reth-ethereum-primitives"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-evm",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-rpc-types-eth",
 "derive_more 2.0.1",
 "modular-bitfield",
 "rand 0.8.5",
 "reth-codecs",
 "reth-primitives-traits",
 "reth-zstd-compressors",
 "revm-context",
 "secp256k1 0.30.0",
 "serde",
 "serde_with",
]

[[package]]
name = "reth-evm"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-evm",
 "alloy-primitives",
 "auto_impl",
 "derive_more 2.0.1",
 "futures-util",
 "reth-execution-errors",
 "reth-execution-types",
 "reth-primitives-traits",
 "reth-storage-api",
 "reth-storage-errors",
 "reth-trie-common",
 "revm",
]

[[package]]
name = "reth-execution-errors"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-evm",
 "alloy-primitives",
 "alloy-rlp",
 "nybbles",
 "reth-storage-errors",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-execution-types"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-evm",
 "alloy-primitives",
 "derive_more 2.0.1",
 "reth-ethereum-primitives",
 "reth-primitives-traits",
 "reth-trie-common",
 "revm",
 "serde",
 "serde_with",
]

[[package]]
name = "reth-fs-util"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "serde",
 "serde_json",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-libmdbx"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "bitflags 2.8.0",
 "byteorder",
 "dashmap",
 "derive_more 2.0.1",
 "indexmap 2.7.1",
 "parking_lot",
 "reth-mdbx-sys",
 "smallvec",
 "thiserror 2.0.12",
 "tracing",
]

[[package]]
name = "reth-mdbx-sys"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "bindgen 0.70.1",
 "cc",
]

[[package]]
name = "reth-metrics"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "futures",
 "metrics 0.24.1",
 "metrics-derive",
 "tokio",
 "tokio-util",
]

[[package]]
name = "reth-net-banlist"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
]

[[package]]
name = "reth-net-nat"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "futures-util",
 "if-addrs",
 "reqwest",
 "serde_with",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
]

[[package]]
name = "reth-network"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rlp",
 "aquamarine",
 "auto_impl",
 "derive_more 2.0.1",
 "discv5",
 "enr",
 "futures",
 "itertools 0.14.0",
 "metrics 0.24.1",
 "parking_lot",
 "pin-project",
 "rand 0.8.5",
 "reth-chainspec",
 "reth-consensus",
 "reth-discv4",
 "reth-discv5",
 "reth-dns-discovery",
 "reth-ecies",
 "reth-eth-wire",
 "reth-eth-wire-types",
 "reth-ethereum-forks",
 "reth-ethereum-primitives",
 "reth-fs-util",
 "reth-metrics",
 "reth-net-banlist",
 "reth-network-api",
 "reth-network-p2p",
 "reth-network-peers",
 "reth-network-types",
 "reth-primitives-traits",
 "reth-storage-api",
 "reth-tasks",
 "reth-tokio-util",
 "reth-transaction-pool",
 "rustc-hash 2.1.1",
 "schnellru",
 "secp256k1 0.30.0",
 "serde",
 "smallvec",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tracing",
]

[[package]]
name = "reth-network-api"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "alloy-rpc-types-admin",
 "auto_impl",
 "derive_more 2.0.1",
 "enr",
 "futures",
 "reth-eth-wire-types",
 "reth-ethereum-forks",
 "reth-network-p2p",
 "reth-network-peers",
 "reth-network-types",
 "reth-tokio-util",
 "serde",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
]

[[package]]
name = "reth-network-p2p"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "auto_impl",
 "derive_more 2.0.1",
 "futures",
 "reth-consensus",
 "reth-eth-wire-types",
 "reth-ethereum-primitives",
 "reth-network-peers",
 "reth-network-types",
 "reth-primitives-traits",
 "reth-storage-errors",
 "tokio",
 "tracing",
]

[[package]]
name = "reth-network-peers"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "enr",
 "secp256k1 0.30.0",
 "serde_with",
 "thiserror 2.0.12",
 "tokio",
 "url",
]

[[package]]
name = "reth-network-types"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eip2124",
 "humantime-serde",
 "reth-net-banlist",
 "reth-network-peers",
 "serde",
 "serde_json",
 "tracing",
]

[[package]]
name = "reth-nippy-jar"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "anyhow",
 "bincode",
 "derive_more 2.0.1",
 "lz4_flex",
 "memmap2",
 "reth-fs-util",
 "serde",
 "thiserror 2.0.12",
 "tracing",
 "zstd",
]

[[package]]
name = "reth-node-api"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-rpc-types-engine",
 "eyre",
 "reth-basic-payload-builder",
 "reth-consensus",
 "reth-db-api",
 "reth-engine-primitives",
 "reth-evm",
 "reth-network-api",
 "reth-node-core",
 "reth-node-types",
 "reth-payload-builder",
 "reth-payload-builder-primitives",
 "reth-payload-primitives",
 "reth-provider",
 "reth-tasks",
 "reth-tokio-util",
 "reth-transaction-pool",
]

[[package]]
name = "reth-node-core"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "clap",
 "derive_more 2.0.1",
 "dirs-next",
 "eyre",
 "futures",
 "humantime",
 "rand 0.8.5",
 "reth-chainspec",
 "reth-cli-util",
 "reth-config",
 "reth-consensus",
 "reth-db",
 "reth-discv4",
 "reth-discv5",
 "reth-engine-primitives",
 "reth-ethereum-forks",
 "reth-net-nat",
 "reth-network",
 "reth-network-p2p",
 "reth-network-peers",
 "reth-primitives-traits",
 "reth-prune-types",
 "reth-rpc-eth-types",
 "reth-rpc-server-types",
 "reth-rpc-types-compat",
 "reth-stages-types",
 "reth-storage-api",
 "reth-storage-errors",
 "reth-tracing",
 "reth-transaction-pool",
 "secp256k1 0.30.0",
 "serde",
 "shellexpand",
 "strum 0.27.1",
 "thiserror 2.0.12",
 "toml",
 "tracing",
 "vergen",
 "vergen-git2",
]

[[package]]
name = "reth-node-types"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "reth-chainspec",
 "reth-db-api",
 "reth-engine-primitives",
 "reth-payload-primitives",
 "reth-primitives-traits",
 "reth-trie-db",
]

[[package]]
name = "reth-payload-builder"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-rpc-types",
 "futures-util",
 "metrics 0.24.1",
 "reth-chain-state",
 "reth-ethereum-engine-primitives",
 "reth-metrics",
 "reth-payload-builder-primitives",
 "reth-payload-primitives",
 "reth-primitives-traits",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "reth-payload-builder-primitives"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "pin-project",
 "reth-payload-primitives",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "reth-payload-primitives"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "auto_impl",
 "op-alloy-rpc-types-engine",
 "reth-chain-state",
 "reth-chainspec",
 "reth-errors",
 "reth-primitives-traits",
 "serde",
 "thiserror 2.0.12",
 "tokio",
]

[[package]]
name = "reth-primitives"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "once_cell",
 "reth-ethereum-forks",
 "reth-ethereum-primitives",
 "reth-primitives-traits",
 "reth-static-file-types",
]

[[package]]
name = "reth-primitives-traits"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-genesis",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-trie",
 "auto_impl",
 "byteorder",
 "bytes",
 "derive_more 2.0.1",
 "k256",
 "modular-bitfield",
 "once_cell",
 "op-alloy-consensus",
 "rayon",
 "reth-codecs",
 "revm-bytecode",
 "revm-primitives",
 "revm-state",
 "secp256k1 0.30.0",
 "serde",
 "serde_with",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-provider"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "auto_impl",
 "dashmap",
 "eyre",
 "itertools 0.14.0",
 "metrics 0.24.1",
 "notify",
 "parking_lot",
 "rayon",
 "reth-chain-state",
 "reth-chainspec",
 "reth-codecs",
 "reth-db",
 "reth-db-api",
 "reth-errors",
 "reth-ethereum-primitives",
 "reth-evm",
 "reth-execution-types",
 "reth-fs-util",
 "reth-metrics",
 "reth-network-p2p",
 "reth-nippy-jar",
 "reth-node-types",
 "reth-primitives-traits",
 "reth-prune-types",
 "reth-stages-types",
 "reth-static-file-types",
 "reth-storage-api",
 "reth-storage-errors",
 "reth-trie",
 "reth-trie-db",
 "revm-database",
 "strum 0.27.1",
 "tokio",
 "tracing",
]

[[package]]
name = "reth-prune-types"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "derive_more 2.0.1",
 "modular-bitfield",
 "reth-codecs",
 "serde",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-revm"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "reth-primitives-traits",
 "reth-storage-api",
 "reth-storage-errors",
 "reth-trie",
 "revm",
]

[[package]]
name = "reth-rpc"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-dyn-abi",
 "alloy-eips",
 "alloy-evm",
 "alloy-genesis",
 "alloy-network",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-rpc-types",
 "alloy-rpc-types-admin",
 "alloy-rpc-types-beacon",
 "alloy-rpc-types-debug",
 "alloy-rpc-types-engine",
 "alloy-rpc-types-eth",
 "alloy-rpc-types-mev",
 "alloy-rpc-types-trace",
 "alloy-rpc-types-txpool",
 "alloy-serde",
 "alloy-signer",
 "alloy-signer-local",
 "async-trait",
 "derive_more 2.0.1",
 "futures",
 "http",
 "http-body",
 "hyper",
 "jsonrpsee",
 "jsonwebtoken",
 "parking_lot",
 "pin-project",
 "rand 0.8.5",
 "reth-chain-state",
 "reth-chainspec",
 "reth-consensus",
 "reth-engine-primitives",
 "reth-errors",
 "reth-ethereum-primitives",
 "reth-evm",
 "reth-execution-types",
 "reth-metrics",
 "reth-network-api",
 "reth-network-peers",
 "reth-network-types",
 "reth-node-api",
 "reth-primitives-traits",
 "reth-revm",
 "reth-rpc-api",
 "reth-rpc-engine-api",
 "reth-rpc-eth-api",
 "reth-rpc-eth-types",
 "reth-rpc-server-types",
 "reth-rpc-types-compat",
 "reth-storage-api",
 "reth-tasks",
 "reth-transaction-pool",
 "revm",
 "revm-inspectors",
 "revm-primitives",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tower 0.4.13",
 "tracing",
 "tracing-futures",
]

[[package]]
name = "reth-rpc-api"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eips",
 "alloy-genesis",
 "alloy-json-rpc",
 "alloy-primitives",
 "alloy-rpc-types",
 "alloy-rpc-types-admin",
 "alloy-rpc-types-anvil",
 "alloy-rpc-types-beacon",
 "alloy-rpc-types-debug",
 "alloy-rpc-types-engine",
 "alloy-rpc-types-eth",
 "alloy-rpc-types-mev",
 "alloy-rpc-types-trace",
 "alloy-rpc-types-txpool",
 "alloy-serde",
 "jsonrpsee",
 "reth-engine-primitives",
 "reth-network-peers",
 "reth-rpc-eth-api",
]

[[package]]
name = "reth-rpc-engine-api"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "async-trait",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "metrics 0.24.1",
 "parking_lot",
 "reth-chainspec",
 "reth-engine-primitives",
 "reth-metrics",
 "reth-payload-builder",
 "reth-payload-builder-primitives",
 "reth-payload-primitives",
 "reth-primitives-traits",
 "reth-rpc-api",
 "reth-rpc-server-types",
 "reth-storage-api",
 "reth-tasks",
 "reth-transaction-pool",
 "serde",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
]

[[package]]
name = "reth-rpc-eth-api"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-dyn-abi",
 "alloy-eips",
 "alloy-json-rpc",
 "alloy-network",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-rpc-types-eth",
 "alloy-rpc-types-mev",
 "alloy-serde",
 "async-trait",
 "auto_impl",
 "dyn-clone",
 "futures",
 "jsonrpsee",
 "jsonrpsee-types",
 "parking_lot",
 "reth-chainspec",
 "reth-errors",
 "reth-evm",
 "reth-network-api",
 "reth-node-api",
 "reth-payload-builder",
 "reth-primitives-traits",
 "reth-provider",
 "reth-revm",
 "reth-rpc-eth-types",
 "reth-rpc-server-types",
 "reth-rpc-types-compat",
 "reth-tasks",
 "reth-transaction-pool",
 "reth-trie-common",
 "revm",
 "revm-inspectors",
 "tokio",
 "tracing",
]

[[package]]
name = "reth-rpc-eth-types"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-eth",
 "alloy-sol-types",
 "derive_more 2.0.1",
 "futures",
 "itertools 0.14.0",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "metrics 0.24.1",
 "rand 0.8.5",
 "reth-chain-state",
 "reth-chainspec",
 "reth-errors",
 "reth-ethereum-primitives",
 "reth-evm",
 "reth-execution-types",
 "reth-metrics",
 "reth-primitives-traits",
 "reth-revm",
 "reth-rpc-server-types",
 "reth-rpc-types-compat",
 "reth-storage-api",
 "reth-tasks",
 "reth-transaction-pool",
 "reth-trie",
 "revm",
 "revm-inspectors",
 "schnellru",
 "serde",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "reth-rpc-server-types"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "reth-errors",
 "reth-network-api",
 "serde",
 "strum 0.27.1",
]

[[package]]
name = "reth-rpc-types-compat"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-primitives",
 "alloy-rpc-types-eth",
 "jsonrpsee-types",
 "reth-primitives-traits",
 "serde",
]

[[package]]
name = "reth-stages-types"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "bytes",
 "modular-bitfield",
 "reth-codecs",
 "reth-trie-common",
 "serde",
]

[[package]]
name = "reth-static-file-types"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "derive_more 2.0.1",
 "serde",
 "strum 0.27.1",
]

[[package]]
name = "reth-storage-api"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rpc-types-engine",
 "auto_impl",
 "reth-chainspec",
 "reth-db-api",
 "reth-db-models",
 "reth-ethereum-primitives",
 "reth-execution-types",
 "reth-primitives-traits",
 "reth-prune-types",
 "reth-stages-types",
 "reth-storage-errors",
 "reth-trie-common",
 "reth-trie-db",
 "revm-database",
]

[[package]]
name = "reth-storage-errors"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-eips",
 "alloy-primitives",
 "alloy-rlp",
 "derive_more 2.0.1",
 "reth-primitives-traits",
 "reth-prune-types",
 "reth-static-file-types",
 "revm-database-interface",
 "thiserror 2.0.12",
]

[[package]]
name = "reth-tasks"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "auto_impl",
 "dyn-clone",
 "futures-util",
 "metrics 0.24.1",
 "pin-project",
 "rayon",
 "reth-metrics",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "tracing-futures",
]

[[package]]
name = "reth-tokio-util"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "reth-tracing"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "clap",
 "eyre",
 "rolling-file",
 "tracing",
 "tracing-appender",
 "tracing-journald",
 "tracing-logfmt",
 "tracing-subscriber 0.3.19",
]

[[package]]
name = "reth-transaction-pool"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rlp",
 "aquamarine",
 "auto_impl",
 "bitflags 2.8.0",
 "futures-util",
 "metrics 0.24.1",
 "parking_lot",
 "rand 0.8.5",
 "reth-chain-state",
 "reth-chainspec",
 "reth-eth-wire-types",
 "reth-ethereum-primitives",
 "reth-execution-types",
 "reth-fs-util",
 "reth-metrics",
 "reth-primitives-traits",
 "reth-storage-api",
 "reth-tasks",
 "revm-interpreter",
 "revm-primitives",
 "rustc-hash 2.1.1",
 "schnellru",
 "serde",
 "smallvec",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tracing",
]

[[package]]
name = "reth-trie"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-eips",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-trie",
 "auto_impl",
 "itertools 0.14.0",
 "metrics 0.24.1",
 "reth-execution-errors",
 "reth-metrics",
 "reth-primitives-traits",
 "reth-stages-types",
 "reth-storage-errors",
 "reth-trie-common",
 "reth-trie-sparse",
 "revm-database",
 "tracing",
]

[[package]]
name = "reth-trie-common"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-consensus",
 "alloy-primitives",
 "alloy-rlp",
 "alloy-rpc-types-eth",
 "alloy-serde",
 "alloy-trie",
 "bytes",
 "derive_more 2.0.1",
 "itertools 0.14.0",
 "nybbles",
 "rayon",
 "reth-codecs",
 "reth-primitives-traits",
 "revm-database",
 "serde",
 "serde_with",
]

[[package]]
name = "reth-trie-db"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "reth-db-api",
 "reth-execution-errors",
 "reth-primitives-traits",
 "reth-trie",
 "tracing",
]

[[package]]
name = "reth-trie-sparse"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "auto_impl",
 "metrics 0.24.1",
 "reth-execution-errors",
 "reth-metrics",
 "reth-primitives-traits",
 "reth-tracing",
 "reth-trie-common",
 "smallvec",
]

[[package]]
name = "reth-zstd-compressors"
version = "1.3.7"
source = "git+https://github.com/paradigmxyz/reth?tag=v1.3.7#ed7da87da4de340a437bf46f39a7e1397ac82065"
dependencies = [
 "zstd",
]

[[package]]
name = "revm"
version = "21.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7db41167e2a1fddb734984cc26e4bf0a0cb298829d1c488b4de37bda764e1d47"
dependencies = [
 "revm-bytecode",
 "revm-context",
 "revm-context-interface",
 "revm-database",
 "revm-database-interface",
 "revm-handler",
 "revm-inspector",
 "revm-interpreter",
 "revm-precompile",
 "revm-primitives",
 "revm-state",
]

[[package]]
name = "revm-bytecode"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdc3ae92c0c071f4a5ac3ef398fed50bacf8ebd5495d2afded34c60874afa7a3"
dependencies = [
 "bitvec",
 "phf",
 "revm-primitives",
 "serde",
]

[[package]]
name = "revm-context"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5fd5d8a35cf33d2494e32a966ebee6bc23dea9b1fbc3477c5b58e42ddceaa5b"
dependencies = [
 "cfg-if",
 "derive-where",
 "revm-bytecode",
 "revm-context-interface",
 "revm-database-interface",
 "revm-primitives",
 "revm-state",
 "serde",
]

[[package]]
name = "revm-context-interface"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8253163a7868c86b88dc76a193724b8c6252bf260dc1cf11d814a5f4fa7a804"
dependencies = [
 "alloy-eip2930",
 "alloy-eip7702",
 "auto_impl",
 "revm-database-interface",
 "revm-primitives",
 "revm-state",
 "serde",
]

[[package]]
name = "revm-database"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbb40baf1ec91bfda68a37a9be72c5d089e2b662532689209cb2e0febe1eb64c"
dependencies = [
 "alloy-eips",
 "auto_impl",
 "revm-bytecode",
 "revm-database-interface",
 "revm-primitives",
 "revm-state",
 "serde",
]

[[package]]
name = "revm-database-interface"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c541612673da04df1ab3a6a56127851e93a5d05539eb915a6c541d24e7c5902"
dependencies = [
 "auto_impl",
 "revm-primitives",
 "revm-state",
 "serde",
]

[[package]]
name = "revm-handler"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f55164c03c05eace53cf7f64df5dff14c7769956e6f2b9e4acb88301dc7537c"
dependencies = [
 "auto_impl",
 "revm-bytecode",
 "revm-context",
 "revm-context-interface",
 "revm-database-interface",
 "revm-interpreter",
 "revm-precompile",
 "revm-primitives",
 "revm-state",
 "serde",
]

[[package]]
name = "revm-inspector"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62f67d36e1abebe20b891b7ef57de3af2addfbc2d9cd4ea3f49ade8a67d0e79d"
dependencies = [
 "auto_impl",
 "revm-context",
 "revm-database-interface",
 "revm-handler",
 "revm-interpreter",
 "revm-primitives",
 "revm-state",
 "serde",
 "serde_json",
]

[[package]]
name = "revm-inspectors"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a32ec21c38a85f83773e6b3cdb7060aae8ac9edb291118fbfd4da7f2a50e620"
dependencies = [
 "alloy-primitives",
 "alloy-rpc-types-eth",
 "alloy-rpc-types-trace",
 "alloy-sol-types",
 "anstyle",
 "boa_engine",
 "boa_gc",
 "colorchoice",
 "revm",
 "serde",
 "serde_json",
 "thiserror 2.0.12",
]

[[package]]
name = "revm-interpreter"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3cd45ea4fdee2c3f430df4ddb4936dc85c49dc5a7ce9838a8b9ad6861ab153c6"
dependencies = [
 "revm-bytecode",
 "revm-context-interface",
 "revm-primitives",
 "serde",
]

[[package]]
name = "revm-precompile"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac48995560dd5ea15e3788106bdf8893186d945bd40d674fb63aa351cf2e58fa"
dependencies = [
 "ark-bls12-381",
 "ark-bn254",
 "ark-ec",
 "ark-ff 0.5.0",
 "ark-serialize 0.5.0",
 "aurora-engine-modexp",
 "blst",
 "c-kzg",
 "cfg-if",
 "k256",
 "libsecp256k1",
 "once_cell",
 "p256",
 "revm-primitives",
 "ripemd",
 "secp256k1 0.30.0",
 "sha2 0.10.8",
 "substrate-bn",
]

[[package]]
name = "revm-primitives"
version = "17.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb9b235b3c03299a531717ae4f9ee6bdb4c1a1755c9f8ce751298d1c99d95fc3"
dependencies = [
 "alloy-primitives",
 "enumn",
 "serde",
]

[[package]]
name = "revm-state"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfdff0435bd0cb9e1f9dcc44eaea581973b0550cb897ce368d43259922b1c241"
dependencies = [
 "bitflags 2.8.0",
 "revm-bytecode",
 "revm-primitives",
 "serde",
]

[[package]]
name = "rfc6979"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8dd2a808d456c4a54e300a23e9f5a67e122c3024119acbfd73e3bf664491cb2"
dependencies = [
 "hmac 0.12.1",
 "subtle",
]

[[package]]
name = "ring"
version = "0.17.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed9b823fa29b721a59671b41d6b06e66b29e0628e207e8b1c3ceeda701ec928d"
dependencies = [
 "cc",
 "cfg-if",
 "getrandom 0.2.15",
 "libc",
 "untrusted",
 "windows-sys 0.52.0",
]

[[package]]
name = "ripemd"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd124222d17ad93a644ed9d011a40f4fb64aa54275c08cc216524a9ea82fb09f"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "risc0-binfmt"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62eb7025356a233c1bc267c458a2ce56fcfc89b136d813c8a77be14ef1eaf2b1"
dependencies = [
 "anyhow",
 "borsh",
 "derive_more 2.0.1",
 "elf",
 "lazy_static",
 "postcard",
 "risc0-zkp",
 "risc0-zkvm-platform",
 "semver 1.0.25",
 "serde",
 "tracing",
]

[[package]]
name = "risc0-build"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c951d23a3255fe9e43526418ad8fc3471cac1c48b338f9cfeb4063d250c1f698"
dependencies = [
 "anyhow",
 "cargo_metadata 0.19.2",
 "derive_builder",
 "dirs",
 "docker-generate",
 "hex",
 "risc0-binfmt",
 "risc0-zkos-v1compat",
 "risc0-zkp",
 "risc0-zkvm-platform",
 "rzup",
 "semver 1.0.25",
 "serde",
 "serde_json",
 "stability",
 "tempfile",
]

[[package]]
name = "risc0-circuit-keccak"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0094af5a57b020388a03bdd3834959c7d62723f1687be81414ade25104d93263"
dependencies = [
 "anyhow",
 "bytemuck",
 "paste",
 "risc0-binfmt",
 "risc0-circuit-recursion",
 "risc0-core",
 "risc0-zkp",
 "tracing",
]

[[package]]
name = "risc0-circuit-recursion"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76ebded45c902c2b6939924a1cddd1d06b5d1d4ad1531e8798ebfee78f9c038d"
dependencies = [
 "anyhow",
 "bytemuck",
 "hex",
 "metal",
 "risc0-core",
 "risc0-zkp",
 "tracing",
]

[[package]]
name = "risc0-circuit-rv32im"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15030849f8356f01f23c74b37dbfa4283100b594eb634109993e9e005ef45f64"
dependencies = [
 "anyhow",
 "bit-vec",
 "bytemuck",
 "derive_more 2.0.1",
 "paste",
 "risc0-binfmt",
 "risc0-core",
 "risc0-zkp",
 "serde",
 "tracing",
]

[[package]]
name = "risc0-core"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "317bbf70a8750b64d4fd7a2bdc9d7d5f30d8bb305cae486962c797ef35c8d08e"
dependencies = [
 "bytemuck",
 "bytemuck_derive",
 "rand_core 0.6.4",
]

[[package]]
name = "risc0-groth16"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cf5d0b673d5fc67a89147c2e9c53134707dcc8137a43d1ef06b4ff68e99b74f"
dependencies = [
 "anyhow",
 "ark-bn254",
 "ark-ec",
 "ark-groth16",
 "ark-serialize 0.5.0",
 "bytemuck",
 "hex",
 "num-bigint",
 "num-traits",
 "risc0-binfmt",
 "risc0-zkp",
 "serde",
 "stability",
]

[[package]]
name = "risc0-zkos-v1compat"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f76c479b69d1987cb54ac72dcc017197296fdcd6daf78fafc10cbbd3a167a7de"
dependencies = [
 "include_bytes_aligned",
 "no_std_strings",
]

[[package]]
name = "risc0-zkp"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a287e9cd6d7b3b38eeb49c62090c46a1935922309fbd997a9143ed8c43c8f3cb"
dependencies = [
 "anyhow",
 "blake2",
 "borsh",
 "bytemuck",
 "cfg-if",
 "digest 0.10.7",
 "hex",
 "hex-literal",
 "metal",
 "paste",
 "rand_core 0.6.4",
 "risc0-core",
 "risc0-zkvm-platform",
 "serde",
 "sha2 0.10.8",
 "stability",
 "tracing",
]

[[package]]
name = "risc0-zkvm"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "910c2023c39ac1e23dd4f7acdff086333f31ca608035f96c74366a79c098de3b"
dependencies = [
 "anyhow",
 "bincode",
 "bonsai-sdk",
 "borsh",
 "bytemuck",
 "bytes",
 "derive_more 2.0.1",
 "getrandom 0.2.15",
 "hex",
 "lazy-regex",
 "prost",
 "risc0-binfmt",
 "risc0-build",
 "risc0-circuit-keccak",
 "risc0-circuit-recursion",
 "risc0-circuit-rv32im",
 "risc0-core",
 "risc0-groth16",
 "risc0-zkos-v1compat",
 "risc0-zkp",
 "risc0-zkvm-platform",
 "rrs-lib",
 "rzup",
 "semver 1.0.25",
 "serde",
 "sha2 0.10.8",
 "stability",
 "tempfile",
 "tracing",
]

[[package]]
name = "risc0-zkvm-platform"
version = "2.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cae9cb2c2f6cab2dfa395ea6e2576713929040c7fb0c5f4150d13e1119d18686"
dependencies = [
 "bytemuck",
 "cfg-if",
 "getrandom 0.2.15",
 "getrandom 0.3.2",
 "libm",
 "stability",
]

[[package]]
name = "rlimit"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7043b63bd0cd1aaa628e476b80e6d4023a3b50eb32789f2728908107bd0c793a"
dependencies = [
 "libc",
]

[[package]]
name = "rlp"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb919243f34364b6bd2fc10ef797edbfa75f33c252e7998527479c6d6b47e1ec"
dependencies = [
 "bytes",
 "rustc-hex",
]

[[package]]
name = "roaring"
version = "0.10.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a652edd001c53df0b3f96a36a8dc93fce6866988efc16808235653c6bcac8bf2"
dependencies = [
 "bytemuck",
 "byteorder",
]

[[package]]
name = "rocksdb"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6bd13e55d6d7b8cd0ea569161127567cd587676c99f4472f779a0279aa60a7a7"
dependencies = [
 "libc",
 "librocksdb-sys",
]

[[package]]
name = "rolling-file"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8395b4f860856b740f20a296ea2cd4d823e81a2658cf05ef61be22916026a906"
dependencies = [
 "chrono",
]

[[package]]
name = "route-recognizer"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afab94fb28594581f62d981211a9a4d53cc8130bbcbbb89a0440d9b8e81a7746"

[[package]]
name = "rrs-lib"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4382d3af3a4ebdae7f64ba6edd9114fff92c89808004c4943b393377a25d001"
dependencies = [
 "downcast-rs",
 "paste",
]

[[package]]
name = "rs_merkle"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb09b49230ba22e8c676e7b75dfe2887dea8121f18b530ae0ba519ce442d2b21"
dependencies = [
 "sha2 0.10.8",
]

[[package]]
name = "rstest"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b423f0e62bdd61734b67cd21ff50871dfaeb9cc74f869dcd6af974fbcb19936"
dependencies = [
 "futures",
 "futures-timer",
 "rstest_macros",
 "rustc_version 0.4.1",
]

[[package]]
name = "rstest_macros"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5e1711e7d14f74b12a58411c542185ef7fb7f2e7f8ee6e2940a883628522b42"
dependencies = [
 "cfg-if",
 "glob",
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "regex",
 "relative-path",
 "rustc_version 0.4.1",
 "syn 2.0.98",
 "unicode-ident",
]

[[package]]
name = "ruint"
version = "1.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "825df406ec217a8116bd7b06897c6cc8f65ffefc15d030ae2c9540acc9ed50b6"
dependencies = [
 "alloy-rlp",
 "ark-ff 0.3.0",
 "ark-ff 0.4.2",
 "bytes",
 "fastrlp 0.3.1",
 "fastrlp 0.4.0",
 "num-bigint",
 "num-integer",
 "num-traits",
 "parity-scale-codec",
 "primitive-types",
 "proptest",
 "rand 0.8.5",
 "rlp",
 "ruint-macro",
 "serde",
 "valuable",
 "zeroize",
]

[[package]]
name = "ruint-macro"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48fd7bd8a6377e15ad9d42a8ec25371b94ddc67abe7c8b9127bec79bebaaae18"

[[package]]
name = "rusqlite"
version = "0.34.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37e34486da88d8e051c7c0e23c3f15fd806ea8546260aa2fec247e97242ec143"
dependencies = [
 "bitflags 2.8.0",
 "fallible-iterator",
 "fallible-streaming-iterator",
 "hashlink 0.10.0",
 "libsqlite3-sys",
 "smallvec",
]

[[package]]
name = "rustc-demangle"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "719b953e2095829ee67db738b3bfa9fa368c94900df327b3f07fe6e794d2fe1f"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hash"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "357703d41365b4b27c590e3ed91eabb1b663f07c4c084095e60cbed4362dff0d"
dependencies = [
 "rand 0.8.5",
]

[[package]]
name = "rustc-hex"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e75f6a532d0fd9f7f13144f392b6ad56a32696bfcd9c78f797f16bbb6f072d6"

[[package]]
name = "rustc_version"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0dfe2087c51c460008730de8b57e6a320782fbfb312e1f4d520e6c6fae155ee"
dependencies = [
 "semver 0.11.0",
]

[[package]]
name = "rustc_version"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfcb3a22ef46e85b45de6ee7e79d063319ebb6594faafcf1c225ea92ab6e9b92"
dependencies = [
 "semver 1.0.25",
]

[[package]]
name = "rustc_version_runtime"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2dd18cd2bae1820af0b6ad5e54f4a51d0f3fcc53b05f845675074efcc7af071d"
dependencies = [
 "rustc_version 0.4.1",
 "semver 1.0.25",
]

[[package]]
name = "rustix"
version = "0.38.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdb5bc1ae2baa591800df16c9ca78619bf65c0488b41b96ccec5d11220d8c154"
dependencies = [
 "bitflags 2.8.0",
 "errno",
 "libc",
 "linux-raw-sys 0.4.15",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustix"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c71e83d6afe7ff64890ec6b71d6a69bb8a610ab78ce364b3352876bb4c801266"
dependencies = [
 "bitflags 2.8.0",
 "errno",
 "libc",
 "linux-raw-sys 0.9.4",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustls"
version = "0.23.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "822ee9188ac4ec04a2f0531e55d035fb2de73f18b41a63c70c2712503b6fb13c"
dependencies = [
 "aws-lc-rs",
 "log",
 "once_cell",
 "ring",
 "rustls-pki-types",
 "rustls-webpki",
 "subtle",
 "zeroize",
]

[[package]]
name = "rustls-native-certs"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcff2dd52b58a8d98a70243663a0d234c4e2b79235637849d15913394a247d3"
dependencies = [
 "openssl-probe",
 "rustls-pki-types",
 "schannel",
 "security-framework",
]

[[package]]
name = "rustls-pemfile"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dce314e5fee3f39953d46bb63bb8a46d40c2f8fb7cc5a3b6cab2bde9721d6e50"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "rustls-pki-types"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "917ce264624a4b4db1c364dcc35bfca9ded014d0a958cd47ad3e960e988ea51c"

[[package]]
name = "rustls-platform-verifier"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a5467026f437b4cb2a533865eaa73eb840019a0916f4b9ec563c6e617e086c9"
dependencies = [
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "jni",
 "log",
 "once_cell",
 "rustls",
 "rustls-native-certs",
 "rustls-platform-verifier-android",
 "rustls-webpki",
 "security-framework",
 "security-framework-sys",
 "webpki-root-certs",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustls-platform-verifier-android"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f87165f0995f63a9fbeea62b64d10b4d9d8e78ec6d7d51fb2125fda7bb36788f"

[[package]]
name = "rustls-webpki"
version = "0.103.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0aa4eeac2588ffff23e9d7a7e9b3f971c5fb5b7ebc9452745e0c232c64f83b2f"
dependencies = [
 "aws-lc-rs",
 "ring",
 "rustls-pki-types",
 "untrusted",
]

[[package]]
name = "rustversion"
version = "1.0.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7c45b9784283f1b2e7fb61b42047c2fd678ef0960d4f6f1eba131594cc369d4"

[[package]]
name = "rusty-fork"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb3dcc6e454c328bb824492db107ab7c0ae8fcffe4ad210136ef014458c1bc4f"
dependencies = [
 "fnv",
 "quick-error",
 "tempfile",
 "wait-timeout",
]

[[package]]
name = "ryu"
version = "1.0.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ea1a2d0a644769cc99faa24c3ad26b379b786fe7c36fd3c546254801650e6dd"

[[package]]
name = "ryu-js"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad97d4ce1560a5e27cec89519dc8300d1aa6035b099821261c651486a19e44d5"

[[package]]
name = "rzup"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "400558bf12d4292a7804093b60a437ba8b0219ea7d53716b2c010a0d31e5f4a8"
dependencies = [
 "clap",
 "colored",
 "flate2",
 "fs2",
 "indicatif",
 "is-terminal",
 "reqwest",
 "semver 1.0.25",
 "serde",
 "strum 0.26.3",
 "tar",
 "tempfile",
 "thiserror 2.0.12",
 "toml",
 "xz",
 "yaml-rust2",
]

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "schannel"
version = "0.1.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f29ebaa345f945cec9fbbc532eb307f0fdad8161f281b6369539c8d84876b3d"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "schemars"
version = "0.8.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fbf2ae1b8bc8e02df939598064d22402220cd5bbcca1c76f7d6a310974d5615"
dependencies = [
 "dyn-clone",
 "schemars_derive",
 "serde",
 "serde_json",
]

[[package]]
name = "schemars_derive"
version = "0.8.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32e265784ad618884abaea0600a9adf15393368d840e0222d101a072f3f7534d"
dependencies = [
 "proc-macro2",
 "quote",
 "serde_derive_internals",
 "syn 2.0.98",
]

[[package]]
name = "schnellru"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "356285bbf17bea63d9e52e96bd18f039672ac92b55b8cb997d6162a2a37d1649"
dependencies = [
 "ahash",
 "cfg-if",
 "hashbrown 0.13.2",
]

[[package]]
name = "scoped-tls"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1cf6437eb19a8f4a6cc0f7dca544973b0b78843adbfeb3683d1a94a0024a294"

[[package]]
name = "scopeguard"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94143f37725109f92c262ed2cf5e59bce7498c01bcc1502d7b9afe439a4e9f49"

[[package]]
name = "sec1"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3e97a565f76233a6003f9f5c54be1d9c5bdfa3eccfb189469f11ec4901c47dc"
dependencies = [
 "base16ct",
 "der",
 "generic-array",
 "pkcs8",
 "serdect",
 "subtle",
 "zeroize",
]

[[package]]
name = "secp256k1"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e0cc0f1cf93f4969faf3ea1c7d8a9faed25918d96affa959720823dfe86d4f3"
dependencies = [
 "bitcoin_hashes",
 "rand 0.8.5",
 "secp256k1-sys",
 "serde",
]

[[package]]
name = "secp256k1"
version = "0.30.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b50c5943d326858130af85e049f2661ba3c78b26589b8ab98e65e80ae44a1252"
dependencies = [
 "bitcoin_hashes",
 "rand 0.8.5",
 "secp256k1-sys",
 "serde",
]

[[package]]
name = "secp256k1-sys"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4387882333d3aa8cb20530a17c69a3752e97837832f34f6dccc760e715001d9"
dependencies = [
 "cc",
]

[[package]]
name = "security-framework"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271720403f46ca04f7ba6f55d438f8bd878d6b8ca0a1046e8228c4145bcbb316"
dependencies = [
 "bitflags 2.8.0",
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49db231d56a190491cb4aeda9527f1ad45345af50b0851622a7adb8c03b01c32"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "semver"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f301af10236f6df4160f7c3f04eec6dbc70ace82d23326abad5edee88801c6b6"
dependencies = [
 "semver-parser",
]

[[package]]
name = "semver"
version = "1.0.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f79dfe2d285b0488816f30e700a7438c5a73d816b5b7d3ac72fbc48b0d185e03"
dependencies = [
 "serde",
]

[[package]]
name = "semver-parser"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9900206b54a3527fdc7b8a938bffd94a568bac4f4aa8113b209df75a09c0dec2"
dependencies = [
 "pest",
]

[[package]]
name = "send_wrapper"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f638d531eccd6e23b980caf34876660d38e265409d8e99b397ab71eb3612fad0"

[[package]]
name = "serde"
version = "1.0.218"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8dfc9d19bdbf6d17e22319da49161d5d0108e4188e8b680aef6299eed22df60"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde_derive"
version = "1.0.218"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f09503e191f4e797cb8aac08e9a4a4695c5edf6a2e70e376d961ddd5c969f82b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "serde_derive_internals"
version = "0.29.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18d26a20a969b9e3fdf2fc2d9f21eda6c40e2de84c9408bb5d3b05d499aae711"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "serde_json"
version = "1.0.139"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44f86c3acccc9c65b153fe1b85a3be07fe5515274ec9f0653b4a0875731c72a6"
dependencies = [
 "indexmap 2.7.1",
 "itoa",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "serde_repr"
version = "0.1.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c64451ba24fc7a6a2d60fc75dd9c83c90903b19028d4eff35e88fc1e86564e9"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "serde_spanned"
version = "0.6.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87607cb1398ed59d48732e575a4c28a7a8ebf2454b964fe3f224f2afc07909e1"
dependencies = [
 "serde",
]

[[package]]
name = "serde_urlencoded"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3491c14715ca2294c4d6a88f15e84739788c1d030eed8c110436aafdaa2f3fd"
dependencies = [
 "form_urlencoded",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "serde_with"
version = "3.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6b6f7f2fcb69f747921f79f3926bd1e203fce4fef62c268dd3abfb6d86029aa"
dependencies = [
 "base64 0.22.1",
 "chrono",
 "hex",
 "indexmap 1.9.3",
 "indexmap 2.7.1",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_with_macros",
 "time",
]

[[package]]
name = "serde_with_macros"
version = "3.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d00caa5193a3c8362ac2b73be6b9e768aa5a4b2f721d8f4b339600c3cb51f8e"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "serdect"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a84f14a19e9a014bb9f4512488d9829a68e04ecabffb0f9904cd1ace94598177"
dependencies = [
 "base16ct",
 "serde",
]

[[package]]
name = "sha1"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bf829a2d51ab4a5ddf1352d8470c140cadc8301b2ae1789db023f01cedd6ba"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha2"
version = "0.9.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d58a1e1bf39749807d89cf2d98ac2dfa0ff1cb3faa38fbb64dd88ac8013d800"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if",
 "cpufeatures",
 "digest 0.9.0",
 "opaque-debug",
]

[[package]]
name = "sha2"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "793db75ad2bcafc3ffa7c68b215fee268f537982cd901d132f89c6343f3a3dc8"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest 0.10.7",
 "sha2-asm",
]

[[package]]
name = "sha2-asm"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b845214d6175804686b2bd482bcffe96651bb2d1200742b712003504a2dac1ab"
dependencies = [
 "cc",
]

[[package]]
name = "sha3"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75872d278a8f37ef87fa0ddbda7802605cb18344497949862c0d4dcb291eba60"
dependencies = [
 "digest 0.10.7",
 "keccak",
]

[[package]]
name = "sha3-asm"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c28efc5e327c837aa837c59eae585fc250715ef939ac32881bcc11677cd02d46"
dependencies = [
 "cc",
 "cfg-if",
]

[[package]]
name = "sharded-slab"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40ca3c46823713e0d4209592e8d6e826aa57e928f09752619fc696c499637f6"
dependencies = [
 "lazy_static",
]

[[package]]
name = "shellexpand"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da03fa3b94cc19e3ebfc88c4229c49d8f08cdbd1228870a45f0ffdf84988e14b"
dependencies = [
 "dirs",
]

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "short-header-proof-provider"
version = "0.7.2"
dependencies = [
 "borsh",
 "futures",
 "parking_lot",
 "sov-db",
 "sov-mock-da",
 "sov-modules-api",
 "sov-rollup-interface",
 "tempfile",
 "thiserror 2.0.12",
]

[[package]]
name = "signal-hook-registry"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9e9e0b4211b72e7b8b6e85c807d36c212bdb33ea8587f7569562a84df5465b1"
dependencies = [
 "libc",
]

[[package]]
name = "signature"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77549399552de45a898a580c1b41d445bf730df867cc44e6c0233bbc4b8329de"
dependencies = [
 "digest 0.10.7",
 "rand_core 0.6.4",
]

[[package]]
name = "simple_asn1"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adc4e5204eb1910f40f9cfa375f6f05b68c3abac4b6fd879c8ff5e7ae8a0a085"
dependencies = [
 "num-bigint",
 "num-traits",
 "thiserror 1.0.69",
 "time",
]

[[package]]
name = "siphasher"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56199f7ddabf13fe5074ce809e7d3f42b42ae711800501b5b16ea82ad029c39d"

[[package]]
name = "sketches-ddsketch"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85636c14b73d81f541e525f585c0a2109e6744e1565b5c1668e31c70c10ed65c"

[[package]]
name = "slab"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f92a496fb766b417c996b9c5e57daf2f7ad3b0bebe1ccfca4856390e3d3bb67"
dependencies = [
 "autocfg",
]

[[package]]
name = "smallvec"
version = "1.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcf8323ef1faaee30a44a340193b1ac6814fd9b7b4e88e9d4519a3e4abe1cfd"
dependencies = [
 "serde",
]

[[package]]
name = "snap"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b6b67fb9a61334225b5b790716f609cd58395f895b3fe8b328786812a40bc3b"

[[package]]
name = "socket2"
version = "0.5.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c970269d99b64e60ec3bd6ad27270092a5394c4e309314b18ae3fe575695fbe8"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "soketto"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e859df029d160cb88608f5d7df7fb4753fd20fdfb4de5644f3d8b8440841721"
dependencies = [
 "base64 0.22.1",
 "bytes",
 "futures",
 "http",
 "httparse",
 "log",
 "rand 0.8.5",
 "sha1",
]

[[package]]
name = "sov-accounts"
version = "0.7.2"
dependencies = [
 "borsh",
 "hex",
 "jsonrpsee",
 "schemars",
 "serde",
 "serde_json",
 "sov-db",
 "sov-keys",
 "sov-modules-api",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "tempfile",
 "thiserror 2.0.12",
]

[[package]]
name = "sov-db"
version = "0.7.2"
dependencies = [
 "alloy-primitives",
 "anyhow",
 "bincode",
 "borsh",
 "byteorder",
 "criterion",
 "jmt",
 "rand 0.8.5",
 "rlimit",
 "rocksdb",
 "serde",
 "sha2 0.10.8",
 "sov-rollup-interface",
 "sov-schema-db",
 "tempfile",
 "tracing",
 "uuid",
]

[[package]]
name = "sov-keys"
version = "0.7.2"
dependencies = [
 "borsh",
 "derive_more 1.0.0",
 "digest 0.10.7",
 "hex",
 "k256",
 "rand 0.8.5",
 "schemars",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "thiserror 2.0.12",
]

[[package]]
name = "sov-ledger-rpc"
version = "0.7.2"
dependencies = [
 "alloy-primitives",
 "anyhow",
 "faster-hex",
 "jsonrpsee",
 "serde",
 "sov-db",
 "sov-modules-api",
 "sov-rollup-interface",
 "tempfile",
 "tokio",
]

[[package]]
name = "sov-mock-da"
version = "0.7.2"
dependencies = [
 "anyhow",
 "async-trait",
 "borsh",
 "bytes",
 "hex",
 "rusqlite",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "sov-rollup-interface",
 "tempfile",
 "tokio",
 "tracing",
]

[[package]]
name = "sov-mock-zkvm"
version = "0.7.2"
dependencies = [
 "anyhow",
 "borsh",
 "serde",
 "sov-rollup-interface",
 "tokio",
 "uuid",
]

[[package]]
name = "sov-modules-api"
version = "0.7.2"
dependencies = [
 "anyhow",
 "bech32 0.9.1",
 "bincode",
 "borsh",
 "clap",
 "derive_more 1.0.0",
 "digest 0.10.7",
 "hex",
 "jmt",
 "jsonrpsee",
 "rand 0.8.5",
 "schemars",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "sov-db",
 "sov-keys",
 "sov-modules-api",
 "sov-modules-core",
 "sov-modules-macros",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "tempfile",
 "thiserror 2.0.12",
]

[[package]]
name = "sov-modules-core"
version = "0.7.2"
dependencies = [
 "anyhow",
 "bech32 0.9.1",
 "borsh",
 "derive_more 1.0.0",
 "digest 0.10.7",
 "hex",
 "jmt",
 "proptest",
 "schemars",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "sov-keys",
 "sov-modules-core",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "sov-state",
 "tempfile",
 "thiserror 2.0.12",
 "tinyvec",
]

[[package]]
name = "sov-modules-macros"
version = "0.7.2"
dependencies = [
 "borsh",
 "jsonrpsee",
 "proc-macro2",
 "quote",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "sov-modules-api",
 "sov-modules-core",
 "sov-state",
 "syn 1.0.109",
 "trybuild",
]

[[package]]
name = "sov-modules-rollup-blueprint"
version = "0.7.2"
dependencies = [
 "anyhow",
 "async-trait",
 "citrea-common",
 "citrea-stf",
 "derive_more 1.0.0",
 "jsonrpsee",
 "prover-services",
 "reth-tasks",
 "sov-db",
 "sov-ledger-rpc",
 "sov-modules-api",
 "sov-modules-stf-blueprint",
 "sov-prover-storage-manager",
 "sov-rollup-interface",
 "tokio",
 "tracing",
]

[[package]]
name = "sov-modules-stf-blueprint"
version = "0.7.2"
dependencies = [
 "anyhow",
 "borsh",
 "citrea-primitives",
 "hex",
 "jmt",
 "jsonrpsee",
 "rs_merkle",
 "serde",
 "sov-db",
 "sov-keys",
 "sov-modules-api",
 "sov-rollup-interface",
 "sov-state",
 "tracing",
]

[[package]]
name = "sov-prover-storage-manager"
version = "0.7.2"
dependencies = [
 "anyhow",
 "rand 0.8.5",
 "sov-db",
 "sov-rollup-interface",
 "sov-schema-db",
 "sov-state",
 "tempfile",
 "tracing",
]

[[package]]
name = "sov-rollup-interface"
version = "0.7.2"
dependencies = [
 "alloy-primitives",
 "anyhow",
 "async-trait",
 "borsh",
 "bytes",
 "digest 0.10.7",
 "faster-hex",
 "hex",
 "jmt",
 "risc0-zkp",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "sov-keys",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "uuid",
]

[[package]]
name = "sov-schema-db"
version = "0.7.2"
dependencies = [
 "anyhow",
 "byteorder",
 "metrics 0.23.0",
 "metrics-derive",
 "rocksdb",
 "sov-schema-db",
 "tempfile",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
]

[[package]]
name = "sov-state"
version = "0.7.2"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "anyhow",
 "bcs",
 "borsh",
 "bytes",
 "jmt",
 "proptest",
 "serde",
 "sha2 0.10.8",
 "sov-db",
 "sov-keys",
 "sov-modules-core",
 "sov-rollup-interface",
 "sov-schema-db",
 "tempfile",
]

[[package]]
name = "sp1-build"
version = "3.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "474a76fa492fed2541d665db433771af10b8f5fe31b1cce86bdd51dd0f9418ba"
dependencies = [
 "anyhow",
 "cargo_metadata 0.18.1",
 "chrono",
 "clap",
 "dirs",
]

[[package]]
name = "sp1-helper"
version = "3.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "130d503edbf38cf0bbc923c58124c552c2a2eea1bba3b924ab12a0beea148041"
dependencies = [
 "sp1-build",
]

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"

[[package]]
name = "spki"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d91ed6c858b01f942cd56b37a94b3e0a1798290327d1236e4d9cf4eaca44d29d"
dependencies = [
 "base64ct",
 "der",
]

[[package]]
name = "sptr"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b9b39299b249ad65f3b7e96443bad61c02ca5cd3589f46cb6d610a0fd6c0d6a"

[[package]]
name = "stability"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d904e7009df136af5297832a3ace3370cd14ff1546a232f4f185036c2736fcac"
dependencies = [
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "strsim"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7da8b5736845d9f2fcb837ea5d9e2628564b3b043a70948a3f0b778838c5fb4f"

[[package]]
name = "strum"
version = "0.26.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fec0f0aef304996cf250b31b5a10dee7980c85da9d759361292b8bca5a18f06"
dependencies = [
 "strum_macros 0.26.4",
]

[[package]]
name = "strum"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f64def088c51c9510a8579e3c5d67c65349dcf755e5479ad3d010aa6454e2c32"
dependencies = [
 "strum_macros 0.27.1",
]

[[package]]
name = "strum_macros"
version = "0.26.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c6bee85a5a24955dc440386795aa378cd9cf82acd5f764469152d2270e581be"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.98",
]

[[package]]
name = "strum_macros"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c77a8c5abcaf0f9ce05d62342b7d298c346515365c36b673df4ebe3ced01fde8"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.98",
]

[[package]]
name = "substrate-bn"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b5bbfa79abbae15dd642ea8176a21a635ff3c00059961d1ea27ad04e5b441c"
dependencies = [
 "byteorder",
 "crunchy",
 "lazy_static",
 "rand 0.8.5",
 "rustc-hex",
]

[[package]]
name = "subtle"
version = "2.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13c2bddecc57b384dee18652358fb23172facb8a2c51ccc10d74c157bdea3292"

[[package]]
name = "syn"
version = "1.0.109"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36147f1a48ae0ec2b5b3bc5b537d267457555a10dc06f3dbc8cb11ba3006d3b1"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn-solidity"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4560533fbd6914b94a8fb5cc803ed6801c3455668db3b810702c57612bac9412"
dependencies = [
 "paste",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sync_wrapper"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bf256ce5efdfa370213c1dabab5935a12e49f2c58d15e9eac2870d3b4f27263"
dependencies = [
 "futures-core",
]

[[package]]
name = "synstructure"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8af7666ab7b6390ab78131fb5b0fce11d6b7a6951602017c35fa82800708971"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "sysinfo"
version = "0.33.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fc858248ea01b66f19d8e8a6d55f41deaf91e9d495246fd01368d99935c6c01"
dependencies = [
 "core-foundation-sys",
 "libc",
 "memchr",
 "ntapi",
 "windows 0.57.0",
]

[[package]]
name = "tagptr"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b2093cf4c8eb1e67749a6762251bc9cd836b6fc171623bd0a9d324d37af2417"

[[package]]
name = "tap"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55937e1799185b12863d447f42597ed69d9928686b8d88a1df17376a097d8369"

[[package]]
name = "tar"
version = "0.4.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d863878d212c87a19c1a610eb53bb01fe12951c0501cf5a0d65f724914a667a"
dependencies = [
 "filetime",
 "libc",
 "xattr",
]

[[package]]
name = "target-triple"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ac9aa371f599d22256307c24a9d748c041e548cbf599f35d890f9d365361790"

[[package]]
name = "tempfile"
version = "3.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a64e3985349f2441a1a9ef0b853f869006c3855f2cda6862a94d26ebb9d6a1"
dependencies = [
 "fastrand",
 "getrandom 0.3.2",
 "once_cell",
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "termcolor"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06794f8f6c5c898b3275aebefa6b8a1cb24cd2c6c79397ab15774837a0bc5755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "thin-vec"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a38c90d48152c236a3ab59271da4f4ae63d678c5d7ad6b7714d7cb9760be5e4b"

[[package]]
name = "thiserror"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6aaf5339b578ea85b50e080feb250a3e8ae8cfcdff9a461c9ec2904bc923f52"
dependencies = [
 "thiserror-impl 1.0.69",
]

[[package]]
name = "thiserror"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567b8a2dae586314f7be2a752ec7474332959c6460e02bde30d702a66d488708"
dependencies = [
 "thiserror-impl 2.0.12",
]

[[package]]
name = "thiserror-impl"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fee6c4efc90059e10f81e6d42c60a18f76588c3d74cb83a0b242a2b6c7504c1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "thiserror-impl"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f7cf42b4507d8ea322120659672cf1b9dbb93f8f2d4ecfd6e51350ff5b17a1d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "thread_local"
version = "1.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b9ef9bad013ada3808854ceac7b46812a6465ba368859a37e2100283d2d719c"
dependencies = [
 "cfg-if",
 "once_cell",
]

[[package]]
name = "threadpool"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d050e60b33d41c19108b32cea32164033a9013fe3b46cbd4457559bfbf77afaa"
dependencies = [
 "num_cpus",
]

[[package]]
name = "time"
version = "0.3.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35e7868883861bd0e56d9ac6efcaaca0d6d5d82a2a7ec8209ff492c07cf37b21"
dependencies = [
 "deranged",
 "itoa",
 "js-sys",
 "libc",
 "num-conv",
 "num_threads",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef927ca75afb808a4d64dd374f00a2adf8d0fcff8e7b184af886c3c87ec4a3f3"

[[package]]
name = "time-macros"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2834e6017e3e5e4b9834939793b282bc03b37a3336245fa820e35e233e2a85de"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "tiny-keccak"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c9d3793400a45f954c52e73d068316d76b6f4e36977e3fcebb13a2721e80237"
dependencies = [
 "crunchy",
]

[[package]]
name = "tinystr"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9117f5d4db391c1cf6927e7bea3db74b9a1c1add8f7eda9ffd5364f40f57b82f"
dependencies = [
 "displaydoc",
 "zerovec",
]

[[package]]
name = "tinytemplate"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be4d6b5f19ff7664e8c98d03e2139cb510db9b0a60b55f8e8709b689d939b6bc"
dependencies = [
 "serde",
 "serde_json",
]

[[package]]
name = "tinyvec"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "022db8904dfa342efe721985167e9fcd16c29b226db4397ed752a761cfce81e8"
dependencies = [
 "serde",
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "tokio"
version = "1.44.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6b88822cbe49de4185e3a4cbf8321dd487cf5fe0c5c65695fef6346371e9c48"
dependencies = [
 "backtrace",
 "bytes",
 "libc",
 "mio",
 "parking_lot",
 "pin-project-lite",
 "signal-hook-registry",
 "socket2",
 "tokio-macros",
 "windows-sys 0.52.0",
]

[[package]]
name = "tokio-macros"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e06d43f1345a3bcd39f6a56dbb7dcab2ba47e68e8ac134855e7e2bdbaf8cab8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "tokio-rustls"
version = "0.26.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f6d0975eaace0cf0fcadee4e4aaa5da15b5c079146f2cffb67c113be122bf37"
dependencies = [
 "rustls",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eca58d7bba4a75707817a2c44174253f9236b2d5fbd055602e9d5c07c139a047"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
 "tokio-util",
]

[[package]]
name = "tokio-util"
version = "0.7.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7fcaa8d55a2bdd6b83ace262b016eca0d79ee02818c5c1bcdf0305114081078"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite",
 "slab",
 "tokio",
]

[[package]]
name = "toml"
version = "0.8.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd87a5cdd6ffab733b2f74bc4fd7ee5fff6634124999ac278c35fc78c6120148"
dependencies = [
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_edit",
]

[[package]]
name = "toml_datetime"
version = "0.6.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dd7358ecb8fc2f8d014bf86f6f638ce72ba252a2c3a2572f2a795f1d23efb41"
dependencies = [
 "serde",
]

[[package]]
name = "toml_edit"
version = "0.22.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17b4795ff5edd201c7cd6dca065ae59972ce77d1b80fa0a84d94950ece7d1474"
dependencies = [
 "indexmap 2.7.1",
 "serde",
 "serde_spanned",
 "toml_datetime",
 "winnow",
]

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "futures-core",
 "futures-util",
 "hdrhistogram",
 "indexmap 1.9.3",
 "pin-project",
 "pin-project-lite",
 "rand 0.8.5",
 "slab",
 "tokio",
 "tokio-util",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d039ad9159c98b70ecfd540b2573b97f7f52c3e8d9f8ad57a24b916a536975f9"
dependencies = [
 "futures-core",
 "futures-util",
 "pin-project-lite",
 "sync_wrapper",
 "tokio",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-http"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e9cd434a998747dd2c4276bc96ee2e0c7a2eadf3cae88e52be55a05fa9053f5"
dependencies = [
 "async-compression",
 "base64 0.21.7",
 "bitflags 2.8.0",
 "bytes",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "http-body-util",
 "http-range-header",
 "httpdate",
 "iri-string",
 "mime",
 "mime_guess",
 "percent-encoding",
 "pin-project-lite",
 "tokio",
 "tokio-util",
 "tower 0.4.13",
 "tower-layer",
 "tower-service",
 "tracing",
 "uuid",
]

[[package]]
name = "tower-layer"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "121c2a6cda46980bb0fcd1647ffaf6cd3fc79a013de288782836f6df9c48780e"

[[package]]
name = "tower-service"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8df9b6e13f2d32c91b9bd719c00d1958837bc7dec474d94952798cc8e69eeec3"

[[package]]
name = "tracing"
version = "0.1.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "784e0ac535deb450455cbfa28a6f0df145ea1bb7ae51b821cf5e7927fdcfbdd0"
dependencies = [
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-appender"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3566e8ce28cc0a3fe42519fc80e6b4c943cc4c8cef275620eb8dac2d3d4e06cf"
dependencies = [
 "crossbeam-channel",
 "thiserror 1.0.69",
 "time",
 "tracing-subscriber 0.3.19",
]

[[package]]
name = "tracing-attributes"
version = "0.1.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "395ae124c09f9e6918a2310af6038fba074bcf474ac352496d5910dd59a2226d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "tracing-core"
version = "0.1.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e672c95779cf947c5311f83787af4fa8fffd12fb27e4993211a84bdfd9610f9c"
dependencies = [
 "once_cell",
 "valuable",
]

[[package]]
name = "tracing-futures"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97d095ae15e245a057c8e8451bab9b3ee1e1f68e9ba2b4fbc18d0ac5237835f2"
dependencies = [
 "futures",
 "futures-task",
 "pin-project",
 "tracing",
]

[[package]]
name = "tracing-journald"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc0b4143302cf1022dac868d521e36e8b27691f72c84b3311750d5188ebba657"
dependencies = [
 "libc",
 "tracing-core",
 "tracing-subscriber 0.3.19",
]

[[package]]
name = "tracing-log"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee855f1f400bd0e5c02d150ae5de3840039a3f54b025156404e34c23c03f47c3"
dependencies = [
 "log",
 "once_cell",
 "tracing-core",
]

[[package]]
name = "tracing-logfmt"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b1f47d22deb79c3f59fcf2a1f00f60cbdc05462bf17d1cd356c1fefa3f444bd"
dependencies = [
 "time",
 "tracing",
 "tracing-core",
 "tracing-subscriber 0.3.19",
]

[[package]]
name = "tracing-serde"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "704b1aeb7be0d0a84fc9828cae51dab5970fee5088f83d1dd7ee6f6246fc6ff1"
dependencies = [
 "serde",
 "tracing-core",
]

[[package]]
name = "tracing-subscriber"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e0d2eaa99c3c2e41547cfa109e910a68ea03823cccad4a0525dcbc9b01e8c71"
dependencies = [
 "tracing-core",
]

[[package]]
name = "tracing-subscriber"
version = "0.3.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8189decb5ac0fa7bc8b96b7cb9b2701d60d48805aca84a238004d665fcc4008"
dependencies = [
 "matchers",
 "nu-ansi-term",
 "once_cell",
 "regex",
 "serde",
 "serde_json",
 "sharded-slab",
 "smallvec",
 "thread_local",
 "tracing",
 "tracing-core",
 "tracing-log",
 "tracing-serde",
]

[[package]]
name = "tree_hash"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c58eb0f518840670270d90d97ffee702d8662d9c5494870c9e1e9e0fa00f668"
dependencies = [
 "alloy-primitives",
 "ethereum_hashing",
 "ethereum_ssz",
 "smallvec",
 "typenum",
]

[[package]]
name = "tree_hash_derive"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "699e7fb6b3fdfe0c809916f251cf5132d64966858601695c3736630a87e7166a"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "try-lock"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e421abadd41a4225275504ea4d6566923418b7f05506fbc9c0fe86ba7396114b"

[[package]]
name = "trybuild"
version = "1.0.103"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b812699e0c4f813b872b373a4471717d9eb550da14b311058a4d9cf4173cbca6"
dependencies = [
 "glob",
 "serde",
 "serde_derive",
 "serde_json",
 "target-triple",
 "termcolor",
 "toml",
]

[[package]]
name = "typenum"
version = "1.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1dccffe3ce07af9386bfd29e80c0ab1a8205a2fc34e4bcd40364df902cfa8f3f"

[[package]]
name = "typewit"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb77c29baba9e4d3a6182d51fa75e3215c7fd1dab8f4ea9d107c716878e55fc0"

[[package]]
name = "ucd-trie"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2896d95c02a80c6d6a5d6e953d479f5ddf2dfdb6a244441010e373ac0fb88971"

[[package]]
name = "uint"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76f64bba2c53b04fcab63c01a7d7427eadc821e3bc48c34dc9ba29c501164b52"
dependencies = [
 "byteorder",
 "crunchy",
 "hex",
 "static_assertions",
]

[[package]]
name = "uint"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "909988d098b2f738727b161a106cfc7cab00c539c2687a8836f8e565976fb53e"
dependencies = [
 "byteorder",
 "crunchy",
 "hex",
 "static_assertions",
]

[[package]]
name = "unarray"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaea85b334db583fe3274d12b4cd1880032beab409c0d774be044d4480ab9a94"

[[package]]
name = "unicase"
version = "2.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b844d17643ee918803943289730bec8aac480150456169e647ed0b576ba539"

[[package]]
name = "unicode-ident"
version = "1.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00e2473a93778eb0bad35909dff6a10d28e63f792f16ed15e404fca9d5eeedbe"

[[package]]
name = "unicode-segmentation"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6ccf251212114b54433ec949fd6a7841275f9ada20dddd2f29e9ceea4501493"

[[package]]
name = "unicode-width"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fc81956842c57dac11422a97c3b8195a1ff727f06e85c84ed2e8aa277c9a0fd"

[[package]]
name = "unicode-xid"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc1c04c71510c7f702b52b7c350734c9ff1295c464a03335b00bb84fc54f853"

[[package]]
name = "universal-hash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc1de2c688dc15305988b563c3854064043356019f97a4b46276fe734c4f07ea"
dependencies = [
 "crypto-common",
 "subtle",
]

[[package]]
name = "unsigned-varint"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb066959b24b5196ae73cb057f45598450d2c5f71460e98c49b738086eff9c06"

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "url"
version = "2.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32f8b686cadd1473f4bd0117a5d28d36b1ade384ea9b5069a1c40aefed7fda60"
dependencies = [
 "form_urlencoded",
 "idna",
 "percent-encoding",
 "serde",
]

[[package]]
name = "utf16_iter"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8232dd3cdaed5356e0f716d285e4b40b932ac434100fe9b7e0e8e935b9e6246"

[[package]]
name = "utf8_iter"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c140620e7ffbb22c2dee59cafe6084a59b5ffc27a8859a5f0d494b5d52b6be"

[[package]]
name = "utf8parse"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06abde3611657adf66d383f00b093d7faecc7fa57071cce2578660c9f1010821"

[[package]]
name = "uuid"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b3758f5e68192bb96cc8f9b7e2c2cfdabb435499a28499a42f8f984092adad4b"
dependencies = [
 "borsh",
 "borsh-derive",
 "getrandom 0.2.15",
 "serde",
]

[[package]]
name = "valuable"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba73ea9cf16a25df0c8caa16c51acb937d5712a8429db78a3ee29d5dcacd3a65"

[[package]]
name = "vcpkg"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426"

[[package]]
name = "vergen"
version = "9.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0d2f179f8075b805a43a2a21728a46f0cc2921b3c58695b28fa8817e103cd9a"
dependencies = [
 "anyhow",
 "cargo_metadata 0.19.2",
 "derive_builder",
 "regex",
 "rustversion",
 "time",
 "vergen-lib",
]

[[package]]
name = "vergen-git2"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d86bae87104cb2790cdee615c2bb54729804d307191732ab27b1c5357ea6ddc5"
dependencies = [
 "anyhow",
 "derive_builder",
 "git2",
 "rustversion",
 "time",
 "vergen",
 "vergen-lib",
]

[[package]]
name = "vergen-lib"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b07e6010c0f3e59fcb164e0163834597da68d1f864e2b8ca49f74de01e9c166"
dependencies = [
 "anyhow",
 "derive_builder",
 "rustversion",
]

[[package]]
name = "version_check"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b928f33d975fc6ad9f86c8f283853ad26bdd5b10b7f1542aa2fa15e2289105a"

[[package]]
name = "wait-timeout"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ac3b126d3914f9849036f826e054cbabdc8519970b8998ddaf3b5bd3c65f11"
dependencies = [
 "libc",
]

[[package]]
name = "walkdir"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29790946404f91d9c5d06f9874efddea1dc06c5efe94541a7d6863108e3a5e4b"
dependencies = [
 "same-file",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa7760aed19e106de2c7c0b581b509f2f25d3dacaf737cb82ac61bc6d760b0e"
dependencies = [
 "try-lock",
]

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasi"
version = "0.14.2+wasi-0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9683f9a5a998d873c0d21fcbe3c083009670149a8fab228644b8bd36b2c48cb3"
dependencies = [
 "wit-bindgen-rt",
]

[[package]]
name = "wasm-bindgen"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1edc8929d7499fc4e8f0be2262a241556cfc54a0bea223790e71446f2aab1ef5"
dependencies = [
 "cfg-if",
 "once_cell",
 "rustversion",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f0a0651a5c2bc21487bde11ee802ccaf4c51935d0d3d42a6101f98161700bc6"
dependencies = [
 "bumpalo",
 "log",
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "555d470ec0bc3bb57890405e5d4322cc9ea83cebb085523ced7be4144dac1e61"
dependencies = [
 "cfg-if",
 "js-sys",
 "once_cell",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fe63fc6d09ed3792bd0897b314f53de8e16568c2b3f7982f468c0bf9bd0b407"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ae87ea40c9f689fc23f209965b6fb8a99ad69aeeb0231408be24920604395de"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a05d73b933a847d6cccdda8f838a22ff101ad9bf93e33684f39c1f5f0eece3d"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "wasm-streams"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15053d8d85c7eccdbefef60f06769760a563c7f0a9d6902a13d35c7800b0ad65"
dependencies = [
 "futures-util",
 "js-sys",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
]

[[package]]
name = "wasmtimer"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0048ad49a55b9deb3953841fa1fc5858f0efbcb7a18868c899a360269fac1b23"
dependencies = [
 "futures",
 "js-sys",
 "parking_lot",
 "pin-utils",
 "slab",
 "wasm-bindgen",
]

[[package]]
name = "web-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33b6dd2ef9186f1f2072e409e99cd22a975331a6b3591b12c764e0e55c60d5d2"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "web-time"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a6580f308b1fad9207618087a65c04e7a10bc77e02c8e84e9b00dd4b12fa0bb"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "webpki-root-certs"
version = "0.26.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09aed61f5e8d2c18344b3faa33a4c837855fe56642757754775548fee21386c4"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "webpki-roots"
version = "0.26.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2210b291f7ea53617fbafcc4939f10914214ec15aace5ba62293a668f322c5c9"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "which"
version = "4.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87ba24419a2078cd2b0f2ede2691b6c66d8e47836da3b6db8265ebad47afbfc7"
dependencies = [
 "either",
 "home",
 "once_cell",
 "rustix 0.38.44",
]

[[package]]
name = "widestring"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd7cf3379ca1aac9eea11fba24fd7e315d621f8dfe35c8d7d2be8b793726e07d"

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf221c93e13a30d793f7645a0e7762c55d169dbb0a49671918a2319d289b10bb"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e48a53791691ab099e5e2ad123536d0fff50652600abaf43bbf952894110d0be"
dependencies = [
 "windows-core 0.52.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12342cb4d8e3b046f3d80effd474a7a02447231330ef77d71daa6fbc40681143"
dependencies = [
 "windows-core 0.57.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd04d41d93c4992d421894c18c8b43496aa748dd4c081bac0dc93eb0489272b6"
dependencies = [
 "windows-core 0.58.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-core"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33ab640c8d7e35bf8ba19b884ba838ceb4fba93a4e8c65a9059d08afcfc683d9"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-core"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2ed2439a290666cd67ecce2b0ffaad89c2a56b976b736e6ece670297897832d"
dependencies = [
 "windows-implement 0.57.0",
 "windows-interface 0.57.0",
 "windows-result 0.1.2",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-core"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ba6d44ec8c2591c134257ce647b7ea6b20335bf6379a27dac5f1641fcf59f99"
dependencies = [
 "windows-implement 0.58.0",
 "windows-interface 0.58.0",
 "windows-result 0.2.0",
 "windows-strings",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-implement"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9107ddc059d5b6fbfbffdfa7a7fe3e22a226def0b2608f72e9d552763d3e1ad7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "windows-implement"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bbd5b46c938e506ecbce286b6628a02171d56153ba733b6c741fc627ec9579b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "windows-interface"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29bee4b38ea3cde66011baa44dba677c432a78593e202392d1e9070cf2a7fca7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "windows-interface"
version = "0.58.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "053c4c462dc91d3b1504c6fe5a726dd15e216ba718e84a0e46a88fbe5ded3515"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "windows-registry"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e400001bb720a623c1c69032f8e3e4cf09984deec740f007dd2b03ec864804b0"
dependencies = [
 "windows-result 0.2.0",
 "windows-strings",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-result"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e383302e8ec8515204254685643de10811af0ed97ea37210dc26fb0032647f8"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-result"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d1043d8214f791817bab27572aaa8af63732e11bf84aa21a45a78d6c317ae0e"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-strings"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd9b125c486025df0eabcb585e62173c6c9eddcec5d117d3b6e8c30e2ee4d10"
dependencies = [
 "windows-result 0.2.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75283be5efb2831d37ea142365f009c02ec203cd29a3ebecbc093d52315b66d0"
dependencies = [
 "windows-targets 0.42.2",
]

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.59.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e38bc4d79ed67fd075bcc251a1c39b32a1776bbe92e5bef1f0bf1f8c531853b"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071"
dependencies = [
 "windows_aarch64_gnullvm 0.42.2",
 "windows_aarch64_msvc 0.42.2",
 "windows_i686_gnu 0.42.2",
 "windows_i686_msvc 0.42.2",
 "windows_x86_64_gnu 0.42.2",
 "windows_x86_64_gnullvm 0.42.2",
 "windows_x86_64_msvc 0.42.2",
]

[[package]]
name = "windows-targets"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c"
dependencies = [
 "windows_aarch64_gnullvm 0.48.5",
 "windows_aarch64_msvc 0.48.5",
 "windows_i686_gnu 0.48.5",
 "windows_i686_msvc 0.48.5",
 "windows_x86_64_gnu 0.48.5",
 "windows_x86_64_gnullvm 0.48.5",
 "windows_x86_64_msvc 0.48.5",
]

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm 0.52.6",
 "windows_aarch64_msvc 0.52.6",
 "windows_i686_gnu 0.52.6",
 "windows_i686_gnullvm",
 "windows_i686_msvc 0.52.6",
 "windows_x86_64_gnu 0.52.6",
 "windows_x86_64_gnullvm 0.52.6",
 "windows_x86_64_msvc 0.52.6",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b38e32f0abccf9987a4e3079dfb67dcd799fb61361e53e2882c3cbaf0d905d8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc35310971f3b2dbbf3f0690a219f40e2d9afcf64f9ab7cc1be722937c26b4bc"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_i686_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f"

[[package]]
name = "windows_i686_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75915e7def60c94dcef72200b9a8e58e5091744960da64ec734a6c6e9b3743e"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060"

[[package]]
name = "windows_i686_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f55c233f70c4b27f66c523580f78f1004e8b5a8b659e05a4eb49d4166cca406"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d40abd2583d23e4718fddf1ebec84dbff8381c07cae67ff7768bbf19c6718e"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b52767868a23d5bab768e390dc5f5c55825b6d30b86c844ff2dc7414044cc"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed94fce61571a4006852b7389a063ab983c02eb1bb37b47f8272ce92d06d9538"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"

[[package]]
name = "winnow"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e7f4ea97f6f78012141bcdb6a216b2609f0979ada50b20ca5b52dde2eac2bb1"
dependencies = [
 "memchr",
]

[[package]]
name = "winreg"
version = "0.50.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "524e57b2c537c0f9b1e69f1965311ec12182b4122e45035b1508cd24d2adadb1"
dependencies = [
 "cfg-if",
 "windows-sys 0.48.0",
]

[[package]]
name = "wit-bindgen-rt"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f42320e61fe2cfd34354ecb597f86f413484a798ba44a8ca1165c58d42da6c1"
dependencies = [
 "bitflags 2.8.0",
]

[[package]]
name = "write16"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1890f4022759daae28ed4fe62859b1236caebfc61ede2f63ed4e695f3f6d936"

[[package]]
name = "writeable"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e9df38ee2d2c3c5948ea468a8406ff0db0b29ae1ffde1bcf20ef305bcc95c51"

[[package]]
name = "wyz"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f360fc0b24296329c78fda852a1e9ae82de9cf7b27dae4b7f62f118f77b9ed"
dependencies = [
 "tap",
]

[[package]]
name = "xattr"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e105d177a3871454f754b33bb0ee637ecaaac997446375fd3e5d43a2ed00c909"
dependencies = [
 "libc",
 "linux-raw-sys 0.4.15",
 "rustix 0.38.44",
]

[[package]]
name = "xz"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c887690ff2a2e233e8e49633461521f98ec57fbff9d59a884c9a4f04ec1da34"
dependencies = [
 "xz2",
]

[[package]]
name = "xz2"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388c44dc09d76f1536602ead6d325eb532f5c122f17782bd57fb47baeeb767e2"
dependencies = [
 "lzma-sys",
]

[[package]]
name = "yaml-rust2"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a1a1c0bc9823338a3bdf8c61f994f23ac004c6fa32c08cd152984499b445e8d"
dependencies = [
 "arraydeque",
 "encoding_rs",
 "hashlink 0.9.1",
]

[[package]]
name = "yoke"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "120e6aef9aa629e3d4f52dc8cc43a015c7724194c97dfaf45180d2daf2b77f40"
dependencies = [
 "serde",
 "stable_deref_trait",
 "yoke-derive",
 "zerofrom",
]

[[package]]
name = "yoke-derive"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2380878cad4ac9aac1e2435f3eb4020e8374b5f13c296cb75b4620ff8e229154"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "synstructure",
]

[[package]]
name = "zerocopy"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b9b4fd18abc82b8136838da5d50bae7bdea537c574d8dc1a34ed098d6c166f0"
dependencies = [
 "byteorder",
 "zerocopy-derive 0.7.35",
]

[[package]]
name = "zerocopy"
version = "0.8.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd97444d05a4328b90e75e503a34bad781f14e28a823ad3557f0750df1ebcbc6"
dependencies = [
 "zerocopy-derive 0.8.23",
]

[[package]]
name = "zerocopy-derive"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa4f8080344d4671fb4e831a13ad1e68092748387dfc4f55e356242fae12ce3e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "zerocopy-derive"
version = "0.8.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6352c01d0edd5db859a63e2605f4ea3183ddbd15e2c4a9e7d32184df75e4f154"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "zerofrom"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cff3ee08c995dee1859d998dea82f7374f2826091dd9cd47def953cae446cd2e"
dependencies = [
 "zerofrom-derive",
]

[[package]]
name = "zerofrom-derive"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "595eed982f7d355beb85837f651fa22e90b3c044842dc7f2c2842c086f295808"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
 "synstructure",
]

[[package]]
name = "zeroize"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ced3678a2879b30306d323f4542626697a464a97c0a07c9aebf7ebca65cd4dde"
dependencies = [
 "zeroize_derive",
]

[[package]]
name = "zeroize_derive"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce36e65b0d2999d2aafac989fb249189a141aee1f53c612c1f37d72631959f69"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "zerovec"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa2b893d79df23bfb12d5461018d408ea19dfafe76c2c7ef6d4eba614f8ff079"
dependencies = [
 "yoke",
 "zerofrom",
 "zerovec-derive",
]

[[package]]
name = "zerovec-derive"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6eafa6dfb17584ea3e2bd6e76e0cc15ad7af12b09abdd1ca55961bed9b1063c6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.98",
]

[[package]]
name = "zstd"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e91ee311a569c327171651566e07972200e76fcfe2242a4fa446149a3881c08a"
dependencies = [
 "zstd-safe",
]

[[package]]
name = "zstd-safe"
version = "7.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3051792fbdc2e1e143244dc28c60f73d8470e93f3f9cbd0ead44da5ed802722"
dependencies = [
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.14+zstd.1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fb060d4926e4ac3a3ad15d864e99ceb5f343c6b34f5bd6d81ae6ed417311be5"
dependencies = [
 "cc",
 "pkg-config",
]
