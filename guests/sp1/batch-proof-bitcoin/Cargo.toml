[workspace]

[package]
name = "sp1-batch-prover-bitcoin"
version = "0.7.2"
edition = "2021"
resolver = "2"

[dependencies]
bitcoin-da = { path = "../../../crates/bitcoin-da", default-features = false }
citrea-primitives = { path = "../../../crates/primitives" }
citrea-sp1 = { path = "../../../crates/sp1", default-features = false }
citrea-stf = { path = "../../../crates/citrea-stf" }
const-hex = "1.12"
sov-modules-api = { path = "../../../crates/sovereign-sdk/module-system/sov-modules-api", default-features = false }
sov-modules-stf-blueprint = { path = "../../../crates/sovereign-sdk/module-system/sov-modules-stf-blueprint" }
sov-rollup-interface = { path = "../../../crates/sovereign-sdk/rollup-interface" }
sov-state = { path = "../../../crates/sovereign-sdk/module-system/sov-state" }
sp1-zkvm = { version = "3.0.0", default-features = false, features = ["lib"] }

# Have to put this here to enable features for the patch crate even though we don't use this crate explicitly
curve25519-dalek = { version = "4.1.3", default-features = true }

# Not all patches are needed as those versions are not used in our repo,
# but they are kept commented out to not deal with searching them if we ever need them
[patch.crates-io]
# sha2-v0-9-8 = { git = "https://github.com/sp1-patches/RustCrypto-hashes", package = "sha2", branch = "patch-sha2-v0.9.8" }
# sha2-v0-10-6 = { git = "https://github.com/sp1-patches/RustCrypto-hashes", package = "sha2", branch = "patch-sha2-v0.10.6" }
sha2-v0-10-8 = { git = "https://github.com/sp1-patches/RustCrypto-hashes", package = "sha2", branch = "patch-sha2-v0.10.8" }
# sha3-v0-9-8 = { git = "https://github.com/sp1-patches/RustCrypto-hashes", package = "sha3", branch = "patch-sha3-v0.9.8" }
# sha3-v0-10-6 = { git = "https://github.com/sp1-patches/RustCrypto-hashes", package = "sha3", branch = "patch-sha3-v0.10.6" }
sha3-v0-10-8 = { git = "https://github.com/sp1-patches/RustCrypto-hashes", package = "sha3", branch = "patch-sha3-v0.10.8" }
crypto-bigint = { git = "https://github.com/sp1-patches/RustCrypto-bigint", branch = "patch-v0.5.5" }
tiny-keccak = { git = "https://github.com/sp1-patches/tiny-keccak", branch = "patch-v2.0.2" }
curve25519-dalek = { git = "https://github.com/sp1-patches/curve25519-dalek", branch = "patch-curve25519-v4.1.3" }
# curve25519-dalek-ng = { git = "https://github.com/sp1-patches/curve25519-dalek-ng", branch = "patch-v4.1.1" }
# ed25519-consensus = { git = "https://github.com/sp1-patches/ed25519-consensus", branch = "patch-v2.1.0" }
ecdsa-core = { git = "https://github.com/sp1-patches/signatures", package = "ecdsa", branch = "patch-ecdsa-v0.16.9" }
substrate-bn = { git = "https://github.com/sp1-patches/bn", branch = "patch-v0.6.0" }
bls12_381 = { git = "https://github.com/sp1-patches/bls12_381", branch = "patch-v0.8.0" }

[profile.release]
debug = 0
lto = true
opt-level = 3
codegen-units = 1
