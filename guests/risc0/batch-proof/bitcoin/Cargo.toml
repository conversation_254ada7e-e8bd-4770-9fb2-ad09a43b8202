[package]
name = "batch-proof-bitcoin"
version = "0.7.2"
edition = "2021"
resolver = "2"

[workspace]

[dependencies]
# automatically added by the unstable feature
# but we tend to forget to update this while updating risc0
# so putting here explicitly
risc0-bigint2 = "1.4.5"
risc0-zkvm = { version = "2.3.0", default-features = false, features = ["unstable"] }
risc0-zkvm-platform = { version = "2.0.3", features = ["heap-embedded-alloc"] }

anyhow = "1.0.95"
bitcoin-da = { path = "../../../../crates/bitcoin-da", default-features = false }
citrea-primitives = { path = "../../../../crates/primitives" }
citrea-risc0-adapter = { path = "../../../../crates/risc0" }
citrea-stf = { path = "../../../../crates/citrea-stf" }
const-hex = "1.14"
sov-modules-api = { path = "../../../../crates/sovereign-sdk/module-system/sov-modules-api", default-features = false }
sov-modules-stf-blueprint = { path = "../../../../crates/sovereign-sdk/module-system/sov-modules-stf-blueprint" }
sov-rollup-interface = { path = "../../../../crates/sovereign-sdk/rollup-interface" }
sov-state = { path = "../../../../crates/sovereign-sdk/module-system/sov-state" }

[features]
# sys-getenv enabled for network selection here, only used in tests
testing = ["citrea-primitives/testing", "risc0-zkvm-platform/sys-getenv"]

[patch.crates-io]
sha2 = { git = "https://github.com/risc0/RustCrypto-hashes", tag = "sha2-v0.10.8-risczero.0" }
crypto-bigint = { git = "https://github.com/risc0/RustCrypto-crypto-bigint", tag = "v0.5.5-risczero.0" }
secp256k1 = { git = "https://github.com/Sovereign-Labs/rust-secp256k1.git", branch = "risc0-compatible-0-29-0" }
tiny-keccak = { git = "https://github.com/risc0/tiny-keccak", tag = "tiny-keccak/v2.0.2-risczero.0" }
k256 = { git = "https://github.com/risc0/RustCrypto-elliptic-curves", tag = "k256/v0.13.4-risczero.1" }
p256 = { git = "https://github.com/risc0/RustCrypto-elliptic-curves", tag = "p256/v0.13.2-risczero.1" }
substrate-bn = { git = "https://github.com/risc0/paritytech-bn", tag = "v0.6.0-risczero.0" }
blst = { git = "https://github.com/risc0/blst", tag = "v0.3.14-risczero.2" }

[profile.release]
debug = 0
lto = true
opt-level = 3
codegen-units = 1
