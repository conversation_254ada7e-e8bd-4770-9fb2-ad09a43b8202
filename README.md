# Citrea

**The first rollup that enhances the capabilities of Bitcoin blockspace with zero-knowledge technology, now [live on Bitcoin Testnet](https://www.blog.citrea.xyz/citrea-testnet-live-on-bitcoin-testnet4)! 🎉🍊🍋**

![](resources/assets/banner.png)

> [!WARNING]
> Citrea Testnet is now launched on top of Bitcoin Testnet4. While many key features are completed, Citrea's proper integration with [Bitcoin Mainnet](https://github.com/bitcoin/bitcoin) and [Clementine](https://github.com/chainwayxyz/clementine) is still ongoing, and it is also going to be audited for the Mainnet. \
> \
> Follow our [website](https://citrea.xyz) & [social media accounts](https://twitter.com/citrea_xyz) for announcements regarding the next phases of Citrea. \
> \
> Citrea uses **BTC** as its native token. **There's no Citrea token**. Please beware of scams! \
> \
> Please also note that **BTC** and **cBTC** of Citrea Testnet do not carry any real value.

## What is Citrea?

Citrea is the first rollup that enhances the capabilities of Bitcoin blockspace with zero-knowledge technology, **making it possible to build everything on Bitcoin**.

Every transaction occurring on Citrea, is fully secured by zero-knowledge proofs and optimistically verified by Bitcoin via BitVM. The execution environment of Citrea is trustless with respect to Bitcoin and is accessible to all participants of the Bitcoin Network.

Citrea's vision is to build scalable infrastructure that advances Bitcoin into its next phase, the foundation for world's finance. Citrea represents **Bitcoin Security at Scale** with its execution shard that keeps the settlement and data availability on-chain, on-Bitcoin.

## FAQ

| Question                                         | Answer                                                                                                                      |
| ------------------------------------------------ | --------------------------------------------------------------------------------------------------------------------------- |
| How do I set up the development environment?     | [dev-setup.md](./docs/dev-setup.md)                                                                                         |
| How do I run Citrea for testing and development? | [run-dev.md](./docs/run-dev.md)                                                                                                     |
| Where can I read more about the architecture?    | Technical docs for the repo are in progress, in the meantime refer to [our official documentation.](https://docs.citrea.xyz) |
| How do I run a Citrea testnet node?              | [run-testnet.md](./docs/run-testnet.md) |

## Official Links

- [Website](https://citrea.xyz)
- [Docs](https://docs.citrea.xyz)
- [Blog](https://blog.citrea.xyz)
- [X](https://x.com/citrea_xyz)
- [Discord](https://discord.gg/citrea)

## Acknowledgments

- [Sovereign SDK](https://github.com/Sovereign-Labs/sovereign-sdk): Citrea is built on a forked version of the Sovereign SDK. We're deeply thankful to the development & support of Sovereign Labs through our journey.
- [Reth](https://github.com/paradigmxyz/reth): We use Reth crates in various components in Citrea. We're grateful for their rapid development & contribution to the Rust-Ethereum ecosystem.
