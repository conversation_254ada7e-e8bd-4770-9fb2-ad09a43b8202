[workspace]
resolver = "2"
members = [
  # Citrea
  "bin/citrea",
  "bin/cli",
  "crates/batch-prover",
  "crates/bitcoin-da",
  "crates/citrea-stf",
  "crates/common",
  "crates/ethereum-rpc",
  "crates/evm",
  "crates/fullnode",
  "crates/light-client-prover",
  "crates/primitives",
  "crates/prover-services",
  "crates/storage-ops",
  "crates/risc0",
  "crates/sequencer",
  "crates/short-header-proof-provider",
  "crates/l2-block-rule-enforcer",
  # "crates/sp1",
  # Sovereign sdk
  "crates/sovereign-sdk/rollup-interface",
  "crates/sovereign-sdk/adapters/mock-da",
  "crates/sovereign-sdk/adapters/mock-zkvm",
  # Full Node
  "crates/sovereign-sdk/full-node/db/sov-db",
  "crates/sovereign-sdk/full-node/sov-ledger-rpc",
  "crates/sovereign-sdk/full-node/sov-prover-storage-manager",
  # Module System
  "crates/sovereign-sdk/module-system/sov-modules-stf-blueprint",
  "crates/sovereign-sdk/module-system/sov-modules-rollup-blueprint",
  "crates/sovereign-sdk/module-system/sov-modules-macros",
  "crates/sovereign-sdk/module-system/sov-modules-core",
  "crates/sovereign-sdk/module-system/sov-state",
  "crates/sovereign-sdk/module-system/sov-keys",
  "crates/sovereign-sdk/module-system/sov-modules-api",
  "crates/sovereign-sdk/module-system/module-implementations/sov-accounts",
  "crates/sovereign-sdk/module-system/module-implementations/integration-tests",
]

[workspace.package]
version = "0.7.2"
edition = "2021"
license = "GPL-3.0"
authors = ["Chainway Labs <<EMAIL>>"]
homepage = "https://citrea.xyz"
publish = false
repository = "https://github.com/chainwayxyz/citrea"

[workspace.dependencies]
# External dependencies
async-trait = "0.1.83"
anyhow = { version = "1.0.95", default-features = false }
backoff = { version = "0.4.0", features = ["futures", "tokio"] }
base64 = "0.22"
borsh = { version = "1.5", default-features = false, features = ["derive"] }
# TODO: Consider replacing this serialization format
#     https://github.com/Sovereign-Labs/sovereign-sdk/issues/283
bincode = "1.3.3"
bitcoin = { version = "0.32.5", features = ["serde", "rand"] }
bitcoincore-rpc = "0.18.0"
bcs = "0.1.6"
bech32 = { version = "0.9.1", default-features = false }
brotli = "7"
byteorder = { version = "1.5.0", default-features = false }
bytes = { version = "1.10.0", default-features = false }
chrono = { version = "0.4.39", default-features = false }
clap = { version = "4.5.23", features = ["derive", "env"] }
const-hex = "1.14"
constmuck = "1.1"
crypto-bigint = "0.5.5"
digest = { version = "0.10.7", default-features = false, features = ["alloc"] }
derive_more = { version = "1", default-features = false }
faster-hex = "0.10.0"
futures = "0.3"
hyper = "1.6.0"
itertools = { version = "0.13.0", default-features = false }
jmt = { git = "https://github.com/penumbra-zone/jmt.git", rev = "550a2f2" }
jsonrpsee = { version = "0.24.8", features = ["jsonrpsee-types"] }
k256 = { version = "0.13.4" }
lru = "0.13"
hex = { version = "0.4.3", default-features = false, features = ["alloc", "serde"] }
log-panics = { version = "2", features = ["with-backtrace"] }
metrics = "0.23.0"
metrics-derive = "0.1.0"
metrics-exporter-prometheus = "0.15.3"
metrics-util = "0.17.0"
# 1.13 uses getrandom 0.3
uuid = { version = "=1.12.1", default-features = false, features = ["serde", "borsh", "v4", "v7"] }
parking_lot = "0.12.3"
proptest = { version = "1.6.0", default-features = false, features = ["alloc"] }
rand = "0.8"
rayon = "1.8.0"
rlimit = "0.10.2"
rustc_version_runtime = { version = "0.3.0", default-features = false }
rs_merkle = "1.4.2"
reqwest = { version = "0.12.12", features = ["rustls-tls", "json", "http2"], default-features = false }
rocksdb = { version = "0.22.0", features = ["lz4"], default-features = false }
serde = { version = "1", default-features = false, features = ["alloc", "derive"] }
serde_json = { version = "1", default-features = false, features = ["alloc"] }
sha2 = { version = "0.10.8", default-features = false, features = ["asm"] }
schemars = { version = "0.8.16", features = ["derive"] }
secp256k1 = { version = "0.30.0", default-features = false, features = ["global-context", "recovery"] }
thiserror = { version = "2", default-features = false }
tinyvec = { version = "1", default-features = false, features = ["alloc", "rustc_1_61", "rustc_1_57", "rustc_1_55"] }
tracing = { version = "0.1.41", default-features = false, features = ["attributes"] }
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "json", "fmt"] }
toml = "0.8.0"
tempfile = "3.8"
tokio = { version = "1.44.2", features = ["full"] }
tower-http = { version = "0.5.0", features = ["full"] }
tower = { version = "0.4.13", features = ["full"] }

# Risc0 dependencies
bonsai-sdk = { version = "1.4" }
risc0-binfmt = "2.0.2"
risc0-zkvm = { version = "2.3.0", default-features = false }
risc0-zkvm-platform = "2.0.3"
risc0-zkp = "2.0.2"
risc0-circuit-rv32im = "3.0.0"
risc0-build = "2.3.0"
rzup = "0.4.1"

# EVM dependencies
revm-inspectors = { version = "0.18.0", default-features = false }
reth-primitives = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-primitives-traits = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-chainspec = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-errors = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-execution-types = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-rpc-eth-types = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-rpc-eth-api = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-rpc-server-types = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-rpc-types-compat = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-node-api = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-transaction-pool = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-provider = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-tasks = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-db = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-trie = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-rpc = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }
reth-stages = { git = "https://github.com/paradigmxyz/reth", tag = "v1.3.7", default-features = false }

revm = { version = "21.0.0", features = ["serde"], default-features = false }
revm-precompile = { version = "18.0.0", default-features = false, features = ['secp256r1', 'blst', 'bn'] }
revm-primitives = { version = "17.0.0", default-features = false }
revm-bytecode = { version = "2.0.0", default-features = false }
alloy-genesis = { version = "0.13", default-features = false }
alloy-trie = { version = "0.7.9", default-features = false }
alloy-rlp = { version = "0.3.10", default-features = false }
alloy-rpc-types = { version = "0.13", features = ["eth"], default-features = false }
alloy-rpc-types-eth = { version = "0.13", default-features = false }
alloy-rpc-types-trace = { version = "0.13", default-features = false }
alloy-primitives = { version = "0.8.25", default-features = false, features = ["rand", "serde", "tiny-keccak", "k256"] }
alloy-serde = { version = "0.13", default-features = false }
alloy-sol-types = { version = "0.8.25", default-features = false, features = ["json"] }
alloy = { version = "0.13", default-features = false }
alloy-eips = { version = "0.13", default-features = false }
alloy-consensus = { version = "0.13", default-features = false, features = ["serde", "serde-bincode-compat"] }
alloy-network = { version = "0.13", default-features = false }

citrea-e2e = { git = "https://github.com/chainwayxyz/citrea-e2e", rev = "859cddf" }

[patch.crates-io]
bitcoincore-rpc = { version = "0.18.0", git = "https://github.com/chainwayxyz/rust-bitcoincore-rpc.git", rev = "0fe8a8b" }

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
debug = 0
