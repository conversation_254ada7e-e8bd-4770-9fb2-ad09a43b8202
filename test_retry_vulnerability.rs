use std::time::Duration;

/// Demonstrates the retry loop vulnerability in process_missed_da_blocks
fn main() {
    println!("Testing sequencer retry loop vulnerability");

    let mut attempt_count = 0;
    let start_time = std::time::Instant::now();
    let max_elapsed = Duration::from_secs(10);
    let mut current_interval = Duration::from_millis(200);
    let multiplier = 1.5;

    println!("Starting retry loop simulation...");

    // Simulate the vulnerable retry logic from process_missed_da_blocks
    loop {
        attempt_count += 1;
        let elapsed = start_time.elapsed();

        println!("Retry attempt #{} at {:.2}s", attempt_count, elapsed.as_secs_f64());

        // Simulate malicious Bitcoin node returning invalid data
        // In real attack, this would be malformed JSON, invalid block data, etc.
        println!("  -> Malicious Bitcoin node returns invalid block data");

        // Check if max elapsed time reached
        if elapsed >= max_elapsed {
            println!("  -> Max elapsed time reached, but in real code this function would be called again!");
            break;
        }

        // Exponential backoff delay
        std::thread::sleep(current_interval);
        current_interval = Duration::from_millis(
            (current_interval.as_millis() as f64 * multiplier) as u64
        );

        // Limit simulation to prevent infinite loop in demo
        if attempt_count >= 8 {
            break;
        }
    }

    let total_time = start_time.elapsed();
    println!("\nVULNERABILITY DEMONSTRATED:");
    println!("- Retry failed after {} attempts in {:.2}s", attempt_count, total_time.as_secs_f64());
    println!("- In real attack, process_missed_da_blocks would be called again");
    println!("- This creates an infinite loop as the function restarts the retry cycle");
    println!("- Sequencer gets permanently stuck and cannot produce L2 blocks");
    
    println!("\nKey vulnerability points:");
    println!("1. All errors are treated as transient in the retry logic");
    println!("2. Max elapsed time limit can be bypassed by restarting the function");
    println!("3. No global maximum attempt limit exists");
    println!("4. Single malicious Bitcoin node can halt entire L2 network");
}
