name: release

on:
  push:
    tags:
      - "v*.*.*"

jobs:
  linux_amd64_binary_extraction:
    runs-on: ubicloud-standard-30
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Dependencies
        run: |
          sudo apt update && sudo apt -y install curl gcc cpp cmake clang llvm
          sudo apt -y autoremove && sudo apt clean && sudo rm -rf /var/lib/apt/lists/*

      - name: Install Rust
        run: |
          curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
          rustup install 1.85.0
          rustup default 1.85.0

      - name: Install risc0
        uses: ./.github/actions/install-risc0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Project
        env:
          SKIP_GUEST_BUILD: 1
        run: |
          cargo build --release

      - name: Upload citrea linux-amd64 Binary
        uses: actions/upload-artifact@v4
        with:
          name: citrea-${{ github.ref_name }}-linux-amd64
          path: target/release/citrea

      - name: Upload citrea-cli linux-amd64 Binary
        uses: actions/upload-artifact@v4
        with:
          name: citrea-cli-${{ github.ref_name }}-linux-amd64
          path: target/release/citrea-cli

  osx_arm64_binary_extraction:
    runs-on: self-hosted-citrea-osx-arm64
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Rust
        run: |
          curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
          rustup install 1.85.0
          rustup default 1.85.0

      - name: Install risc0
        uses: ./.github/actions/install-risc0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Build Project
        env:
          SKIP_GUEST_BUILD: 1
        run: |
          source $HOME/.cargo/env
          cargo build --release

      - name: Upload citrea osx-arm64 Binary
        uses: actions/upload-artifact@v4
        with:
          name: citrea-${{ github.ref_name }}-osx-arm64
          path: target/release/citrea

      - name: Upload citrea-cli osx-arm64 Binary
        uses: actions/upload-artifact@v4
        with:
          name: citrea-cli-${{ github.ref_name }}-osx-arm64
          path: target/release/citrea-cli

  release:
    needs: [ linux_amd64_binary_extraction, osx_arm64_binary_extraction ]
    runs-on: ubuntu-latest
    steps:
      - name: Download linux-amd64 Binary
        uses: actions/download-artifact@v4
        with:
          name: citrea-${{ github.ref_name }}-linux-amd64
          path: release

      - name: rename file
        run: |
          mv release/citrea release/citrea-${{ github.ref_name }}-linux-amd64

      - name: Download OSX ARM64 Binary
        uses: actions/download-artifact@v4
        with:
          name: citrea-${{ github.ref_name }}-osx-arm64
          path: release

      - name: rename file
        run: |
            mv release/citrea release/citrea-${{ github.ref_name }}-osx-arm64

      - name: Download citrea-cli linux-amd64 Binary
        uses: actions/download-artifact@v4
        with:
          name: citrea-cli-${{ github.ref_name }}-linux-amd64
          path: release


      - name: rename file
        run: |
          mv release/citrea-cli release/citrea-cli-${{ github.ref_name }}-linux-amd64

      - name: Download citrea-cli osx-arm64 Binary
        uses: actions/download-artifact@v4
        with:
          name: citrea-cli-${{ github.ref_name }}-osx-arm64
          path: release

      - name: rename file
        run: |
          mv release/citrea-cli release/citrea-cli-${{ github.ref_name }}-osx-arm64

      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          files: |
            release/citrea-${{ github.ref_name }}-osx-arm64
            release/citrea-${{ github.ref_name }}-linux-amd64
            release/citrea-cli-${{ github.ref_name }}-linux-amd64
            release/citrea-cli-${{ github.ref_name }}-osx-arm64
          name: Release ${{ github.ref_name }}
          body: |
            This is the release for version ${{ github.ref_name }}.

            It includes:
            - citrea-${{ github.ref_name }}-linux-amd64
            - citrea-${{ github.ref_name }}-osx-arm64
            - citrea-cli-${{ github.ref_name }}-linux-amd64
            - citrea-cli-${{ github.ref_name }}-osx-arm64

  update-dockerhub:
    runs-on: ubuntu-latest
    needs: [ release ]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download Citrea binary and Move required resources
        run: |
          sudo apt update
          sudo apt -y install wget
          RELEASE="${{ github.ref_name }}"

          BINARY_URL="https://github.com/chainwayxyz/citrea/releases/download/${RELEASE}/citrea-${RELEASE}-linux-amd64"
          echo "Downloading binary from ${BINARY_URL}"
          # Download the binary using wget
          wget -O docker/build-push/testnet/citrea "${BINARY_URL}"

          # Make the downloaded file executable
          chmod +x docker/build-push/testnet/citrea

          mkdir -p docker/build-push/testnet/genesis/
          mv resources/genesis/testnet/* docker/build-push/testnet/genesis/

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Registry
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build Linux image
        uses: docker/build-push-action@v5
        env:
          DOCKERHUB_REPOSITORY: citrea-full-node
          IMAGE_TAG: testnet
        with:
          context: docker/build-push/testnet/
          file: docker/build-push/testnet/Dockerfile
          platforms: linux/amd64
          push: false
          load: true
          provenance: false
          tags: |
            ${{ vars.DOCKERHUB_USERNAME }}/${{ env.DOCKERHUB_REPOSITORY }}:${{ env.IMAGE_TAG }}

      - name: Tag  and push image to Dockerhub
        env:
          DOCKERHUB_REPOSITORY: citrea-full-node
          GENERAL_IMAGE_TAG: testnet
          VERSION_IMAGE_TAG: ${{ github.ref_name }}
        run: |
          docker tag ${{ vars.DOCKERHUB_USERNAME }}/${{ env.DOCKERHUB_REPOSITORY }}:${{ env.GENERAL_IMAGE_TAG }} ${{ vars.DOCKERHUB_USERNAME }}/${{ env.DOCKERHUB_REPOSITORY }}:${{ env.VERSION_IMAGE_TAG }}

          docker push ${{ vars.DOCKERHUB_USERNAME }}/${{ env.DOCKERHUB_REPOSITORY }}:${{ env.VERSION_IMAGE_TAG }}
          docker push ${{ vars.DOCKERHUB_USERNAME }}/${{ env.DOCKERHUB_REPOSITORY }}:${{ env.GENERAL_IMAGE_TAG }}

