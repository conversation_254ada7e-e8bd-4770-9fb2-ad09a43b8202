# Makes a Docker build to be used in Hive tests.

name: Docker for Hive

on:
  workflow_dispatch:
  schedule:
    # every day
    - cron: "13 21 * * *" # 21:13 UTC

env:
  CARGO_TERM_COLOR: always

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  # Except in `nightly` and `stable` branches! Any cancelled job will cause the
  # CI run to fail, and we want to keep a clean history for major branches.
  cancel-in-progress: ${{ (github.ref != 'refs/heads/nightly') && (github.ref != 'refs/heads/devnet-freeze') && (github.ref != 'refs/heads/main')}}

jobs:
  docker:
    timeout-minutes: 120
    name: Build and publish Docker image
    runs-on: ubicloud-standard-16
    steps:
      - uses: actions/checkout@v4
      - name: Docker Setup Buildx
        uses: docker/setup-buildx-action@v3.2.0
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v5.3.0
        with:
          context: .
          file: ./resources/hive/Dockerfile
          push: true
          tags: ${{ vars.DOCKERHUB_USERNAME }}/citrea:latest
