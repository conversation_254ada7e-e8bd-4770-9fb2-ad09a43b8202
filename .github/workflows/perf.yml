name: Performance Comparison

on:
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always
  FOUNDRY_PROFILE: ci
  TARGET_PCT: 3
  COMPARISON_FILE: comparison_results.log
  USE_DOCKER: "true"

jobs:
  performance-comparison:
    runs-on: ubicloud-standard-16
    steps:
      - uses: actions/checkout@v4
      - name: Fetch latest nightly
        run: |
          git fetch origin nightly:nightly
      - uses: rui314/setup-mold@v1
      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          version: "23.2"
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Toolchain
        uses: dtolnay/rust-toolchain@1.85.0
        with:
          override: true
          components: rustfmt, clippy
      - name: Rust Cache
        uses: ubicloud/rust-cache@v2
      - name: Install risc0
        uses: ./.github/actions/install-risc0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Cache ethereum-tests
        uses: actions/cache@v4
        with:
          key: "eth-tests-1c23e3c"
          path: crates/evm/ethereum-tests

      - name: Run Performance Comparison
        run: |
          chmod +x ./resources/scripts/cycle-diff.sh
          ./resources/scripts/cycle-diff.sh generate

      - name: Check Performance Regression
        run: |
          ./resources/scripts/cycle-diff.sh check

      - name: Upload comparison results
        uses: actions/upload-artifact@v4
        with:
          name: comparison-results
          path: comparison_results.log
