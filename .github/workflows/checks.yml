name: Checks

# On Rust, GitHub Actions, and caching
# ===========
# Here's a list of things to keep in mind if you find yourself maintaining this
# CI:
#
# https://docs.github.com/en/actions/using-workflows/caching-dependencies-to-speed-up-workflows#matching-a-cache-key
#
# - Always install and select the desired Rust toolchain *before* running
#   `Swatinem/rust-cache`. This is because the active Rust toolchain is used as
#   a cache key.
# - You can use `rustup show` to install and select the right Rust toolchain if
#   you have a `rust-toolchain.toml` file:
#   https://github.com/rust-lang/rustup/issues/1397.
# - When caching Rust compilation artifacts, keep in mind that different `cargo`
#   commands will use different profiles
#   (https://doc.rust-lang.org/cargo/reference/profiles.html). Learn what you
#   can reuse between one job and another and don't assume two commands will
#   just share caches without conflicts.
# - Be extremely aware of cache thrashing a.k.a. churning. GitHub Actions' cache
#   allows for 10GiB of data which is easily exceeded if not careful.
#   Sometimes it's better not to cache than cache excessively.
#   Disabling cache writes for non-default branches altogether if cache churning
#   is unacceptably high is supposed to help with this.
# - Learn cache invalidation rules of `Swatinem/rust-cache` before making
#   changes, e.g. what happens when `rustc --version` changes or `Cargo.lock`
#   changes (or is missing).
# - The jobs dependency tree is the way it is to accommodate for sharing caches,
#   not necessarily because it makes logical sense to run one job after the
#   other. This is due to the fact that we can't share caches between jobs that
#   run in parallel.
# - `sccache` is a good alternative to `Swatinem/rust-cache`, but it behaves
#   poorly with GHA and often incurs into cache requests rate limits. We should
#   probably explore `sccache` with a different backend.
# - If a job makes good use of extra cores, consider give it a bigger machine.
#   GHA larger runners increase in cost linearly with the number of cores
#   (https://docs.github.com/en/billing/managing-billing-for-github-actions/about-billing-for-github-actions),
#   so you're not wasting money unless several cores are sitting idle for long.

on:
  workflow_dispatch:
    inputs:
      custom_name:
        description: 'Custom run name (optional)'
        required: false
        type: string
      RUST_LOG:
        description: 'Set RUST_LOG level (optional)'
        required: false
        type: string
  merge_group:
    types: ["checks_requested"]
  push:
    branches: ["nightly", "devnet-freeze", "release-v*"]
  pull_request:
    branches: ["nightly", "devnet-freeze", "release-v*"]
    types: [opened, synchronize, reopened, ready_for_review]

run-name: ${{ inputs.custom_name || github.event.pull_request.title || github.sha }}

env:
  CARGO_TERM_COLOR: always
  RUSTFLAGS: -D warnings
  FOUNDRY_PROFILE: ci
  TEST_BITCOIN_DOCKER: "bitcoin/bitcoin:29.0"

# Automatically cancels a job if a new commit if pushed to the same PR, branch, or tag.
# Source: <https://stackoverflow.com/a/72408109/5148606>
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  # Except in `nightly` and `stable` branches! Any cancelled job will cause the
  # CI run to fail, and we want to keep a clean history for major branches.
  cancel-in-progress: ${{ (github.ref != 'refs/heads/nightly') && (github.ref != 'refs/heads/devnet-freeze') && (github.ref != 'refs/heads/main') }}

jobs:
  build:
    name: build
    runs-on: ubicloud-standard-30
    timeout-minutes: 60
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - uses: rui314/setup-mold@v1
      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          version: "23.2"
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Toolchain
        uses: dtolnay/rust-toolchain@nightly
      - name: Rust Cache
        uses: ubicloud/rust-cache@v2
      - name: Install risc0
        uses: ./.github/actions/install-risc0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Cache ethereum-tests
        uses: actions/cache@v4
        with:
          key: "eth-tests-1c23e3c"
          path: crates/evm/ethereum-tests
      - name: Build citrea
        run: make build
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: citrea-build
          path: |
            target/debug/citrea
            target/debug/citrea-cli
            target/debug/**/methods.rs
          retention-days: 1

  check:
    name: check
    runs-on: ubicloud-standard-4
    timeout-minutes: 60
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - uses: rui314/setup-mold@v1
      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          version: "23.2"
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Toolchain
        uses: dtolnay/rust-toolchain@nightly
        with:
          components: rustfmt, clippy
      - name: Install risc0
        uses: ./.github/actions/install-risc0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Check TOML
        uses: dprint/check@v2.2
      - name: Build guests
        run: make build-risc0
      - name: Run lint
        run: |
          if ! make lint ; then
            echo "Linting or formatting errors detected, please run 'make lint-fix' to fix it";
            exit 1
          fi
        env:
          SKIP_GUEST_BUILD: 1

  udeps:
    name: udeps
    runs-on: ubicloud-standard-8
    timeout-minutes: 60
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - uses: rui314/setup-mold@v1
      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          version: "23.2"
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Install nightly toolchain
        uses: actions-rs/toolchain@v1
        with:
          toolchain: nightly
          override: true
      - name: Install risc0
        uses: ./.github/actions/install-risc0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Build guests
        run: make build-risc0
      - name: Run cargo-udeps
        env:
          RUSTFLAGS: -A warnings
          SKIP_GUEST_BUILD: 1
        uses: aig787/cargo-udeps-action@v1
        with:
          version: "latest"
          args: "--workspace --all-features --all-targets"

  deny:
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - name: Run cargo-deny
        uses: EmbarkStudios/cargo-deny-action@v2
        with:
          command: check

  coverage:
    needs:
      - docker-setup
    runs-on: ubicloud-standard-30
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@1.85.0
        with:
          components: llvm-tools-preview
      - name: Rust Cache
        uses: ubicloud/rust-cache@v2
      - name: Setup env
        if: ${{ github.event.inputs.RUST_LOG != '' }}
        run: echo "RUST_LOG=${{ github.event.inputs.RUST_LOG }}" >> $GITHUB_ENV
      - uses: taiki-e/install-action@nextest
      - uses: taiki-e/install-action@cargo-llvm-cov
      - name: Install risc0
        uses: ./.github/actions/install-risc0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Cache ethereum-tests
        uses: actions/cache@v4
        with:
          key: "eth-tests-1c23e3c"
          path: crates/evm/ethereum-tests
      - name: Restore cached Docker image
        uses: actions/cache@v4
        with:
          path: /tmp/docker
          key: ${{ runner.os }}-docker-${{ env.TEST_BITCOIN_DOCKER }}
      - name: Load Docker image
        continue-on-error: true
        run: docker load < /tmp/docker/bitcoin.tar
      - name: Run coverage
        run: make coverage
        env:
          RUST_BACKTRACE: 1
          TEST_BITCOIN_DOCKER: 1
          RISC0_DEV_MODE: 1 # This is needed to generate mock proofs and verify them
          PARALLEL_PROOF_LIMIT: 1
          TEST_OUT_DIR: ${{ runner.temp }}/coverage
          CITREA_E2E_TEST_BINARY: ${{ github.workspace }}/target/llvm-cov-target/debug/citrea
          CITREA_CLI_E2E_TEST_BINARY: ${{ github.workspace }}/target/llvm-cov-target/debug/citrea-cli
      - name: Upload e2e test dir
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-dir-coverage
          path: ${{ runner.temp }}/coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v4
        with:
          fail_ci_if_error: true # optional (default = false)
          files: ./lcov.info
          token: ${{ secrets.CODECOV_TOKEN }}

  uniswap:
    runs-on: ubicloud-standard-2
    needs: build
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: citrea-build
          path: target/debug
      - name: Make citrea executable
        run: chmod +x target/debug/citrea
      - name: Install node dependencies
        working-directory: ./bin/citrea/tests/mock/evm/uniswap
        run: npm install
      - name: Run uniswap tests
        run: |
          RUST_LOG=off ./target/debug/citrea --da-layer mock --rollup-config-path resources/configs/mock/sequencer_rollup_config.toml --sequencer resources/configs/mock/sequencer_config.toml --genesis-paths resources/genesis/mock/ --dev &
          sleep 2
          RUST_LOG=off ./target/debug/citrea --rollup-config-path resources/configs/mock/rollup_config.toml --genesis-paths resources/genesis/mock/ --dev &
          sleep 2
          cd ./bin/citrea/tests/mock/evm/uniswap
          npx hardhat run --network citrea scripts/01_deploy.js
          npx hardhat run --network citrea scripts/02_swap.js
          seqnHeight=$(curl -s 0.0.0.0:12345/ -H "Content-Type: application/json" --data '{"method":"eth_getBlockByNumber","params":["latest"],"id":1,"jsonrpc":"2.0"}' | jq -e .result.number) || (echo "Couldn't get sequencer block"; exit 1)
          nodeHeight=$(curl -s 0.0.0.0:12346/ -H "Content-Type: application/json" --data '{"method":"eth_getBlockByNumber","params":["latest"],"id":1,"jsonrpc":"2.0"}' | jq -e .result.number) || (echo "Couldn't get full node block"; exit 1)
          echo seqnHeight: $seqnHeight
          echo nodeHeight: $nodeHeight
          sleep 10
          seqnRoot=$(curl -s 0.0.0.0:12345/ -H "Content-Type: application/json" --data '{"method":"eth_getBlockByNumber","params":['${seqnHeight}'],"id":1,"jsonrpc":"2.0"}' | jq -e .result.stateRoot) || (echo "Couldn't get sequencer state root"; exit 1)
          nodeRoot=$(curl -s 0.0.0.0:12346/ -H "Content-Type: application/json" --data '{"method":"eth_getBlockByNumber","params":['${seqnHeight}'],"id":1,"jsonrpc":"2.0"}' | jq -e .result.stateRoot) || (echo "Couldn't get full node state root"; exit 1)
          echo seqnRoot: $seqnRoot
          echo nodeRoot: $nodeRoot
          if [ "$seqnRoot" != "$nodeRoot" ]; then
            echo "State root mismatch";
            exit 1
          fi

  web3_py:
    runs-on: ubicloud-standard-2
    needs: build
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.x"
      - uses: dcarbone/install-jq-action@v2
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: citrea-build
          path: target/debug
      - name: Make citrea executable
        run: chmod +x target/debug/citrea
      - name: Install dependencies
        working-directory: ./bin/citrea/tests/mock/evm/web3_py
        run: pip install -r requirements.txt
      - name: Run web3.py tests
        run: |
          RUST_LOG=off ./target/debug/citrea --da-layer mock --rollup-config-path resources/configs/mock/sequencer_rollup_config.toml --sequencer resources/configs/mock/sequencer_config.toml --genesis-paths resources/genesis/mock/ --dev &
          sleep 2
          RUST_LOG=off ./target/debug/citrea --da-layer mock --rollup-config-path resources/configs/mock/rollup_config.toml --genesis-paths resources/genesis/mock/ --dev &
          sleep 2
          cd ./bin/citrea/tests/mock/evm/web3_py
          python test.py

  ethers_js:
    runs-on: ubicloud-standard-2
    needs: build
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: citrea-build
          path: target/debug
      - name: Make citrea executable
        run: chmod +x target/debug/citrea
      - name: Install node dependencies
        working-directory: ./bin/citrea/tests/mock/evm/ethers_js
        run: npm install
      - name: Run ethers_js tests
        run: |
          RUST_LOG=off ./target/debug/citrea --da-layer mock --rollup-config-path resources/configs/mock/sequencer_rollup_config.toml --sequencer resources/configs/mock/sequencer_config.toml --genesis-paths resources/genesis/mock/ --dev &
          sleep 2
          RUST_LOG=off ./target/debug/citrea --da-layer mock --rollup-config-path resources/configs/mock/rollup_config.toml --genesis-paths resources/genesis/mock/ --dev &
          sleep 2
          cd ./bin/citrea/tests/mock/evm/ethers_js
          npm install
          npx mocha test.js
          cd ../../../../..

  nextest:
    needs:
      - docker-setup
    name: nextest
    runs-on: ubicloud-standard-30
    timeout-minutes: 60
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
      - uses: rui314/setup-mold@v1
      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          version: "23.2"
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Toolchain
        uses: dtolnay/rust-toolchain@1.85.0
      - name: Rust Cache
        uses: ubicloud/rust-cache@v2
      - name: Setup env
        if: ${{ github.event.inputs.RUST_LOG != '' }}
        run: echo "RUST_LOG=${{ github.event.inputs.RUST_LOG }}" >> $GITHUB_ENV
      - name: Install risc0
        uses: ./.github/actions/install-risc0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
        # `cargo-nextest` is much faster than standard `cargo test`.
      - uses: taiki-e/install-action@nextest
      - name: Cache ethereum-tests
        uses: actions/cache@v4
        with:
          key: "eth-tests-1c23e3c"
          path: crates/evm/ethereum-tests
      - name: Restore cached Docker image
        uses: actions/cache@v4
        with:
          path: /tmp/docker
          key: ${{ runner.os }}-docker-${{ env.TEST_BITCOIN_DOCKER }}
      - name: Load Docker image
        continue-on-error: true
        run: docker load < /tmp/docker/bitcoin.tar
      - name: Run nextest
        run: make test
        env:
          RUST_BACKTRACE: 1
          RISC0_DEV_MODE: 1 # This is needed to generate mock proofs and verify them
          TEST_BITCOIN_DOCKER: 1
          PARALLEL_PROOF_LIMIT: 1
          TEST_OUT_DIR: ${{ runner.temp }}/test
          CITREA_E2E_TEST_BINARY: ${{ github.workspace }}/target/debug/citrea
          CITREA_CLI_E2E_TEST_BINARY: ${{ github.workspace }}/target/debug/citrea-cli
      - name: Upload e2e test dir
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-dir-test
          path: ${{ runner.temp }}/test

  system-contracts:
    strategy:
      fail-fast: true
    name: Foundry project
    runs-on: ubicloud-standard-2
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          cache: false

      - name: Run Forge build
        run: |
          cd crates/evm/src/evm/system_contracts
          forge --version
          forge build --sizes
        id: build

      - name: Run Forge tests
        run: |
          cd crates/evm/src/evm/system_contracts
          forge test -vvv
        id: test
  check_genesis_files:
    strategy:
      fail-fast: true
    name: Check Genesis Files
    runs-on: ubicloud-standard-2
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1
        with:
          cache: false

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"

      - name: Run check_genesis.sh
        run: ./.github/scripts/check_genesis.sh
        shell: bash

  docker-setup:
    runs-on: ubicloud-standard-2
    if: github.event.pull_request.draft == false
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        continue-on-error: true
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Cache Docker images
        id: cache-docker
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: /tmp/docker
          key: ${{ runner.os }}-docker-${{ env.TEST_BITCOIN_DOCKER }}

      - name: Pull Docker image
        if: steps.cache-docker.outputs.cache-hit != 'true'
        continue-on-error: true
        run: |
          docker pull ${{ env.TEST_BITCOIN_DOCKER }}
          mkdir -p /tmp/docker
          docker save ${{ env.TEST_BITCOIN_DOCKER }} > /tmp/docker/bitcoin.tar

      - name: Load Docker image from cache
        if: steps.cache-docker.outputs.cache-hit == 'true'
        continue-on-error: true
        run: |
          docker load < /tmp/docker/bitcoin.tar

  codespell:
    name: codespell
    runs-on: ubicloud-standard-2
    if: github.event.pull_request.draft == false
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"

      - name: Install codespell
        run: pip install codespell

      - name: Run codespell
        run: |
          codespell --skip="*.lock,./target,./crates/evm/ethereum-tests," -I="codespell_ignore.txt"
