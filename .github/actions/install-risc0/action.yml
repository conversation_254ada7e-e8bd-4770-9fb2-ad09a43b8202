name: 'Install risc0'
description: 'Installs risc0 toolchain'

inputs:
  github_token:
    description: 'GitHub token for authentication'
    required: true

runs:
  using: "composite"
  steps:
    - name: Install risc0
      shell: bash
      env:
        GITHUB_TOKEN: ${{ inputs.github_token }}
      run: | 
        curl -L https://risczero.com/install | bash
        export PATH="$PATH:$HOME/.risc0/bin"
        rzup install cargo-risczero 2.3.0
        rzup install cpp
        rzup install r0vm 2.3.0
        rzup install rust 1.85.0
