# ref: https://docs.codecov.com/docs/codecovyml-reference
coverage:
  # Hold ourselves to a high bar
  range: 50..100
  round: down
  precision: 1
  status:
    # ref: https://docs.codecov.com/docs/commit-status
    project:
      default:
        # Avoid false negatives
        threshold: 1%
    patch: off

# Test files aren't important for coverage
ignore:
  - "tests"

# Make comments less noisy
comment:
  layout: "files"
  require_changes: true